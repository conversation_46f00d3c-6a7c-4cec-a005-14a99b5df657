@font-face {
  font-family: Overused Grotesk Woff;
  src: url('../fonts/OverusedGrotesk-Medium.woff2') format("woff2");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Overused Grotesk Woff;
  src: url('../fonts/OverusedGrotesk-Roman.woff2') format("woff2");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Overused Grotesk Woff;
  src: url('../fonts/OverusedGrotesk-Light.woff2') format("woff2");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Overused Grotesk Woff;
  src: url('../fonts/OverusedGrotesk-SemiBold.woff2') format("woff2");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Overused Grotesk;
  src: url('../fonts/OverusedGrotesk-Medium.otf') format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Overused Grotesk;
  src: url('../fonts/OverusedGrotesk-Roman.otf') format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Overused Grotesk;
  src: url('../fonts/OverusedGrotesk-Light.otf') format("opentype");
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: Overusedgrotesk;
  src: url('../fonts/OverusedGrotesk-Medium.otf') format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

:root {
  --woelfli-black: black;
  --color: white;
  --woelfli-grau: #444c56;
  --woelfli-blaugrau: #243848;
  --woelfli-beige: #f7f3eb;
  --size-48: 0px;
  --color\<deleted\|variable-ca18ade5-8558-bbe2-962e-514a420944d6\>: white;
  --size-32: 2rem;
  --section-background: whitesmoke;
  --size-16: 1rem;
  --woelfli-lightgrey: whitesmoke;
  --grid-margin: 0px;
  --size-2: .125rem;
  --size-4: .25rem;
  --size-8: .5rem;
  --size-12: .75rem;
  --size-24: 1.5rem;
  --size-64: 0px;
  --size-80: 0px;
  --size-96: 0px;
  --size-120: 0px;
  --size-144: 0px;
  --grid-gutter: var(--size-16);
}

.w-layout-grid {
  grid-row-gap: 16px;
  grid-column-gap: 16px;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.w-checkbox {
  margin-bottom: 5px;
  padding-left: 20px;
  display: block;
}

.w-checkbox:before {
  content: " ";
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-checkbox:after {
  content: " ";
  clear: both;
  grid-area: 1 / 1 / 2 / 2;
  display: table;
}

.w-checkbox-input {
  float: left;
  margin: 4px 0 0 -20px;
  line-height: normal;
}

.w-checkbox-input--inputType-custom {
  border: 1px solid #ccc;
  border-radius: 2px;
  width: 12px;
  height: 12px;
}

.w-checkbox-input--inputType-custom.w--redirected-checked {
  background-color: #3898ec;
  background-image: url('https://d3e54v103j8qbb.cloudfront.net/static/custom-checkbox-checkmark.589d534424.svg');
  background-position: 50%;
  background-repeat: no-repeat;
  background-size: cover;
  border-color: #3898ec;
}

.w-checkbox-input--inputType-custom.w--redirected-focus {
  box-shadow: 0 0 3px 1px #3898ec;
}

.w-form-formradioinput--inputType-custom {
  border: 1px solid #ccc;
  border-radius: 50%;
  width: 12px;
  height: 12px;
}

.w-form-formradioinput--inputType-custom.w--redirected-focus {
  box-shadow: 0 0 3px 1px #3898ec;
}

.w-form-formradioinput--inputType-custom.w--redirected-checked {
  border-width: 4px;
  border-color: #3898ec;
}

body {
  color: #b6b6b6;
  background-color: #fff;
  font-family: Overused Grotesk, Arial, sans-serif;
  font-size: 1rem;
  line-height: 1.5;
}

h1 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-family: Overused Grotesk, Arial, sans-serif;
  font-size: 4.125rem;
  font-weight: 300;
  line-height: 1.06;
}

h2 {
  text-align: left;
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 2.625rem;
  font-weight: 400;
  line-height: 1.19;
}

h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1.2;
}

h4 {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 1.75rem;
  font-weight: 700;
  line-height: 1.2;
}

p {
  margin-bottom: 10px;
}

a {
  color: #fff;
  text-decoration: none;
}

blockquote {
  border-left: 5px solid #e2e2e2;
  margin-bottom: 10px;
  padding: 10px 20px;
  font-size: 2.625rem;
  font-weight: 300;
  line-height: 1.3;
}

.section {
  flex-direction: column;
  justify-content: center;
  height: 100%;
  min-height: 100%;
  display: flex;
}

.section.mapbox-section {
  min-height: 100%;
  overflow: visible;
}

.container {
  width: 100%;
  height: auto;
  margin-left: auto;
  margin-right: auto;
  display: block;
  position: relative;
}

.container.is-map {
  height: 100dvh;
}

.locations-map_wrapper {
  z-index: 20;
  width: 25em;
  margin: 1em 1em 1em -27.3em;
  transition: margin .2s;
  display: block;
  position: absolute;
  inset: auto auto auto 0%;
}

.locations-map_wrapper.is--show {
  align-items: flex-start;
  margin-left: 1em;
  display: flex;
}

.locations-map_list {
  z-index: 2;
  display: block;
  position: relative;
}

.mapbox-wrap {
  z-index: 1;
  background-color: #222;
  flex: 1;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: visible;
}

.locations-map_card {
  display: none;
}

.locations-map_name {
  color: var(--woelfli-black);
  text-transform: none;
  margin-top: 0;
  margin-bottom: .2em;
  font-size: 1.2rem;
  font-weight: 500;
  display: flex;
}

.locations-map_population-wrapper {
  flex-wrap: wrap;
  font-family: Satoshi;
  font-size: 1rem;
  display: flex;
}

.locations-map_city {
  color: var(--woelfli-black);
  letter-spacing: .1em;
  text-transform: uppercase;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Overused Grotesk, Arial, sans-serif;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.5;
}

.locations-map_item {
  color: #000;
  display: none;
}

.locations-map_item.is--show {
  height: auto;
  display: block;
}

.card_heading {
  color: var(--woelfli-black);
  text-transform: none;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.8rem;
  font-weight: 400;
  line-height: 1.5em;
}

.location-map_card-wrap {
  z-index: 5;
  background-color: var(--color);
  border-radius: 5px;
  align-items: center;
  min-width: 25rem;
  max-width: 25rem;
  height: auto;
  position: relative;
  overflow: scroll;
  box-shadow: 1px 1px 3px #00000026;
}

.image {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.location-map_card-text-wrap {
  padding: 1.5rem 1rem;
}

.card_divider {
  background-color: var(--woelfli-grau);
  width: 100%;
  height: 1px;
  margin-top: 24px;
  margin-bottom: 24px;
}

.card_description {
  color: var(--woelfli-black);
  font-size: .875rem;
}

.card_read-more-link {
  color: var(--woelfli-black);
  font-size: .87rem;
  font-weight: 500;
  text-decoration: none;
}

.icon-embed-xxsmall {
  color: var(--woelfli-black);
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 1rem;
  height: 1rem;
  display: flex;
}

.close-block {
  background-color: var(--woelfli-blaugrau);
  color: var(--color);
  cursor: pointer;
  border-radius: 8px;
  padding: 16px 8px 16px 16px;
  position: absolute;
  inset: auto -8% 330px auto;
}

.feature-item {
  color: var(--color);
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.margin-right.margin-small {
  margin-right: 8px;
}

.collection-list {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.card_feat-heading {
  color: var(--color);
  letter-spacing: 0;
  text-transform: none;
  margin-bottom: 24px;
  font-size: 1rem;
  font-weight: 400;
}

.card-fade {
  z-index: 5;
  background-image: linear-gradient(180deg, transparent, var(--woelfli-grau));
  border-radius: 16px;
  width: 100%;
  height: 2rem;
  display: none;
  position: absolute;
  inset: auto 0% 0%;
}

.text-block {
  font-weight: 500;
}

.rl-styleguide_empty-space {
  z-index: -1;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  padding: 2rem;
  display: flex;
  position: relative;
}

.max-width-large {
  flex-flow: column;
  width: 100%;
  max-width: 55rem;
  display: flex;
}

.max-width-large.header-text-width {
  max-width: 52rem;
}

.rl-styleguide_background-color-list {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
}

.text-color-secondary {
  color: var(--woelfli-grau);
}

.icon-height-custom1 {
  height: 1.25rem;
}

.rl-styleguide_item {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  place-content: start;
  place-items: start;
  padding-bottom: 1rem;
  display: grid;
}

.rl-styleguide_item.is-stretch {
  grid-row-gap: 1rem;
  justify-items: stretch;
}

.text-weight-bold {
  font-weight: 700;
}

.spacer-xxlarge {
  width: 100%;
  padding-top: 5rem;
}

.padding-xlarge {
  padding: 4rem;
}

.text-color-white {
  color: #fff;
}

.max-width-xxsmall {
  width: 100%;
  max-width: 20rem;
}

.padding-0 {
  padding: 0;
}

.rl-styleguide_header {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: space-between;
  align-items: center;
  padding-top: 3rem;
  padding-bottom: 3rem;
  display: flex;
}

.rl-styleguide_item-row {
  grid-column-gap: 1.5rem;
  grid-row-gap: 0rem;
  grid-template-rows: auto;
  grid-template-columns: 15rem 1fr;
  align-items: center;
}

.rl-styleguide_item-row.is-button-row {
  grid-template-columns: 19rem 1fr;
}

.margin-xlarge {
  margin: 4rem;
}

.padding-vertical, .padding-vertical.padding-tiny, .padding-vertical.padding-xhuge, .padding-vertical.padding-large, .padding-vertical.padding-xxsmall, .padding-vertical.padding-huge, .padding-vertical.padding-medium, .padding-vertical.padding-custom1, .padding-vertical.padding-xlarge, .padding-vertical.padding-0, .padding-vertical.padding-xxhuge, .padding-vertical.padding-custom2, .padding-vertical.padding-xxlarge, .padding-vertical.padding-xsmall, .padding-vertical.padding-custom3, .padding-vertical.padding-small {
  padding-left: 0;
  padding-right: 0;
}

.form_message-success-wrapper {
  margin-top: 1rem;
  padding: 0;
}

.padding-xxlarge {
  padding: 5rem;
}

.icon-embed-custom1 {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 1.25rem;
  height: 1.25rem;
  display: flex;
}

.text-color-black {
  color: #000;
}

.padding-left, .padding-left.padding-xxlarge, .padding-left.padding-custom3, .padding-left.padding-xsmall, .padding-left.padding-small, .padding-left.padding-0, .padding-left.padding-custom1, .padding-left.padding-medium, .padding-left.padding-xhuge, .padding-left.padding-large, .padding-left.padding-xxsmall, .padding-left.padding-huge, .padding-left.padding-tiny, .padding-left.padding-xlarge, .padding-left.padding-xxhuge, .padding-left.padding-custom2 {
  padding-top: 0;
  padding-bottom: 0;
  padding-right: 0;
}

.form_checkbox-icon {
  cursor: pointer;
  border: 1px solid #000;
  border-radius: 0;
  width: 1.125rem;
  min-width: 1.125rem;
  height: 1.125rem;
  min-height: 1.125rem;
  margin-top: 0;
  margin-left: -1.25rem;
  margin-right: .5rem;
  transition: all .2s;
}

.form_checkbox-icon.w--redirected-checked {
  box-shadow: none;
  background-color: #000;
  background-size: 16px 16px;
  border-width: 1px;
  border-color: #000;
}

.form_checkbox-icon.w--redirected-focus {
  box-shadow: none;
  border-color: #000;
}

.form_checkbox-icon.is-alternate {
  border-color: #fff;
}

.max-width-full {
  width: 100%;
  max-width: none;
}

.max-width-full.job-header-cta {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.max-width-full.success-title {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  display: flex;
}

.rl-styleguide_subheading {
  z-index: 3;
  color: #000;
  background-color: #eee;
  border-radius: .75rem;
  margin-bottom: 1rem;
  padding: 1rem;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}

.margin-top-auto {
  margin-top: auto;
}

.margin-bottom, .margin-bottom.margin-xhuge {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}

.margin-bottom.margin-small {
  margin: 0 0 1.875rem;
}

.margin-bottom.margin-small.intro-margin {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  margin-bottom: 0;
  display: flex;
}

.margin-bottom.margin-small.max-width-xsmall {
  max-width: 25rem;
}

.margin-bottom.margin-small.bieten-top {
  grid-column-gap: 0px;
  grid-row-gap: 0px;
  flex-flow: column;
  display: flex;
}

.margin-bottom.margin-small.trainee-text-group {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  display: flex;
}

.margin-bottom.margin-large, .margin-bottom.margin-huge, .margin-bottom.margin-custom3, .margin-bottom.margin-xsmall {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}

.margin-bottom.margin-xsmall.form-check {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  display: flex;
}

.margin-bottom.margin-tiny, .margin-bottom.margin-custom1, .margin-bottom.margin-0, .margin-bottom.margin-custom2, .margin-bottom.margin-xxlarge, .margin-bottom.margin-medium, .margin-bottom.margin-xlarge, .margin-bottom.margin-xxhuge {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
}

.margin-bottom.margin-xxsmall {
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
  display: flex;
  position: relative;
}

.text-color-primary {
  color: #000;
}

.text-weight-light {
  font-weight: 300;
}

.spacer-medium {
  width: 100%;
  padding-top: 2rem;
}

.margin-xsmall {
  margin: 1rem;
}

.rl-styleguide_callout-heading-wrapper {
  font-size: 1.25rem;
  font-weight: 600;
}

.icon-1x1-medium {
  justify-content: center;
  align-items: center;
  width: 3rem;
  height: 3rem;
  display: block;
}

.icon-1x1-medium.step-icon {
  background-color: var(--woelfli-black);
  border-radius: 6px;
}

.icon-height-large {
  height: 5rem;
}

.container-medium {
  width: 100%;
  max-width: 64rem;
  margin-left: auto;
  margin-right: auto;
}

.shadow-xxsmall {
  box-shadow: 0 1px 2px #0000000d;
}

.shadow-medium {
  box-shadow: 0 12px 16px -4px #00000014, 0 4px 6px -2px #00000008;
}

.global-styles {
  display: block;
  position: fixed;
  inset: 0% auto auto 0%;
}

.tag {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  color: #000;
  background-color: #eee;
  border: 1px #eee;
  justify-content: center;
  align-items: center;
  padding: .25rem .5rem;
  font-size: .875rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
}

.tag.is-text {
  color: #4b4b4b;
  background-color: #0000;
  border-style: none;
  border-color: #0000;
  flex: none;
  align-self: flex-start;
  padding: 0;
  font-size: 1.188rem;
  font-weight: 400;
  line-height: 1.6;
  display: inline-block;
}

.tag.is-text.is-alternate {
  color: #fff;
  background-color: #0000;
}

.tag.is-text.uppercase {
  text-transform: uppercase;
}

.tag.is-text.uppercase.text-color-black {
  color: #000;
}

.tag.is-alternate {
  color: #000;
  background-color: #fff;
  border-color: #fff;
}

.rl-styleguide_button-row {
  grid-column-gap: 1rem;
  display: flex;
}

.margin-right-2, .margin-right-2.margin-custom2, .margin-right-2.margin-medium, .margin-right-2.margin-0, .margin-right-2.margin-small, .margin-right-2.margin-xxhuge, .margin-right-2.margin-xxlarge, .margin-right-2.margin-large, .margin-right-2.margin-xxsmall, .margin-right-2.margin-huge, .margin-right-2.margin-xhuge, .margin-right-2.margin-tiny, .margin-right-2.margin-custom3, .margin-right-2.margin-custom1, .margin-right-2.margin-xlarge, .margin-right-2.margin-xsmall {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: 0;
}

.padding-small {
  padding: 1.5rem;
}

.max-width-xxlarge {
  width: 100%;
  max-width: 80rem;
}

.container-small {
  width: 100%;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
}

.spacer-huge {
  width: 100%;
  padding-top: 6rem;
}

.background-color-secondary {
  background-color: var(--woelfli-beige);
}

.icon-embed-plus {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 3rem;
  height: 3rem;
  display: flex;
}

.spacing-clean {
  margin: 0;
  padding: 0;
}

.class-label-column {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.pointer-events-none {
  pointer-events: none;
}

.icon-height-xsmall {
  height: 1.5rem;
}

.margin-vertical, .margin-vertical.margin-xxlarge, .margin-vertical.margin-0, .margin-vertical.margin-custom2, .margin-vertical.margin-xhuge, .margin-vertical.margin-medium, .margin-vertical.margin-custom3, .margin-vertical.margin-xxsmall, .margin-vertical.margin-custom1, .margin-vertical.margin-large, .margin-vertical.margin-xsmall, .margin-vertical.margin-xxhuge, .margin-vertical.margin-small, .margin-vertical.margin-xlarge, .margin-vertical.margin-tiny, .margin-vertical.margin-huge {
  margin-left: 0;
  margin-right: 0;
}

.heading-style-h1 {
  color: #fff;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Overused Grotesk Woff, Arial, sans-serif;
  font-size: 3.875rem;
  font-weight: 500;
  line-height: 1;
}

.heading-style-h1.black-h1 {
  color: #000;
  margin-top: 0;
  font-family: Overused Grotesk Woff, Arial, sans-serif;
  font-weight: 500;
  line-height: 1;
}

.heading-style-h1.text-color-black {
  color: var(--woelfli-black);
}

.heading-style-h1.text-color-black.bewerben-fixed {
  position: relative;
}

.heading-style-h1.absolute-title {
  position: absolute;
}

.form_field-label {
  color: #000;
  margin-bottom: .5rem;
  font-weight: 400;
}

.form_field-label.is-alternate {
  color: #fff;
}

.background-color-tertiary {
  background-color: var(--woelfli-blaugrau);
}

.margin-tiny {
  margin: .25rem;
}

.text-style-strikethrough {
  text-decoration: line-through;
}

.form_radio-label {
  margin-bottom: 0;
}

.form_message-error {
  color: #b42318;
  background-color: #fef3f2;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  display: flex;
}

.icon-1x1-small {
  width: 2rem;
  height: 2rem;
}

.text-style-muted {
  opacity: .6;
}

.background-color-black {
  background-color: #000;
}

.rl-styleguide_ratio-bg {
  z-index: -1;
  background-color: #eee;
  min-width: 3rem;
  height: 100%;
  position: relative;
}

.icon-embed-xlarge {
  flex-direction: column;
  flex: 1;
  justify-content: center;
  align-items: center;
  width: 6.5rem;
  height: 6.5rem;
  display: flex;
}

.icon-embed-xlarge.wolf-svg {
  width: 10rem;
  height: 10rem;
}

.rl-styleguide_label {
  color: #1e51f7;
  white-space: nowrap;
  cursor: context-menu;
  background-color: #ebeffa;
  padding: .25rem .5rem;
  font-size: .75rem;
  display: inline-block;
}

.rl-styleguide_label.is-html-tag {
  color: #bb4198;
  background-color: #f9eff6;
}

.max-width-xlarge {
  width: 100%;
  max-width: 64rem;
}

.spacer-small {
  width: 100%;
  padding-top: 1.5rem;
}

.container-large {
  width: 100%;
  max-width: 75rem;
  margin-left: auto;
  margin-right: auto;
}

.container-large.height-custom {
  height: 100%;
}

.margin-huge {
  margin: 6rem;
}

.heading-style-h6 {
  color: var(--woelfli-black);
  justify-content: flex-end;
  align-items: center;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1.4;
  display: flex;
}

.padding-tiny {
  padding: .25rem;
}

.hide {
  display: none;
}

.text-weight-xbold {
  font-weight: 800;
}

.background-color-primary {
  background-color: var(--woelfli-grau);
}

.form_input {
  color: #000;
  background-color: #fff;
  border: 1px #000;
  border-radius: 4px;
  height: auto;
  min-height: 2.75rem;
  margin-bottom: 0;
  padding: .5rem .75rem;
  font-size: 1rem;
  line-height: 1.6;
}

.form_input:focus {
  border-color: #000;
}

.form_input::placeholder {
  color: #0009;
}

.form_input.is-select-input {
  background-image: url('../images/chevron-down.svg');
  background-position: 100%;
  background-repeat: no-repeat;
  background-size: auto;
}

.form_input.is-select-input.is-alternate {
  background-image: url('../images/chevron-down-white.svg');
}

.form_input.is-alternate {
  color: #fff;
  background-color: #0000;
  border-color: #fff;
}

.form_input.is-alternate::placeholder {
  color: #fff9;
}

.form_input.is-text-area {
  height: auto;
  min-height: 11.25rem;
  padding-top: .75rem;
  padding-bottom: .75rem;
  overflow: auto;
}

.form_radio {
  align-items: center;
  margin-bottom: 0;
  padding-left: 1.125rem;
  display: flex;
}

.form_radio.is-alternate {
  color: #fff;
}

.icon-embed-large {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 5rem;
  height: 5rem;
  display: flex;
}

.heading-style-h3 {
  color: var(--woelfli-black);
  text-align: left;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Overused Grotesk Woff, Arial, sans-serif;
  font-size: 1.75rem;
  font-weight: 500;
  line-height: 1.2;
}

.shadow-large {
  box-shadow: 0 20px 24px -4px #00000014, 0 8px 8px -4px #00000008;
}

.button {
  color: #fff;
  text-align: center;
  border: 1px #000;
  justify-content: flex-start;
  align-items: center;
  padding: .75rem 1.5rem;
  text-decoration: none;
  display: flex;
  position: relative;
}

.button.is-secondary {
  color: #000;
  background-color: #0000;
}

.button.is-secondary.is-alternate {
  color: #fff;
  background-color: #0000;
}

.button.is-link {
  color: #000;
  background-color: #0000;
  border-style: none;
  padding: .25rem 0;
  line-height: 1;
  text-decoration: none;
}

.button.is-link.is-icon {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
}

.button.is-link.is-icon.padding-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.button.is-link.is-icon.padding-0.white {
  color: #fff;
  transition: opacity .2s, color .2s, background-color .2s;
}

.button.is-link.is-icon.padding-0.white:hover {
  opacity: .8;
}

.button.is-link.is-icon.padding-0.white.hide {
  display: none;
}

.button.is-link.is-alternate {
  color: #fff;
  background-color: #0000;
}

.button.is-small {
  padding: .5rem 1.25rem;
}

.button.is-small.is-icon-only {
  padding-left: .5rem;
  padding-right: .5rem;
}

.button.is-tertiary {
  color: #000;
  background-color: #0000;
  border-color: #0000;
}

.button.is-alternate {
  color: #000;
  background-color: #fff;
  border-color: #fff;
}

.button.is-icon-only {
  padding-left: .75rem;
  padding-right: .75rem;
}

.button.is-icon {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  justify-content: center;
  align-items: center;
  display: flex;
}

.button.is-navbar2-button {
  padding-top: .5rem;
  padding-bottom: .5rem;
  display: none;
}

.button.job-button {
  color: #000;
  background-color: #f5f5f5;
  border-radius: 6px;
}

.button.contact-job-button {
  color: var(--color);
}

.button.contact-job-button.align-left {
  background-color: var(--woelfli-grau);
  border-radius: 6px;
}

.class-label-row {
  grid-column-gap: .25rem;
  grid-row-gap: .25rem;
  flex-wrap: wrap;
  align-items: center;
  display: flex;
}

.aspect-ratio-widescreen {
  aspect-ratio: 16 / 9;
}

.rl-styleguide_nav-link {
  color: #fff;
  border-radius: .25rem;
  padding: .5rem 1rem;
  text-decoration: none;
}

.rl-styleguide_nav-link.w--current {
  color: #000;
  background-color: #fff;
}

.text-align-right {
  text-align: right;
}

.shadow-small {
  box-shadow: 0 4px 8px -2px #0000001a, 0 2px 4px -2px #0000000f;
}

.text-weight-medium {
  font-weight: 500;
}

.form_form {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.max-width-xsmall {
  width: 100%;
  max-width: 25rem;
}

.max-width-xsmall.vw-width {
  flex: 0 auto;
  width: 28.403vw;
}

.max-width-xsmall.vw-width.wolf-icon-mitte {
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.button-group {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  flex-wrap: wrap;
  align-items: center;
  display: flex;
}

.margin-top, .margin-top.margin-xxhuge, .margin-top.margin-0, .margin-top.margin-medium, .margin-top.margin-custom1, .margin-top.margin-custom3, .margin-top.margin-xlarge, .margin-top.margin-large, .margin-top.margin-custom2, .margin-top.margin-xhuge, .margin-top.margin-small, .margin-top.margin-xsmall, .margin-top.margin-tiny, .margin-top.margin-xxsmall, .margin-top.margin-huge, .margin-top.margin-xxlarge {
  margin-bottom: 0;
  margin-left: 0;
  margin-right: 0;
}

.spacer-tiny {
  width: 100%;
  padding-top: .25rem;
}

.text-align-center {
  text-align: center;
}

.pointer-events-auto {
  pointer-events: auto;
}

.main-wrapper {
  margin-top: .938rem;
  padding-left: .938rem;
  padding-right: .938rem;
}

.margin-xxhuge {
  margin: 10rem;
}

.form_message-error-wrapper {
  margin-top: 1rem;
  padding: 0;
}

.text-weight-semibold {
  font-weight: 500;
}

.padding-section-large {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  flex-flow: column;
  padding-top: 5rem;
  padding-bottom: 5rem;
  display: flex;
}

.padding-section-large.padding-custom2 {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  padding-top: 5rem;
  padding-bottom: 0;
}

.padding-section-large.padding-custom {
  padding-bottom: 2rem;
}

.padding-section-large.footer-padding {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.padding-section-large.height-custom {
  height: 100%;
}

.shadow-xsmall {
  box-shadow: 0 1px 3px #0000001a, 0 1px 2px #0000000f;
}

.rl-styleguide_empty-box {
  z-index: -1;
  background-color: #eee;
  min-width: 3rem;
  height: 3rem;
  position: relative;
}

.aspect-ratio-square {
  aspect-ratio: 1;
}

.rl-styleguide_icons-list {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-wrap: wrap;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  grid-auto-flow: column;
  display: grid;
}

.shadow-xxlarge {
  box-shadow: 0 32px 64px -12px #00000024;
}

.form_checkbox {
  align-items: center;
  margin-bottom: 0;
  padding-left: 1.25rem;
  display: flex;
}

.form_checkbox.is-alternate {
  color: #fff;
}

.spacer-xhuge {
  width: 100%;
  padding-top: 7rem;
}

.spacer-xxhuge {
  width: 100%;
  padding-top: 10rem;
}

.margin-0 {
  margin: 0;
}

.heading-style-h5 {
  color: var(--woelfli-black);
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.4;
}

.text-size-regular {
  color: #000;
  letter-spacing: .015em;
  flex: 1;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Overused Grotesk Woff, Arial, sans-serif;
  font-size: 1rem;
  font-weight: 400;
}

.text-size-regular.intro-card-text {
  white-space: break-spaces;
  word-break: normal;
}

.text-size-regular.text-color-white {
  color: var(--color);
  margin-top: 0;
}

.text-size-regular.text-color-white.padding-expand {
  margin-top: 1rem;
  padding-top: 0;
}

.text-size-regular.grid-p-headline {
  max-width: 40rem;
}

.text-size-regular.text-color-black {
  color: #000;
}

.text-size-regular.text-color-black.text-weight-medium, .text-size-regular.text-weight-medium {
  font-weight: 500;
}

.text-size-regular.word-break {
  word-break: normal;
  overflow-wrap: normal;
}

.text-size-regular.padding-expand {
  padding-top: 1.875rem;
}

.text-size-regular.hyphen {
  -webkit-hyphens: auto;
  hyphens: auto;
}

.aspect-ratio-landscape {
  aspect-ratio: 3 / 2;
}

.rl-styleguide_list {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  padding-bottom: 4rem;
}

.background-color-white {
  background-color: #fff;
}

.aspect-ratio-portrait {
  aspect-ratio: 2 / 3;
}

.max-width-medium {
  width: 100%;
  max-width: 40rem;
}

.max-width-medium.after-intro-width {
  max-width: 52rem;
}

.max-width-medium.icon-job-columns {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  display: flex;
}

.padding-global {
  padding-left: 5%;
  padding-right: 5%;
  position: relative;
}

.padding-global.testimonial-padding {
  padding-left: 25%;
  padding-right: 25%;
}

.padding-global.height-custom {
  height: 100%;
}

.heading-style-h4 {
  color: var(--woelfli-black);
  letter-spacing: .01em;
  margin-top: 0;
  margin-bottom: 0;
  font-family: Overused Grotesk Woff, Arial, sans-serif;
  font-size: 1.375rem;
  font-weight: 500;
}

.heading-style-h4.cta-text {
  color: #000;
  margin-top: 0;
  margin-bottom: 0;
  line-height: 1;
}

.heading-style-h4.text-color-white {
  color: var(--color);
  letter-spacing: .02em;
}

.form_field-wrapper {
  position: relative;
}

.padding-xhuge {
  padding: 7rem;
}

.padding-xxsmall {
  padding: .5rem;
}

.text-style-quote {
  color: #000;
  border-left: 0 #000;
  margin-bottom: 0;
  padding: 0;
  font-size: 2.625rem;
  font-weight: 300;
  line-height: 1.5;
}

.margin-xhuge {
  margin: 7rem;
}

.text-style-nowrap {
  white-space: nowrap;
}

.padding-large {
  padding: 3rem;
}

.padding-horizontal, .padding-horizontal.padding-xsmall, .padding-horizontal.padding-small, .padding-horizontal.padding-xxlarge, .padding-horizontal.padding-huge, .padding-horizontal.padding-large, .padding-horizontal.padding-xhuge, .padding-horizontal.padding-medium, .padding-horizontal.padding-xxsmall, .padding-horizontal.padding-custom1, .padding-horizontal.padding-custom3, .padding-horizontal.padding-0, .padding-horizontal.padding-tiny, .padding-horizontal.padding-xlarge, .padding-horizontal.padding-xxhuge, .padding-horizontal.padding-custom2 {
  padding-top: 0;
  padding-bottom: 0;
}

.rl-styleguide_callout-link-wrapper-colors {
  background-image: linear-gradient(135deg, #ff744826, #ff484826 50%, #6248ff26), linear-gradient(#fff, #fff);
  border-radius: .6875rem;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: .75rem 1rem;
  display: flex;
}

.icon-1x1-xlarge {
  width: 6.5rem;
  height: 6.5rem;
}

.padding-xsmall {
  padding: 1rem;
}

.text-style-tagline {
  color: #4b4b4b;
  justify-content: center;
  align-items: center;
  font-size: 1.188rem;
  font-weight: 400;
  line-height: 1;
  text-decoration: none;
  display: none;
}

.text-style-tagline.wirsind-tagline {
  opacity: 1;
  color: #fff;
  font-weight: 400;
}

.text-style-tagline.text-color-white {
  color: var(--color);
}

.rl-styleguide_form-wrapper {
  padding-right: 1.5rem;
}

.text-size-tiny {
  font-size: .75rem;
}

.icon-1x1-large {
  width: 5rem;
  height: 5rem;
}

.rl-styleguide_spacing {
  border: 1px dashed #000;
}

.text-color-alternate {
  color: #fff;
}

.text-size-large {
  color: var(--woelfli-black);
  text-align: left;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.375rem;
  font-weight: 400;
  line-height: 1.54;
}

.text-size-large.text-weight-medium {
  flex: 0 auto;
  margin-bottom: 0;
  font-weight: 500;
}

.text-size-large.text-weight-medium.line-height {
  line-height: 1.1;
}

.text-size-large.text-weight-medium.line-height.line-height-2 {
  line-height: 1.3;
}

.text-size-large.text-align-center {
  text-align: center;
}

.form_checkbox-label {
  margin-bottom: 0;
}

.form_checkbox-label.text-size-small {
  color: #000;
}

.text-style-allcaps {
  text-transform: uppercase;
}

.rl-styleguide_nav {
  z-index: 1000;
  background-color: #000;
  border-radius: .75rem;
  margin-top: 1rem;
  padding: .5rem;
  position: sticky;
  top: 1rem;
}

.rl-styleguide_callout-link_colors {
  background-color: #fff;
  background-image: linear-gradient(135deg, #ff7448, #ff4848 50%, #6248ff);
  border-radius: .75rem;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1px;
  text-decoration: none;
  display: flex;
  overflow: hidden;
}

.rl-styleguide_heading {
  z-index: 4;
  color: #000;
  margin-bottom: 1.5rem;
  padding: .25rem .5rem;
  font-size: 3.5rem;
  font-weight: 700;
}

.icon-1x1-xxsmall {
  width: 1rem;
  height: 1rem;
}

.margin-xxlarge {
  margin: 5rem;
}

.rl-styleguide_subheading-small {
  font-size: 1rem;
  font-weight: 400;
}

.form_radio-icon {
  cursor: pointer;
  border: 1px solid #000;
  border-radius: 100px;
  width: 1.125rem;
  min-width: 1.125rem;
  height: 1.125rem;
  min-height: 1.125rem;
  margin-top: 0;
  margin-left: -1.125rem;
  margin-right: .5rem;
}

.form_radio-icon.w--redirected-checked {
  background-color: #fff;
  background-image: none;
  border-width: 6px;
  border-color: #000;
}

.form_radio-icon.w--redirected-focus {
  box-shadow: none;
  border-color: #000;
  margin-top: 0;
}

.form_radio-icon.is-alternate {
  border-color: #fff;
}

.overflow-auto {
  overflow: auto;
}

.background-color-alternative {
  background-color: #000;
}

.text-style-link {
  color: #000;
  text-decoration: underline;
}

.icon-embed-medium {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  display: flex;
}

.layer {
  justify-content: center;
  align-items: center;
  position: absolute;
  inset: 0%;
}

.spacer-xlarge {
  width: 100%;
  padding-top: 4rem;
}

.text-align-left {
  text-align: left;
}

.spacer-large {
  width: 100%;
  padding-top: 3rem;
}

.z-index-1 {
  z-index: 1;
  position: relative;
}

.shadow-xlarge {
  box-shadow: 0 24px 48px -12px #0000002e;
}

.text-weight-normal {
  font-weight: 400;
}

.rl-styleguide_button-list {
  grid-column-gap: 1rem;
  grid-row-gap: 1.5rem;
  white-space: normal;
  grid-template-rows: auto;
  grid-template-columns: auto;
  grid-auto-columns: max-content;
  grid-auto-flow: row;
  place-items: center start;
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
  display: grid;
}

.spacer-xxsmall {
  width: 100%;
  padding-top: .5rem;
}

.icon-height-xxsmall {
  height: 1rem;
}

.rl-styleguide_spacing-all {
  display: none;
}

.icon-height-small {
  height: 2rem;
}

.page-wrapper {
  overflow: visible;
}

.page-wrapper.form-success {
  flex-flow: column;
  justify-content: flex-end;
  align-items: stretch;
  display: flex;
}

.spacer-xsmall {
  width: 100%;
  padding-top: 1rem;
}

.form_message-success {
  color: #027a48;
  background-color: #ecfdf3;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2.5rem;
  display: flex;
}

.padding-bottom, .padding-bottom.padding-custom2, .padding-bottom.padding-xxsmall, .padding-bottom.padding-xlarge, .padding-bottom.padding-small, .padding-bottom.padding-xsmall, .padding-bottom.padding-custom3, .padding-bottom.padding-xxlarge, .padding-bottom.padding-0, .padding-bottom.padding-medium, .padding-bottom.padding-tiny, .padding-bottom.padding-xhuge, .padding-bottom.padding-custom1, .padding-bottom.padding-large, .padding-bottom.padding-xxhuge, .padding-bottom.padding-huge {
  padding-top: 0;
  padding-left: 0;
  padding-right: 0;
}

.margin-xxsmall {
  margin: .5rem;
}

.icon-height-xlarge {
  height: 6.5rem;
}

.text-size-small {
  font-size: .875rem;
}

.padding-section-medium {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.margin-horizontal, .margin-horizontal.margin-medium, .margin-horizontal.margin-huge, .margin-horizontal.margin-custom1, .margin-horizontal.margin-large, .margin-horizontal.margin-custom3, .margin-horizontal.margin-small, .margin-horizontal.margin-0, .margin-horizontal.margin-xsmall, .margin-horizontal.margin-tiny, .margin-horizontal.margin-xxhuge, .margin-horizontal.margin-custom2, .margin-horizontal.margin-xxsmall, .margin-horizontal.margin-xlarge, .margin-horizontal.margin-xhuge, .margin-horizontal.margin-xxlarge {
  margin-top: 0;
  margin-bottom: 0;
}

.margin-left, .margin-left.margin-xxhuge, .margin-left.margin-tiny, .margin-left.margin-custom1, .margin-left.margin-xsmall, .margin-left.margin-xxsmall, .margin-left.margin-large, .margin-left.margin-huge, .margin-left.margin-xhuge, .margin-left.margin-xlarge, .margin-left.margin-custom3, .margin-left.margin-medium, .margin-left.margin-xxlarge, .margin-left.margin-0, .margin-left.margin-custom2, .margin-left.margin-small {
  margin-top: 0;
  margin-bottom: 0;
  margin-right: 0;
}

.overflow-hidden {
  overflow: hidden;
}

.icon-1x1-custom1 {
  width: 1.25rem;
  height: 1.25rem;
}

.margin-large {
  margin: 3rem;
}

.icon-height-medium {
  height: 3rem;
}

.margin-medium {
  margin: 2rem;
}

.overflow-scroll {
  overflow: scroll;
}

.text-style-italic {
  font-style: italic;
}

.padding-right, .padding-right.padding-xxlarge, .padding-right.padding-xlarge, .padding-right.padding-large, .padding-right.padding-huge, .padding-right.padding-xxsmall, .padding-right.padding-xxhuge, .padding-right.padding-0, .padding-right.padding-medium, .padding-right.padding-custom1, .padding-right.padding-xsmall, .padding-right.padding-tiny, .padding-right.padding-custom2, .padding-right.padding-xhuge, .padding-right.padding-custom3, .padding-right.padding-small {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0;
}

.icon-1x1-xsmall {
  width: 1.5rem;
  height: 1.5rem;
}

.text-size-medium {
  color: #000;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.2;
}

.text-size-medium.text-color-black {
  color: var(--woelfli-black);
  text-decoration: underline;
}

.text-size-medium.text-weight-medium {
  margin-top: 0;
  font-weight: 500;
}

.padding-xxhuge {
  padding: 10rem;
}

.icon-embed-xsmall {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
}

.icon-embed-xsmall.back-arrow {
  transform-style: preserve-3d;
  transform: rotateX(0)rotateY(180deg)rotateZ(0);
}

.z-index-2 {
  z-index: 2;
  position: relative;
}

.align-center {
  margin-left: auto;
  margin-right: auto;
}

.heading-style-h2 {
  color: var(--woelfli-black);
  word-break: normal;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 2.169rem;
  font-weight: 400;
  line-height: 1.2;
}

.heading-style-h2.text-weight-medium {
  font-weight: 500;
}

.heading-style-h2.text-weight-medium.line-height {
  line-height: 1;
}

.heading-style-h2.text-color-white {
  color: var(--color);
}

.padding-medium {
  padding: 2rem;
}

.rl-styleguide_shadows-list {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  align-items: start;
}

.max-width-small {
  width: 100%;
  max-width: 30rem;
}

.padding-huge {
  padding: 6rem;
}

.margin-small {
  margin: 1.5rem;
}

.padding-top, .padding-top.padding-medium, .padding-top.padding-large, .padding-top.padding-xxlarge, .padding-top.padding-xhuge, .padding-top.padding-tiny, .padding-top.padding-custom1, .padding-top.padding-xxhuge, .padding-top.padding-xlarge, .padding-top.padding-custom2, .padding-top.padding-xxsmall, .padding-top.padding-xsmall, .padding-top.padding-custom3, .padding-top.padding-small, .padding-top.padding-huge, .padding-top.padding-0 {
  padding-bottom: 0;
  padding-left: 0;
  padding-right: 0;
}

.padding-section-small {
  padding-top: 3rem;
  padding-bottom: 3rem;
}

.rl-styleguide_color-spacer {
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  display: flex;
}

.mapbox-wrapper {
  height: 100svh;
  position: relative;
  overflow: visible;
}

.body {
  background-color: #fff;
}

.paragraph {
  color: var(--woelfli-grau);
}

.block-quote {
  color: var(--woelfli-black);
}

.header_content {
  z-index: 22;
  position: relative;
}

.header_background-image {
  z-index: 0;
  object-fit: cover;
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  inset: 0%;
}

.lightbox-play-icon {
  z-index: 2;
  color: var(--\<unknown\|relume-variable-color-neutral-2\>);
  justify-content: center;
  align-items: center;
  display: flex;
  position: absolute;
}

.navbar2_component {
  z-index: 1200;
  background-color: #ddd0;
  border-bottom: 0 #000;
  align-items: center;
  width: 100%;
  height: auto;
  min-height: 5.5rem;
  padding: 0 5%;
  display: flex;
  position: fixed;
  top: 2rem;
  left: 0%;
  right: 0%;
}

.navbar2_container {
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  grid-template-rows: auto;
  grid-template-columns: .375fr 1fr .375fr;
  grid-auto-columns: 1fr;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 75rem;
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  display: flex;
}

.navbar2_logo-link {
  justify-content: center;
  align-items: center;
  padding-left: 0;
  display: flex;
}

.navbar2_logo-link.w--current:active {
  color: #fff;
}

.navbar2_menu {
  justify-content: center;
  align-items: center;
  display: flex;
  position: static;
}

.navbar2_link {
  color: var(--color);
  letter-spacing: .06rem;
  -webkit-text-decoration-skip-ink: auto;
  text-decoration-skip-ink: auto;
  padding: .5rem 1rem;
  font-size: 1.063rem;
  font-weight: 300;
}

.navbar2_link:hover {
  text-decoration: underline;
}

.navbar2_link.w--current {
  color: var(--color);
  text-decoration: underline;
}

.navbar2_link.text-color-black {
  color: var(--woelfli-black);
}

.navbar2_link.text-color-black.text-size-medium.w--current, .navbar2_link.text-size-medium:hover, .navbar2_link.text-size-medium.w--current, .navbar2_link.text-size-medium.w--current:hover {
  text-underline-position: under;
}

.navbar2_button-wrapper {
  grid-column-gap: 1rem;
  margin-left: 1rem;
  display: none;
}

.navbar2_menu-button {
  padding: 0;
}

.button_dialog {
  grid-column-gap: .125rem;
  grid-row-gap: .125rem;
  color: #000;
  z-index: var(--z-above);
  -webkit-user-select: none;
  user-select: none;
  background-color: #0000;
  flex-flow: column-reverse;
  justify-content: center;
  align-items: center;
  padding: .75rem 2rem;
  display: flex;
  position: relative;
}

.button_dialog.is-menu-button {
  grid-column-gap: .8rem;
  grid-row-gap: .8rem;
  color: #000;
  flex-flow: row;
  padding: .75rem 1rem;
  font-size: 1rem;
  line-height: 1.2em;
}

.button_dialog-menu-line-wrap {
  grid-gap: .375rem;
  display: grid;
}

.button_dialog-menu-line {
  color: var(--color);
  background-color: currentColor;
  width: 2rem;
  height: .0625rem;
  display: block;
}

.button_dialog-bg {
  z-index: var(--z-below);
  background-color: #444c5600;
  border-radius: .5rem;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
  overflow: clip;
}

.block-text_action {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: wrap;
  padding-top: 0;
  display: inline-flex;
}

.menu-dialog {
  color: var(--color);
  z-index: var(--z-dialog);
  content-visibility: auto;
  background-color: #0000;
  border-style: none;
  width: 100%;
  max-width: 100vw;
  height: 100%;
  min-height: 100dvh;
  max-height: 100lvh;
  margin: 0;
  padding: 0;
  display: grid;
  position: fixed;
  inset: 0%;
  overflow: hidden;
}

.menu-dialog_outer {
  padding-top: var(--size-48);
  padding-right: var(--size-48);
  padding-bottom: var(--size-48);
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-end;
  width: 100%;
  max-width: 100vw;
  display: flex;
  position: relative;
  overflow: hidden auto;
}

.menu-dialog_inner {
  padding-bottom: var(--size-48);
  padding-left: var(--size-48);
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  color: var(--color);
  z-index: var(--z-above);
  width: calc((var(--grid-column-width) * 6)  + (var(--grid-gutter) * (6 - 1)));
  border-radius: .4rem;
  flex-flow: column;
  display: flex;
  position: relative;
  background-color: #fff !important;
}

.menu-dialog_header {
  justify-content: space-between;
  align-items: center;
  padding-top: 1.5rem;
  padding-right: 1.5rem;
  display: flex;
}

.menu-dialog_header-text {
  color: #444c56 !important;
}

.menu-dialog_button-close {
  z-index: var(--z-above);
  background-color: #0000;
  border-style: none;
  padding: 0;
  position: relative;
}

.icon {
  -webkit-user-select: none;
  user-select: none;
  transform-style: preserve-3d;
  flex-shrink: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: block;
  transform: translateZ(0);
}

.menu-dialog_nav {
  padding-top: 0;
  padding-right: var(--size-48);
}

.menu-dialog_nav-list-item {
  color: var(--woelfli-blaugrau);
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.menu-dialog_nav-link-outer {
  color: var(--woelfli-blaugrau);
  font-size: 1.25rem;
  display: block;
}

.menu-dialog_nav-link-outer.phone-number {
  color: var(--woelfli-blaugrau);
  font-size: 3rem;
}

.menu-dialog_nav-link-outer.phone-number.text-size-regular {
  margin-top: 1rem;
  font-size: 1.85rem;
}

.menu-dialog_nav-link-outer.email {
  color: var(--woelfli-blaugrau);
  font-size: 1.85rem;
}

.menu-dialog_nav-link {
  color: var(--color\<deleted\|variable-ca18ade5-8558-bbe2-962e-514a420944d6\>);
  justify-content: flex-start;
  align-items: center;
  padding-top: .375rem;
  padding-bottom: .375rem;
  text-decoration: none;
  display: inline-flex;
  position: relative;
}

.menu-dialog_nav-link.u-h2 {
  color: #fff;
  font-size: 1.25rem;
}

.menu-dialog_nav-link.u-h2:active {
  color: var(--woelfli-grau);
}

.menu-dialog_footer {
  padding-top: var(--size-32);
  padding-right: var(--size-48);
  padding-bottom: var(--size-48);
  padding-left: var(--size-48);
  color: var(--color);
  z-index: var(--z-above);
  width: calc((var(--grid-column-width) * 6)  + (var(--grid-gutter) * (6 - 1)));
  border-radius: .4rem;
  margin-top: 1rem;
  display: none;
  background-color: #fff !important;
  outline-color: #fff !important;
}

.menu-dialog_social-title {
  padding-bottom: .5rem;
  display: inline-block;
  color: #444c56 !important;
}

.menu-dialog_social-list {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  display: flex;
}

.menu-dialog_social-link {
  color: var(--color\<deleted\|variable-ca18ade5-8558-bbe2-962e-514a420944d6\>);
}

.menu-dialog_backdrop {
  background-color: color-mix(in hsl, var(--color), transparent 20%);
  opacity: 1;
  -webkit-backdrop-filter: blur(1.25rem);
  backdrop-filter: blur(1.25rem);
  cursor: no-drop;
  border-style: none;
  padding: 0;
  position: fixed;
  inset: 0%;
}

.menu-text {
  color: var(--color);
}

.section_header46 {
  background-color: var(--section-background);
  color: var(--color);
}

.header46_component {
  color: var(--woelfli-grau);
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.section_layout242 {
  background-color: var(--section-background);
  border-top: 1px #d3d1e3;
}

.layout242_list {
  grid-column-gap: 7.5rem;
  grid-row-gap: 7.5rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: start;
  display: grid;
}

.layout242_item {
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.headline-tag-group {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  display: flex;
}

.section_layout459 {
  background-image: none;
  background-size: auto;
  height: 250svh;
  position: relative;
}

.layout459_component {
  z-index: 2;
  height: 100%;
  position: relative;
}

.layout459_list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template: "."
                 "."
                 "."
                 / 1fr;
  grid-auto-columns: 1fr;
  grid-auto-flow: row;
  place-content: stretch;
  place-items: start stretch;
  display: grid;
  position: sticky;
  top: 15rem;
}

.layout459_group-item {
  grid-column-gap: 7rem;
  grid-row-gap: 7rem;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  grid-auto-flow: row;
  place-items: end;
  display: grid;
}

.layout459_group-item.grid-item-double {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-columns: 1fr 1fr;
  grid-auto-flow: row;
  place-content: stretch space-between;
  place-items: start stretch;
  position: relative;
}

.layout459_group-item.grid-item-center {
  grid-template-columns: 1fr;
  justify-content: center;
  place-items: center stretch;
  display: flex;
}

.know-how-box {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  background-color: #8784847a;
  border: 1px #fff9;
  border-radius: .5rem;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  margin-top: 0;
  padding: 3rem;
  transition: all .2s cubic-bezier(.645, .045, .355, 1), filter .2s cubic-bezier(.645, .045, .355, 1), transform .96s cubic-bezier(.3, 1.17, .55, .99);
  display: flex;
}

.know-how-box:hover {
  -webkit-backdrop-filter: blur(7px);
  backdrop-filter: blur(7px);
}

.know-how-box.grid-item3 {
  margin-right: 8.889vw;
}

.intro-section {
  color: var(--color);
  background-color: #f5f5f5;
}

.card-grid-headline {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  max-width: 34rem;
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
  display: flex;
}

.header_background-video {
  z-index: 0;
  background-image: linear-gradient(#00000080, #00000080), url('../images/header-bg_1header-bg.avif');
  background-position: 0 0, 50%;
  background-repeat: repeat, no-repeat;
  background-size: auto, cover;
  background-attachment: scroll, fixed;
  width: 100%;
  max-width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.layout361_component {
  grid-column-gap: 5rem;
  grid-row-gap: 5rem;
  flex-flow: column;
  display: flex;
}

.category-card {
  padding: var(--size-16);
  grid-column-gap: var(--size-32);
  grid-row-gap: var(--size-32);
  aspect-ratio: 2.39;
  color: #fff;
  border-radius: 6px;
  flex-flow: column;
  flex: 1;
  justify-content: flex-end;
  align-items: flex-start;
  width: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.category-card.propertylab {
  background-color: #cbe7ef;
  border-radius: 6px;
  justify-content: center;
  align-items: flex-start;
}

.category-card.coreal {
  background-color: #ca7085;
  justify-content: center;
  align-items: flex-start;
}

.absolute-image {
  z-index: -2;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.absolute-image.logo-image {
  z-index: 1;
  align-self: flex-start;
  max-height: 2.813rem;
  padding-left: 0;
  padding-right: 0;
  position: relative;
  inset: auto;
}

.svg-icon {
  width: auto;
  height: 1.5rem;
}

.svg-icon:hover {
  cursor: pointer;
}

.svg-icon.small {
  height: 1rem;
}

.image-section {
  z-index: 1;
  object-fit: cover;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 250svh;
  display: flex;
  position: absolute;
  inset: 0% 0 0 0%;
  overflow: hidden;
}

.vimeo-wrapper {
  z-index: 2;
  flex: 0 auto;
  justify-content: center;
  align-self: stretch;
  align-items: stretch;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.cdn-video {
  z-index: -999;
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  object-fit: cover;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-self: flex-end;
  width: 100%;
  height: 100%;
  padding-bottom: 0%;
  display: block;
  position: absolute;
}

.section_testimonial1 {
  background-color: var(--section-background);
}

.testimonial1_content {
  text-align: left;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  display: flex;
}

.testimonial1_logo {
  max-height: 3.5rem;
}

.testimonial1_client {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.testimonial1_client-image {
  object-fit: cover;
  border-radius: 100%;
  width: 4rem;
  min-width: 4rem;
  height: 4rem;
  min-height: 4rem;
}

.layout367_grid-list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
}

.layout367_row {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
}

.layout367_card-small-content {
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  align-items: flex-start;
  padding: 3rem;
  display: flex;
}

.layout367_card-small-content-top {
  flex-flow: column;
  display: flex;
}

.layout367_card-small-content-top.small-content-contact {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
}

.layout367_card-small-content-top.footer-logo-grid {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-flow: row;
  justify-content: space-between;
}

.layout367_card-large {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  background-color: #f2f2f2;
  border: 0 #000;
  border-radius: .5rem;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  display: flex;
  overflow: hidden;
}

.layout367_card-large.card-mobile {
  display: none;
}

.layout367_card-large-content {
  flex-flow: row;
  flex: 1;
  justify-content: space-between;
  align-items: flex-start;
  padding: 3rem;
  display: flex;
}

.layout367_card-large-content-top {
  flex-flow: column;
  flex: 0 auto;
  justify-content: center;
  align-items: flex-start;
  display: flex;
}

.layout367_card-large-footer {
  flex-flow: row;
  justify-content: space-between;
  align-items: flex-end;
  width: 100%;
  padding-bottom: 3rem;
  padding-left: 3rem;
  padding-right: 3rem;
  display: flex;
  position: relative;
}

.layout367_card-large-footer.desktop-large-footer {
  display: flex;
}

.layout367_card-large-footer.mobile-large-footer {
  display: none;
}

.footer13_link-list {
  text-align: left;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 100%;
  grid-auto-columns: 100%;
  justify-content: center;
  place-items: flex-start start;
  display: flex;
}

.footer13_link-list.social-link-list {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
}

.footer_link {
  color: #262626;
  cursor: pointer;
  padding-top: 0;
  padding-bottom: 0;
  font-size: 1.375rem;
  font-weight: 500;
  text-decoration: none;
  transition: color .2s cubic-bezier(.77, 0, .175, 1);
}

.footer_link:hover {
  color: #727272;
}

.footer_link.footer-social-link {
  font-size: 1.375rem;
}

.footer_link.footer-social-link:hover {
  color: #000;
}

.footer-contact-text {
  color: #262626;
  margin-bottom: 0;
  font-size: 1.25rem;
  font-weight: 500;
  line-height: 1;
}

.logo-wolf-column {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  transition: opacity .2s cubic-bezier(.645, .045, .355, 1);
  display: flex;
}

.logo-wolf-column:hover {
  opacity: .65;
}

.policy-column {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  flex-flow: column;
  display: flex;
}

.policy-column.policy-mobile {
  display: none;
}

.copyright-column {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  flex-flow: column;
  display: flex;
}

.copyright-column.copyright-mobile {
  display: none;
}

.intro-headline-slogan {
  border-radius: 0;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 30rem;
  display: flex;
  overflow: hidden;
}

.after-header-headline {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  display: flex;
}

.after-header-headline.mapbox-headline {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
}

.marken-navi {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  width: 100%;
  display: flex;
}

.section_header67 {
  position: relative;
}

.section_header67.text-color-alternate {
  flex: 0 auto;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  height: 70dvh;
  margin-top: 0;
  margin-bottom: 0;
  position: relative;
  top: 0;
}

.section_header67.text-color-alternate.success-header {
  justify-content: center;
  align-items: center;
  height: 100dvh;
  display: flex;
}

.header67_background-image-wrapper {
  z-index: 0;
  background-color: var(--section-background);
  position: absolute;
  inset: 0%;
}

.image-overlay-layer {
  z-index: 1;
  background-color: #00000080;
  position: absolute;
  inset: 0%;
}

.image-overlay-layer.footer-overlay-bg {
  background-color: #0000;
  background-image: linear-gradient(#0000004a 36%, #fff0);
  display: block;
}

.header67_background-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.section-header-2 {
  overflow: hidden;
}

.header_component-2 {
  flex-direction: column;
  align-items: center;
  height: auto;
  display: flex;
  position: relative;
  overflow: visible;
}

.header_card-2 {
  z-index: 1;
  border-radius: 0;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  width: 90%;
  height: 90vh;
  margin-top: -6vh;
  margin-bottom: -5vh;
  display: flex;
  position: sticky;
  top: 5vh;
  overflow: hidden;
}

.header_card-2.text-color-alternate-2 {
  color: #fff;
  flex: 0 auto;
  justify-content: flex-end;
  align-items: flex-start;
  width: 100%;
  height: 100svh;
  max-height: 95svh;
  margin-top: 0;
  margin-bottom: 0;
  position: relative;
  top: 0;
}

.header_card-2.text-color-alternate-2.jobs-header-card {
  max-height: 60svh;
}

.header_card-2.text-color-alternate-2.success-header-card-2 {
  justify-content: center;
  align-items: center;
  max-height: 100svh;
}

.header-phone {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  background-color: var(--color);
  border-radius: .5rem;
  justify-content: center;
  align-items: center;
  padding-top: .25rem;
  padding-bottom: .25rem;
  display: none;
}

.header-phone-svg {
  color: var(--woelfli-black);
  max-width: 1.5rem;
  height: 1.5rem;
}

.cursor {
  z-index: 1000;
  opacity: 0;
  pointer-events: none;
  color: #efeeec;
  background-color: #ee9d00;
  border-radius: .25em;
  padding: .3em .75em .4em;
  font-size: 1em;
  transition: opacity .2s;
  position: fixed;
  inset: 0% auto auto 0%;
}

.cursor-paragraph {
  margin-top: 0;
  margin-bottom: 0;
}

.button-row {
  grid-column-gap: .75em;
  grid-row-gap: .75em;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  padding-left: .25em;
  padding-right: .25em;
  display: none;
}

.button-text {
  z-index: 1;
  color: var(--woelfli-black);
  margin-top: 0;
  margin-bottom: 0;
  position: relative;
}

.button-bg {
  z-index: 0;
  background-color: #efeeec;
  border-radius: .5em;
  width: 100%;
  height: 100%;
  transition: transform .5s cubic-bezier(.625, .05, 0, 1);
  position: absolute;
  inset: 0%;
}

.phone-svg {
  z-index: 2;
  position: relative;
}

.lenis-script {
  display: none;
}

.splide-slider-div {
  cursor: grab;
}

.header-gradient {
  z-index: 3;
  background-image: linear-gradient(#071b1e, #d5d0b300 38% 49%);
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
}

.header-gradient.landing-gradient {
  z-index: 2;
  opacity: .64;
  background-color: #1f2839;
  background-image: none;
  display: block;
  top: 0;
}

.header-gradient.landing-gradient.kontakt-gradient {
  background-image: linear-gradient(349deg, #243848d1 11%, #071b1e14 51%, #1f2839);
}

.header-gradient.landing-gradient.kontakt-gradient-2 {
  background-image: linear-gradient(349deg, #d5d0b300, #071b1eb5 17%, #1f2839);
}

.header-gradient.project-gradient {
  z-index: 1;
  opacity: .8;
  background-image: linear-gradient(#161e25b0, #0000008c 87%);
  top: 0;
}

.header-gradient.image-overlay.leistungen {
  background-image: linear-gradient(#071b1e73, #d5d0b300 38% 49%);
}

.header-gradient.leistungen-gradient {
  z-index: 2;
  opacity: .55;
  background-image: linear-gradient(349deg, #d5d0b300, #071b1ecc 21%, #1f28398f);
  display: block;
  top: 0;
}

.header-gradient.leistungen-gradient.kontakt-gradient {
  background-image: linear-gradient(349deg, #243848d1 11%, #071b1e14 51%, #1f2839);
}

.office-map_list {
  display: block;
}

.office-map_item {
  color: #000;
  display: none;
}

.office-map_card-wrap {
  z-index: 5;
  background-color: var(--color);
  border-radius: 5px;
  align-items: center;
  min-width: 25rem;
  max-width: 25rem;
  height: auto;
  position: relative;
  overflow: scroll;
  box-shadow: 1px 1px 3px #00000026;
}

.office-map_card-text-wrap {
  padding: 1.5rem 1rem;
}

.office-map_card {
  display: none;
}

.button-2 {
  z-index: 99;
  background-color: var(--color);
  color: var(--woelfli-black);
  cursor: pointer;
  border-radius: 3px;
  transition: background-color .2s cubic-bezier(.77, 0, .175, 1);
  position: relative;
  inset: auto auto 4% 4%;
}

.button-2:hover {
  background-color: #ced2dd;
}

.map-highlight-buttons {
  z-index: 17;
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  display: flex;
  position: absolute;
  inset: auto auto 3% 3%;
}

.cta-button {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  cursor: pointer;
  background-color: #fff;
  border-radius: 3px;
  justify-content: center;
  align-items: center;
  max-width: max-content;
  padding: .75rem 1.25rem;
  transition-property: background-color;
  transition-duration: .2s;
  transition-timing-function: ease;
  display: flex;
}

.cta-button:hover {
  background-color: #e5e5e5;
}

.cta-button.cta-secondary {
  background-color: #f5f5f5;
}

.cta-button.cta-secondary:hover {
  background-color: #ececec;
}

.header-intro {
  color: var(--woelfli-grau);
  display: flex;
}

.header-intro-wrapper, .grid-header {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  display: flex;
}

.card-row4_component {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  border: 0 #333;
  border-radius: 6px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  padding: 0;
  display: grid;
}

.card-row4_card {
  background-color: var(--color);
  border: 1px solid #999;
  border-radius: 6px;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  display: flex;
  position: relative;
  overflow: hidden;
}

.card-row4_card.propertylab-card {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.card-row4_card.propertylab-card:hover {
  background-color: #d5ecf3;
}

.card-row4_card.coreal-card {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.card-row4_card.coreal-card:hover {
  background-color: #fc736178;
}

.card-row4_card.conexpool-card {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.card-row4_card.conexpool-card:hover {
  color: var(--color);
  background-color: #6060604f;
}

.card-row4_card.woelfli-card {
  background-color: var(--section-background);
  border-style: none;
  border-radius: 6px;
}

.card-row4_card.projekte-card {
  background-color: var(--color);
  border-style: none;
  border-radius: 6px;
}

.card-row4_card.initiativ-card {
  background-color: var(--section-background);
  border-style: none;
  border-radius: 6px;
}

.card-row4_card.job-card {
  background-color: var(--color);
  border-style: none;
  border-radius: 6px;
}

.card-row4_card-content {
  z-index: 2;
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  padding: 3rem;
  display: flex;
  position: relative;
}

.marken-card-logo {
  flex: 0 auto;
  max-width: 100%;
  height: 3.2rem;
  max-height: 3.2rem;
  position: relative;
}

.div-block-6 {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  border: 1px #777;
  border-radius: 6px;
  flex-flow: column;
  padding: 0;
  display: flex;
}

.project-header {
  position: relative;
  overflow: hidden;
}

.project-header-content {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: auto;
  display: flex;
  position: relative;
  overflow: hidden;
}

.project-header-wrapper {
  z-index: 1;
  border-radius: 0;
  flex-flow: row;
  justify-content: center;
  align-items: center;
  display: block;
  position: relative;
  top: auto;
  overflow: hidden;
}

.project-header-image {
  z-index: 0;
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
  inset: 0%;
}

.project-header-img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.button-wrapper {
  z-index: 22;
  display: flex;
  position: relative;
}

.button-wrapper.back-button-header {
  display: none;
}

.project-back-button {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  color: var(--woelfli-blaugrau);
  text-align: center;
  background-color: #fff;
  border: 1px #000;
  border-radius: 8px;
  justify-content: flex-start;
  align-items: center;
  padding: .75rem 1rem;
  text-decoration: none;
  display: flex;
  position: relative;
}

.project-back-button.is-icon {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.header5_content {
  z-index: 2;
  flex-flow: row;
  justify-content: flex-start;
  align-items: stretch;
  min-height: 100svh;
  max-height: 60rem;
  display: flex;
  position: relative;
}

.header5_content.project-header-content {
  z-index: 22;
}

.project-header-navigation {
  width: 100%;
  display: none;
}

.header114_content-left {
  z-index: 3;
  flex-direction: column;
  justify-content: center;
  display: flex;
  position: relative;
}

.project-header-nav {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  flex-flow: row;
  grid-template-rows: auto;
  grid-template-columns: repeat(auto-fit, minmax(min(100%, 15rem), 1fr));
  grid-auto-columns: 1fr;
  justify-content: space-between;
  align-items: flex-end;
  display: grid;
}

.project-header-copy {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  height: 100%;
  display: grid;
}

.next-project-link {
  background-color: #fff;
  border-radius: 6px;
  flex-flow: column;
  flex: 1;
  padding: 1.25rem;
  display: flex;
  position: relative;
}

.link-block {
  max-width: 20rem;
}

.arrow-line {
  justify-content: space-between;
  display: flex;
}

.project-intro {
  background-color: var(--woelfli-lightgrey);
  color: var(--color);
}

.project-intro-content {
  color: var(--woelfli-grau);
  max-width: 60rem;
  display: flex;
}

.project-data {
  background-color: var(--woelfli-lightgrey);
  border-top: 1px #000;
  border-bottom: 1px #000;
}

.project-data-component {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  display: flex;
}

.project-data-content {
  grid-column-gap: 5rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: start;
  display: flex;
}

.project-data-list {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  padding-top: .5rem;
  padding-bottom: .5rem;
  display: grid;
}

.layout66_item {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  color: var(--woelfli-blaugrau);
  display: flex;
}

.layout66_item-icon-wrapper {
  flex: none;
  align-self: flex-start;
  display: none;
}

.flickity-slider-group {
  width: 100%;
  position: relative;
}

.flickity-list {
  width: 100%;
  display: flex;
}

.flickity-item {
  width: calc((99.99% / var(--flick-col))  - (var(--flick-gap) * ((var(--flick-col)  - 1) / var(--flick-col))));
  margin-right: var(--flick-gap);
  flex-shrink: 0;
  height: auto;
}

.slider-card {
  grid-column-gap: 0em;
  grid-row-gap: 0em;
  border-radius: 1.5em;
  flex-flow: column;
  width: 100%;
  height: auto;
  padding: 1em 0 1.5em;
  display: flex;
  position: relative;
}

.slider-card-image {
  aspect-ratio: 2 / 1.5;
  background-color: #e2e1df;
  border-radius: .5em;
  justify-content: center;
  align-items: center;
  width: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.before__125 {
  pointer-events: none;
  padding-top: 100%;
}

.slider-card-icon-optional {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 5em;
  font-weight: 500;
  line-height: 1;
  display: none;
  position: absolute;
}

.flickity-controls {
  pointer-events: none;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
}

.flickity-arrows {
  pointer-events: none;
  justify-content: space-between;
  align-items: center;
  width: calc(100% + 3em);
  display: flex;
  position: relative;
}

.flickity-arrow {
  pointer-events: auto;
  color: #efeeec;
  cursor: pointer;
  background-color: #131313;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 3em;
  height: 3em;
  padding-left: .75em;
  padding-right: .75em;
  display: flex;
}

.flickity-arrow.is--flipped {
  transform: scaleX(-1);
}

.flickity-dots {
  width: 100%;
  padding-top: 4em;
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
}

.flickity-dots-list {
  grid-column-gap: .75em;
  grid-row-gap: .75em;
  justify-content: center;
  align-items: center;
  display: flex;
}

.flickity-dot {
  pointer-events: auto;
  cursor: pointer;
  background-color: #d0cfcd;
  border-radius: 50%;
  width: .75em;
  height: .75em;
}

.project-img-slider {
  overflow: hidden;
}

.flickity-slider-img {
  aspect-ratio: 3 / 2;
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.full-width-img-p {
  flex-direction: column;
}

.header146_image {
  box-sizing: border-box;
  aspect-ratio: 16 / 9;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.header146_image.project-img-adjust {
  object-position: 50% 14%;
}

.header146_image.image-position-1 {
  object-position: 50% 92%;
}

.testimonial-name {
  flex-flow: column;
  display: flex;
}

.section_testimonial2 {
  background-color: var(--color);
}

.before-after {
  background-color: var(--woelfli-lightgrey);
}

.before-after-outer {
  justify-content: center;
  align-items: center;
  height: auto;
  display: block;
}

.before-after-wrap {
  aspect-ratio: 16 / 9;
  border-radius: 12px;
  width: 100%;
  height: auto;
  position: relative;
  overflow: hidden;
}

.dragger {
  z-index: 2;
  justify-content: space-between;
  align-items: center;
  width: 1px;
  height: 100%;
  display: flex;
  position: absolute;
  inset: 0% auto 0% 50%;
}

.img-wrap {
  aspect-ratio: 16 / 9;
  width: 100%;
  position: absolute;
  inset: 0%;
  overflow: hidden;
}

.dragger-inner {
  justify-content: center;
  align-items: center;
  width: .75rem;
  height: .75rem;
  display: none;
  position: absolute;
}

.comparison-img {
  aspect-ratio: auto;
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
}

.splitter_handle-wrapper {
  cursor: pointer;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.splitter_handle-line {
  background-color: var(--color);
  width: 2px;
  height: 47%;
}

.splitter_handle-circle {
  border-style: solid;
  border-width: 1px;
  border-radius: 2rem;
  width: 2.6875rem;
  height: 2.6875rem;
}

.splitter_handle-circle.is-1 {
  grid-column-gap: 6px;
  grid-row-gap: 6px;
  background-color: var(--woelfli-grau);
  border-width: 2px;
  justify-content: center;
  align-items: center;
  display: flex;
}

.splitter_handle-circle.is-1:active {
  border-width: 4px;
}

.arrow-image {
  width: 9.14px;
  height: 18.28px;
}

.project-end {
  display: none;
}

.demo-header {
  background-color: #000;
  padding-top: 25vh;
  padding-bottom: 10vh;
}

.demo-section__title {
  max-width: 6em;
  font-size: 7em;
  font-weight: 500;
  line-height: 1;
}

.sticky-tab-group {
  overflow: clip;
}

.sticky-tab-group__nav-bg {
  z-index: 2;
  background-color: var(--color);
  border-bottom: 1px solid #ffffff26;
  width: 100%;
  height: 6em;
  display: none;
  position: sticky;
  top: 0;
  left: 0;
}

.sticky-tab {
  background-color: #131313;
  position: relative;
  overflow: clip;
}

.sticky-tab__sticky {
  z-index: 1;
  top: calc(var(--nav-height)  - 1px);
  flex-flow: column;
  margin-top: -1px;
  display: flex;
  position: sticky;
  box-shadow: 0 .25em .5em 0 #00000040;
}

.sticky-tab__inner {
  background-color: #000;
  border-top: 1px solid #ffffff26;
  border-bottom: 1px solid #ffffff26;
  justify-content: space-between;
  align-items: center;
  padding-top: 2em;
  padding-bottom: 2em;
}

.sticky-tab__content {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.sticky-tab__title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 2.25em;
  font-weight: 500;
  line-height: 1;
}

.sticky-tab__placeholder-content {
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.osmo-icon-svg {
  width: 8em;
}

.header-content {
  width: 100%;
  margin-top: 5.5rem;
  margin-left: auto;
  margin-right: auto;
}

.portfolio-expose {
  color: var(--woelfli-black);
  margin-bottom: 0;
  font-size: 1.375rem;
  font-weight: 300;
  line-height: 1.54;
}

.svg-3 {
  color: #000;
}

.svg-3.handwriting-svg {
  display: none;
}

.after-intro {
  background-color: var(--color);
  color: var(--color);
}

.after-intro.success-intro {
  z-index: 22;
  border-radius: 6px;
  margin-left: 20rem;
  margin-right: 20rem;
  position: relative;
}

.after-intro-content {
  color: var(--woelfli-grau);
  display: flex;
}

.after-intro-content.marken-intro {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-flow: row;
  justify-content: space-between;
  align-items: center;
}

.after-intro-content.success-content {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  flex-flow: column;
  justify-content: flex-start;
  align-items: center;
}

.logo-image {
  height: 100%;
  min-height: 100%;
  max-height: 100%;
  display: block;
  overflow: clip;
}

.card-top {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-flow: column;
  display: flex;
}

.card-top.woelfli-logo-top, .card-top.initiativ-top {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  flex-flow: row;
}

.before-footer-content {
  z-index: 1;
  position: relative;
}

.footer-email {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  cursor: pointer;
  justify-content: flex-start;
  align-items: center;
  transition: opacity .2s cubic-bezier(.645, .045, .355, 1);
  display: flex;
}

.footer-email:hover {
  opacity: .65;
}

.footer-phone {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  cursor: pointer;
  flex: 0 auto;
  justify-content: flex-start;
  align-items: center;
  transition: opacity .2s cubic-bezier(.77, 0, .175, 1);
  display: flex;
}

.footer-phone:hover {
  opacity: .65;
}

.footer-link-icon {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  transition: opacity .2s;
  display: flex;
}

.footer-link-icon:hover {
  opacity: .65;
}

.footer-icon {
  max-width: 1.25rem;
}

.footer-column {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: row;
  display: flex;
}

.footer-contact-icon {
  height: 2rem;
}

.navbar-icon-link {
  justify-content: center;
  align-items: center;
  margin-left: 1rem;
  padding-left: 0;
  display: flex;
  overflow: hidden;
}

.layout497_component {
  background-color: #fff;
  border: 1px solid #777;
  border-radius: 8px;
  padding: 3rem;
}

.layout497_content {
  flex-flow: column;
  display: flex;
  position: relative;
}

.layout497_inner-wrapper {
  width: 50%;
  padding-right: 2.5rem;
}

.layout497_header {
  margin-bottom: 2rem;
}

.layout497_tabs {
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-items: stretch;
  position: static;
}

.layout497_tabs-content {
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 50%;
  height: 100%;
  padding-left: 0;
  display: flex;
  position: absolute;
  inset: 0% 0% 0% auto;
  overflow: visible;
}

.layout497_tab-pane {
  border-radius: 10px;
  flex: 1;
}

.layout497_image-wrapper {
  border-radius: 10px;
  height: 100%;
  overflow: hidden;
}

.layout497_image {
  box-sizing: border-box;
  aspect-ratio: auto;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.layout497_image.img-position {
  object-position: 0% 50%;
}

.layout497_lightbox {
  border-radius: var(--\<unknown\|relume-variable-radius-large\>);
  justify-content: center;
  align-items: center;
  width: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
}

.layout497_lightbox-image {
  aspect-ratio: 1;
  object-fit: cover;
  width: 100%;
}

.video-overlay-layer {
  z-index: 1;
  background-color: #00000080;
  position: absolute;
  inset: 0%;
}

.layout497_tabs-menu {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  grid-template-rows: auto auto auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.layout497_tab-link {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  border-bottom: 0px none var(--\<unknown\|relume-variable-color-scheme-1-border\>);
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-radius: 5px;
  justify-content: flex-start;
  align-items: center;
  padding: 3rem;
  display: flex;
}

.layout497_tab-link.w--current {
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-left-color: #000;
  border-radius: 5px;
}

.layout497_tab-title {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  color: var(--color);
  flex-flow: column;
  flex: 1;
  display: flex;
}

.layout497_paragraph {
  overflow: hidden;
}

.image-3 {
  aspect-ratio: auto;
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.section_layout433 {
  background-color: var(--\<unknown\|relume-variable-color-scheme-1-background\>);
}

.layout433_content {
  grid-column-gap: 4rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: .5fr 1fr;
  grid-auto-columns: 1fr;
  align-items: start;
  display: grid;
}

.layout433_content-left {
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  height: 100%;
  display: flex;
}

.layout433_content-top {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  display: flex;
}

.layout433_image-group {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr .75fr;
  grid-auto-columns: 1fr;
  align-items: start;
  display: grid;
}

.layout433_image-wrapper {
  border-radius: 5px;
  width: 100%;
  overflow: hidden;
}

.layout433_image1 {
  aspect-ratio: 2 / 3;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.layout433_image2 {
  aspect-ratio: 1;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.team8_list {
  grid-column-gap: 2rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.team8_image-wrapper {
  border-radius: 10px;
  width: 100%;
  overflow: hidden;
}

.team8_image {
  aspect-ratio: 1 / 1.15;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.team8_social {
  grid-column-gap: .875rem;
  grid-row-gap: .875rem;
  grid-template-rows: auto;
  grid-template-columns: max-content;
  grid-auto-columns: 1fr;
  grid-auto-flow: column;
  align-self: flex-start;
  display: flex;
}

.social-link {
  color: #243848;
  text-decoration: underline;
}

.slider-full-width {
  position: relative;
}

.gallery13_component {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  place-items: start stretch;
}

.gallery13_slider {
  background-color: #0000;
  border-radius: 4px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 100%;
  overflow: hidden;
}

.slider15_arrows {
  z-index: 3;
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.875rem;
  padding-bottom: 0;
  padding-right: 1.875rem;
  display: flex;
  position: absolute;
  inset: auto 0% 2rem;
}

.slider-prev {
  background-color: #fff;
  border: 0 #000;
  border-radius: 5px;
  padding: 1rem 1.8rem;
}

.slider-prev.next-button {
  border-radius: 999rem;
  justify-content: center;
  align-items: center;
  padding: .75rem;
  display: flex;
}

.icon-embed-small {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 2rem;
  height: 2rem;
  display: flex;
}

.icon-embed-small.expand-icon {
  display: none;
}

.icon-embed-small.intro-expand-icon {
  color: var(--color);
  display: flex;
}

.icon-embed-small.fortschritt-expand-icon {
  color: var(--woelfli-black);
  display: flex;
}

.slider-next {
  background-color: #fff;
  border: 0 #000;
  border-radius: 999rem;
  justify-content: center;
  align-items: center;
  padding: .75rem;
  display: flex;
}

.gallery13_mask {
  border-radius: 5px;
  height: 100%;
}

.slider-inner-content {
  border-radius: 5px;
  flex-flow: column;
  justify-content: space-between;
  align-items: stretch;
  min-height: 79vh;
  display: flex;
  position: relative;
  overflow: hidden;
}

.slider-background-image {
  z-index: 0;
  background-image: linear-gradient(#00000080, #00000080);
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.slider-overlay-layer {
  z-index: 1;
  background-color: #cfcfcf33;
  width: 100%;
  height: 100%;
  display: none;
  position: absolute;
  inset: 0%;
}

.slider-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.gallery13_arrow {
  border-style: solid;
  border-width: 1px;
  border-radius: 100%;
  justify-content: center;
  align-items: center;
  width: 3.5rem;
  height: 3.5rem;
  display: flex;
}

.gallery13_arrow.hide-mobile-landscape.hide {
  display: none;
}

.gallery13_arrow-icon {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 1rem;
  height: 1rem;
  display: flex;
}

.gallery13_slide-nav {
  height: 1.75rem;
  margin-bottom: 2rem;
  font-size: .5rem;
  display: none;
}

.layout358_grid-list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  position: relative;
}

.layout358_card {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  aspect-ratio: auto;
  background-color: #fff;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  grid-auto-flow: row;
  place-items: stretch stretch;
  display: grid;
  position: relative;
  overflow: hidden;
}

.layout358_card-content {
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  height: 100%;
  min-height: 100%;
  padding: 3rem 3rem 3rem 0;
  display: flex;
  overflow: visible;
}

.layout358_image-wrapper {
  aspect-ratio: 2.39;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 100%;
  display: flex;
}

.layout358_image {
  aspect-ratio: 1;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.team-card {
  background-color: #fff;
}

.layout294_list {
  grid-column-gap: 3rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: start;
  display: grid;
}

.layout294_item {
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  display: flex;
}

.marken-section {
  color: var(--color);
}

.layout359_component {
  border-radius: 10px;
}

.layout359_grid-list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
}

.layout359_card {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  border: 1px solid #999;
  border-radius: 6px;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  display: grid;
  overflow: hidden;
}

.layout359_card.conexpool-card {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.layout359_card.conexpool-card:hover {
  background-color: #6060604f;
}

.layout359_card.propertylab-hover {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.layout359_card.propertylab-hover:hover {
  background-color: #d5ecf3;
}

.layout359_card.coreal-marke {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.layout359_card.coreal-marke:hover {
  background-color: #fc736178;
}

.layout359_card-content {
  grid-column-gap: 5rem;
  grid-row-gap: 5rem;
  flex-direction: column;
  justify-content: center;
  padding: 3rem;
  display: flex;
}

.layout359_card-content-top {
  flex: 1;
  height: auto;
}

.layout396_grid-list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
}

.layout396_grid-list.expand-padding {
  padding-top: 5rem;
}

.layout396_row {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
}

.layout396_row._2-columns {
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
}

.layout396_card {
  background-color: var(--section-background);
  border-radius: 9px;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  display: flex;
  overflow: hidden;
}

.layout396_card-content {
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  padding: 2rem;
  display: flex;
}

.layout396_card-content-top {
  flex-flow: column;
  display: flex;
}

.div-block-8 {
  height: auto;
  display: flex;
}

.marken-card-description {
  border-left: 1px #bdbdbd;
  flex: 1;
  justify-content: flex-start;
  display: flex;
}

.marken-card-divider {
  background-color: #999;
  width: 1px;
  margin-left: 4rem;
  margin-right: 4rem;
}

.kraft-content-right {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  flex: 0 auto;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  align-items: start;
  width: 10.2rem;
  max-width: 9rem;
  display: flex;
}

.kraft-content-left {
  flex-direction: column;
  flex: 1;
  justify-content: flex-start;
  align-items: stretch;
  width: 100%;
  height: 100%;
  display: flex;
}

.kraft-content {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  grid-template-rows: auto;
  grid-template-columns: .5fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: space-between;
  align-items: start;
  display: flex;
}

.filter-group {
  min-height: 100vh;
  padding-bottom: 5em;
}

.filter-buttons {
  grid-column-gap: .5em;
  grid-row-gap: .5em;
  flex-flow: wrap;
  justify-content: flex-start;
  padding: 1em 0;
  display: flex;
}

.filter-buttons.last {
  padding-bottom: 3em;
}

.filter-btn {
  color: #000;
  -webkit-appearance: none;
  appearance: none;
  background-color: #efeeec00;
  border: 1px solid #000;
  border-radius: .25em;
  flex-flow: row-reverse;
  justify-content: flex-start;
  align-items: center;
  max-height: 3.125rem;
  padding: .8rem 1.25em;
  transition-property: background-color;
  transition-duration: .2s;
  transition-timing-function: ease;
  display: flex;
}

.filter-btn:hover {
  background-color: #fff;
}

.reset-btn {
  outline-offset: -2px;
  color: #000;
  -webkit-appearance: none;
  appearance: none;
  background-color: #c90f0f0d;
  border-radius: 10px;
  outline-style: none;
  outline-color: #c90f0f00;
  padding: .25em 1.25em;
  font-family: Overused Grotesk, Arial, sans-serif;
  font-size: 1.25rem;
  line-height: 1;
}

.filter-list {
  flex-flow: wrap;
  width: 100%;
  display: flex;
}

.filter-list__item {
  width: 50%;
  padding: .75em;
}

.project-card {
  grid-column-gap: 1em;
  grid-row-gap: 1em;
  background-color: #efeeec00;
  border-radius: 6px;
  flex-flow: column;
  justify-content: flex-end;
  align-items: stretch;
  width: 100%;
  min-height: 30rem;
  padding: 1em;
  display: flex;
  position: relative;
  overflow: hidden;
}

.project-card__top {
  display: none;
}

.project-card__visual {
  background-color: #e2dfdf;
  border-radius: .25em;
  justify-content: center;
  align-items: center;
  width: 100%;
  display: flex;
  position: relative;
}

.project-card__visual-before {
  padding-top: 66%;
}

.project-card__bottom {
  z-index: 2;
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  background-color: var(--color);
  border-radius: 6px;
  flex-flow: column;
  justify-content: center;
  align-items: flex-start;
  padding: 1.5rem 3em 1.5em;
  display: flex;
  position: relative;
}

.project-card__h3 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.25em;
  font-weight: 500;
  line-height: 1;
}

.project-card-image {
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0% auto auto 0%;
}

.project-card-img {
  width: 100%;
  height: 100%;
  max-height: 100%;
}

.project-card-img.project-card-background {
  z-index: 1;
  object-fit: cover;
  position: absolute;
  inset: 0% auto auto 0%;
}

.thin-headline {
  font-weight: 300;
}

.after-projects-grid {
  background-color: var(--\<unknown\|relume-variable-color-scheme-1-background\>);
}

.after-projects-content {
  grid-column-gap: 4rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: start;
  display: grid;
}

.after-projects-content-left {
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  height: 100%;
  display: flex;
}

.after-projects-content-top {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  display: flex;
}

.after-project-image-group {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr .75fr;
  grid-auto-columns: 1fr;
  align-items: start;
  display: grid;
}

.after-projects-image-wrapper {
  border-radius: 5px;
  width: 100%;
  overflow: hidden;
}

.layout128_content {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: flex-start;
  place-items: stretch center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
}

.layout128_item {
  grid-column-gap: .25rem;
  grid-row-gap: .25rem;
  border: 1px #000;
  border-radius: 3px;
  flex-flow: column;
  flex: 1;
  padding: 0 1.563rem;
  display: flex;
}

.layout128_item-icon-wrapper {
  display: inline-block;
}

.kontakt-intro-wrapper {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  flex-flow: column;
  display: flex;
}

.contact-content-block {
  grid-column-gap: .25rem;
  grid-row-gap: .25rem;
  flex-flow: column;
  display: flex;
}

.contact-block-icon {
  grid-template-rows: auto auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: flex;
}

.contact-blocks {
  background-color: var(--section-background);
  color: var(--color);
}

.layout19_content {
  grid-column-gap: 5rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: center;
  display: grid;
}

.contact-block-wrap {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-flow: wrap;
  display: flex;
}

.layout19_image-wrapper {
  border-radius: 6px;
  overflow: hidden;
}

.layout19_image {
  aspect-ratio: 2;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.contact-block-paragraph.wolfli-address {
  flex-flow: column;
  display: flex;
}

.contact-block-top {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-flow: column;
  display: flex;
}

.card-row39_component {
  z-index: 2;
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
  position: relative;
}

.card-row39_card {
  background-color: #fff;
  border-radius: 6px;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  display: flex;
}

.card-row39_card-content {
  grid-column-gap: 2.5rem;
  grid-row-gap: 2.5rem;
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  padding: 2rem;
  display: flex;
}

.card-row39_card-content-top {
  grid-column-gap: 2.5rem;
  grid-row-gap: 2.5rem;
  flex-flow: column;
  display: flex;
}

.list1_component {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  padding-top: .5rem;
  padding-bottom: .5rem;
  display: none;
}

.list1_item {
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  display: flex;
  position: relative;
}

.list1_item.map-item {
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 1rem;
}

.layout65_item-icon-wrapper {
  flex: none;
  align-self: flex-start;
  margin-top: .4rem;
  margin-right: 1rem;
}

.layout65_item-text-wrapper {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  word-break: normal;
  display: flex;
}

.icon-card-symbol {
  display: flex;
  position: relative;
}

.icon-page-bg-card {
  position: relative;
}

.icon-page-intro {
  background-color: var(--color);
  color: var(--color);
}

.icon-page-marken {
  border-radius: 10px;
}

.stats14_component {
  grid-column-gap: 5rem;
  grid-row-gap: 5rem;
  flex-flow: column;
  display: flex;
}

.stats14_content {
  grid-column-gap: 5rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: center;
  display: grid;
}

.stats14_item-list {
  grid-column-gap: 2rem;
  grid-row-gap: 3rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  padding-top: 0;
  padding-bottom: 0;
}

.stats14_item {
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding-left: 0;
}

.stats14_number {
  font-size: 5rem;
  font-weight: 700;
  line-height: 1.3;
}

.section_stats14 {
  background-color: var(--\<unknown\|relume-variable-color-scheme-1-background\>);
  color: var(--\<unknown\|relume-variable-color-scheme-1-text\>);
}

.headline-spacing {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  display: flex;
}

.tab-title {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.wolf-symbol-footer {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  max-width: 3rem;
  display: flex;
}

.wolf-symbol-footer.wolf-mobile-2 {
  display: none;
}

.marken-card-logo_wide {
  flex: 1;
  max-width: 100%;
  height: 4rem;
  max-height: 4rem;
  position: relative;
}

.stark-section {
  background-color: #fff;
  border-top: 0 #bfbfbf;
}

.link-block-3 {
  cursor: pointer;
}

.policy-link:hover {
  text-underline-position: under;
  -webkit-text-decoration: underline #000;
  text-decoration: underline #000;
}

.footer-logo-link.w--current {
  cursor: pointer;
  transition: opacity .2s cubic-bezier(.77, 0, .175, 1);
}

.footer-logo-link.w--current:hover {
  opacity: .65;
}

.footer-icon-link {
  transition: opacity .2s cubic-bezier(.77, 0, .175, 1);
}

.footer-icon-link:hover {
  opacity: .65;
}

.link-block-4 {
  cursor: pointer;
}

.layout364_grid-list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
}

.layout364_row {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  place-items: start stretch;
  height: auto;
  display: grid;
}

.layout364_card {
  cursor: pointer;
  border: 1px solid #999;
  border-radius: 6px;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
  display: flex;
  overflow: hidden;
}

.layout364_card:hover {
  background-color: var(--section-background);
}

.layout364_card-content {
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  height: auto;
  padding: 3rem;
  display: flex;
}

.layout364_card-content-top {
  flex-flow: column;
  height: auto;
  display: flex;
  position: relative;
}

.headline-span {
  font-weight: 400;
}

.section-leistungen {
  background-color: var(--color);
  color: var(--color);
}

.header-h1-span {
  font-weight: 400;
}

.moving-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.headline-overlay {
  z-index: 3;
  background-image: linear-gradient(5deg, #243848a8, #d5d0b300 38% 49%);
  width: 100%;
  height: 100%;
  display: block;
  position: absolute;
}

.team-section {
  background-color: var(--section-background);
  border-top: 1px #d3d1e3;
}

.intro-akkordion-box {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  border-bottom: 0px none var(--\<unknown\|relume-variable-color-scheme-1-border\>);
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-radius: 5px;
  justify-content: flex-start;
  align-items: center;
  padding: 3rem;
  display: flex;
}

.intro-akkordion-box.w--current {
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-left-color: #000;
  border-radius: 5px;
}

.intro-akkordion-box.know-how-box {
  cursor: pointer;
  background-color: #8784847a;
  justify-content: flex-start;
  height: auto;
}

.intro-akkordion-box.akkordion-anim {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
  cursor: pointer;
  background-color: #8784847a;
  justify-content: flex-start;
  height: auto;
}

.intro-akkordion-title {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  color: var(--color);
  flex-flow: column;
  flex: 1;
  display: flex;
}

.akkordion-title {
  color: var(--color);
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.intro-akkordion-p {
  overflow: hidden;
}

.contact-link:hover {
  text-underline-position: under;
  -webkit-text-decoration: underline #000;
  text-decoration: underline #000;
}

.marken-expand-content {
  border-radius: 6px;
}

.marken-expand-content.propertylab-marke {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.marken-expand-content.propertylab-marke:hover {
  background-color: #d5ecf3;
}

.marken-expand-content.coreal-marke {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.marken-expand-content.coreal-marke:hover {
  background-color: #fc736178;
}

.marken-expand-content.connexpool-marke {
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
}

.marken-expand-content.connexpool-marke:hover {
  background-color: #6060604f;
}

.career9_accordion {
  border: 1px solid #999;
  border-radius: 6px;
  padding: 3rem;
  overflow: hidden;
}

.career9_job-department {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  cursor: pointer;
  justify-content: space-between;
  align-items: flex-start;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
}

.career9_icon-wrapper {
  color: var(--woelfli-black);
  justify-content: center;
  align-self: flex-start;
  align-items: center;
  width: 2rem;
  display: flex;
}

.career9_list-wrapper {
  overflow: hidden;
}

.logo-column {
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  flex-flow: column;
  flex: 1;
  max-width: 100%;
  display: flex;
}

.leistungen-expand {
  border-radius: 6px;
}

.event-header3_content {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  padding-top: 3rem;
  display: grid;
}

.event-header3_featured-list-wrapper {
  background-color: var(--section-background);
  border-radius: 6px;
}

.event-header3_featured-list, .event-header3_featured-item {
  border-radius: 6px;
  height: 100%;
  overflow: hidden;
}

.event-header3_featured-image-wrapper {
  border-radius: 6px;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.event-header3_featured-image {
  aspect-ratio: 3 / 2;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.event-header3_list-wrapper {
  overflow: hidden;
}

.event-header3_list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  border-bottom: var(--\<unknown\|relume-variable-divider-width\>) solid var(--\<unknown\|relume-variable-color-scheme-1-border\>);
  flex-flow: column;
  display: flex;
}

.event-header3_item-content {
  flex: 0 auto;
  width: 100%;
}

.leistung-list-item {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  color: var(--color);
  flex-flow: column;
  flex: 1;
  display: flex;
}

.leistung-item {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  border-bottom: 0px none var(--\<unknown\|relume-variable-color-scheme-1-border\>);
  opacity: 1;
  cursor: default;
  background-color: #f5f5f500;
  border-radius: 5px;
  justify-content: flex-start;
  align-items: center;
  padding: 0;
  display: flex;
}

.leistung-item.w--current {
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-left-color: #000;
  border-radius: 5px;
}

.leistungen-accordion {
  border: 1px solid #999;
  border-radius: 6px;
  padding: 3rem;
  overflow: hidden;
}

.leistungen-header {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  cursor: pointer;
  justify-content: space-between;
  align-items: center;
  height: auto;
  padding-top: 0;
  padding-bottom: 0;
  display: flex;
}

.cw-cookie_banner {
  z-index: 3;
  background-color: var(--section-background);
  border-radius: 6px;
  max-width: 600px;
  padding: 2rem;
  display: none;
  position: fixed;
  inset: auto 0% 20px 20px;
}

.cw-cookie_content {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  display: flex;
}

.heading-style-h3-sans {
  color: #02325f;
  letter-spacing: -.044px;
  margin-top: 0;
  font-size: 3rem;
  font-weight: 400;
  line-height: 1.14;
}

.cw-cookie_buttons {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-direction: row;
  margin-top: 2rem;
  display: flex;
}

.cw-button_secondary {
  color: var(--woelfli-grau);
  text-align: center;
  background-color: #0000;
  border: 1px solid #1e1a35;
  border-radius: 6px;
  justify-content: center;
  padding: .5rem 1.5rem;
  line-height: 1;
  transition: transform .3s, color .3s, background-color .3s;
}

.cw-button_secondary:hover {
  background-color: var(--color);
  color: #fff;
  border-color: #092935;
  transform: translate(0, -2px);
}

.cw-button_primary {
  background-color: var(--color);
  color: var(--woelfli-black);
  text-align: center;
  border-radius: 6px;
  justify-content: center;
  align-items: center;
  padding: .5rem 1.5rem;
  font-weight: 400;
  line-height: 1;
  transition: transform .3s, background-color .3s;
  display: flex;
}

.cw-button_primary:hover {
  background-color: var(--color);
  color: #fff;
  transform: translate(0, -2px);
}

.cw-cookie_selection {
  overflow: hidden;
}

.cw-cookie_options {
  grid-column-gap: 1.25rem;
  grid-row-gap: 1.25rem;
  flex-flow: column;
  justify-content: flex-start;
  align-items: flex-start;
  margin-top: 2rem;
  display: flex;
}

.cw-cookie_checkbox {
  grid-column-gap: 6px;
  grid-row-gap: 6px;
  align-items: center;
  display: flex;
}

.cw-cookie_checkbox-check {
  border-radius: 4px;
  width: 20px;
  height: 20px;
  margin-top: 0;
}

.cw-cookie_checkbox-check.w--redirected-checked {
  background-color: var(--woelfli-blaugrau);
  color: #f2ebdd;
  background-image: url('../images/custom-checkbox-checkmark.589d534424.svg');
  background-position: 50%;
  background-size: contain;
  border: 1px solid #fff;
  width: 1.25rem;
  height: 1.25rem;
  margin-left: -1.25rem;
}

.form_checkbox-label-short {
  margin-bottom: 0;
}

.form_checkbox-label-short.text-size-regular {
  margin-top: 1rem;
  font-size: 1.85rem;
}

.cw-cookie_icon {
  z-index: 99;
  cursor: pointer;
  width: 40px;
  height: 40px;
  padding: 5px;
  display: none;
  position: fixed;
  inset: auto auto 20px 20px;
}

.cw-cookie_script {
  display: none;
}

.fortschritt-p {
  padding-top: 0;
  position: relative;
  overflow: hidden;
}

.fortschrott-expand-title {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.landing-link {
  color: var(--woelfli-black);
  text-decoration: underline;
}

.leistung-intro {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  border-bottom: 0px none var(--\<unknown\|relume-variable-color-scheme-1-border\>);
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-radius: 5px;
  flex-flow: column;
  justify-content: space-between;
  align-items: stretch;
  height: 100%;
  padding: 3rem;
  display: flex;
}

.leistung-intro.w--current {
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-left-color: #000;
  border-radius: 5px;
}

.kontakt-divider {
  background-color: #999;
  flex: 0 auto;
  align-self: auto;
  width: 1px;
  height: 100%;
  min-height: 100%;
  margin-left: 0;
  margin-right: 0;
  position: relative;
}

.svg-anim {
  z-index: 999;
  max-width: 100%;
  height: 100%;
  position: relative;
}

.svg-anim-wrapper {
  justify-content: flex-start;
  align-self: flex-start;
  align-items: center;
  max-width: 10.2rem;
  height: 100%;
  display: flex;
}

.mobile-icon-expand {
  color: var(--woelfli-black);
  justify-content: center;
  align-self: flex-start;
  align-items: center;
  width: 2rem;
  display: none;
}

.leistungen-card-top {
  flex: 1;
  max-width: 100%;
  position: relative;
}

.marken-desktop-close {
  color: var(--woelfli-black);
  justify-content: center;
  align-self: flex-start;
  align-items: center;
  width: 2rem;
  display: flex;
}

.intro-boxes {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template: "."
                 "."
                 "."
                 / 1fr;
  grid-auto-columns: 1fr;
  grid-auto-flow: row;
  place-content: stretch;
  place-items: start stretch;
  display: grid;
  position: sticky;
  top: 15rem;
}

.footer_link-copy {
  color: #262626;
  cursor: pointer;
  padding-top: 0;
  padding-bottom: 0;
  font-size: 1.375rem;
  font-weight: 500;
  text-decoration: none;
  transition: color .2s cubic-bezier(.77, 0, .175, 1);
}

.footer_link-copy:hover {
  color: #727272;
}

.footer_link-copy.footer-social-link {
  font-size: 1.375rem;
}

.footer_link-copy.footer-social-link:hover {
  color: #000;
}

.imprint-link {
  color: var(--woelfli-black);
  text-decoration: underline;
}

.imprint-link:hover {
  text-underline-position: under;
  -webkit-text-decoration: underline #000;
  text-decoration: underline #000;
}

.link-text {
  color: var(--woelfli-black);
  -webkit-text-decoration-skip-ink: auto;
  text-decoration-skip-ink: auto;
  text-decoration: underline;
}

.utility-page-wrap {
  justify-content: center;
  align-items: center;
  width: 100vw;
  max-width: 100%;
  height: 100vh;
  max-height: 100%;
  display: flex;
}

.utility-page-content {
  text-align: center;
  flex-direction: column;
  width: 260px;
  display: flex;
}

.footer-column-content-top {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  display: flex;
}

.footer-logo {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  cursor: pointer;
  justify-content: flex-start;
  align-items: center;
  transition: opacity .2s cubic-bezier(.645, .045, .355, 1);
  display: flex;
}

.footer-logo:hover {
  opacity: .65;
}

.footer-logo-img {
  height: 2.5rem;
}

.footer-logos-desktop {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-flow: column;
  align-items: stretch;
  width: 100%;
  display: flex;
}

.footer-logos-mobile {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  display: none;
}

.slider {
  min-height: 75svh;
}

.right-arrow {
  visibility: hidden;
  display: none;
}

.slide-nav {
  display: none;
}

.left-arrow {
  visibility: hidden;
}

.icon-embed-custom2 {
  width: 100%;
  max-width: 100%;
  height: 100%;
  display: flex;
}

.section_career19 {
  background-color: var(--\<unknown\|relume-variable-color-scheme-1-background\>);
  color: var(--\<unknown\|relume-variable-color-scheme-1-text\>);
}

.career19_tabs {
  flex-direction: column;
  align-items: stretch;
  display: flex;
}

.category-filter-menu {
  align-items: center;
  width: 100%;
  margin-bottom: 4rem;
  display: flex;
}

.category-filter-menu.is-center {
  justify-content: center;
  align-items: center;
}

.category-filter-menu.is-center.no-scrollbar {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  justify-content: flex-start;
  align-items: center;
}

.category-filter-link {
  white-space: nowrap;
  background-color: #f5f5f5;
  border: 1px solid #f5f5f5;
  border-radius: 6px;
  flex: none;
  padding: .5rem 1rem;
  text-decoration: none;
  transition: border-color .2s;
}

.category-filter-link:hover {
  border-color: #999;
}

.category-filter-link.w--current {
  background-color: #f5f5f5;
  border: 1px solid #999;
  font-weight: 500;
}

.career19_list {
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.career19_item {
  border: 1px solid #999;
  border-radius: 6px;
  padding: 2rem;
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
  overflow: hidden;
}

.career19_item:hover {
  background-color: var(--woelfli-lightgrey);
}

.career19_top-wrapper {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.career19_title-wrapper {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  align-items: center;
  display: flex;
}

.career19_job-details-wrapper {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  display: flex;
}

.career19_detail-wrapper {
  grid-column-gap: .75rem;
  grid-row-gap: .75rem;
  align-items: center;
  display: flex;
}

.career19_icon-wrapper {
  flex: none;
}

.initiaitv-card-content {
  z-index: 2;
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  padding: 3rem;
  display: flex;
  position: relative;
}

.section_layout121 {
  display: none;
}

.layout121_content {
  grid-column-gap: 5rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: start;
  display: grid;
  overflow: visible;
}

.layout121_content-left {
  min-height: 100%;
  position: static;
  overflow: visible;
}

.layout121_timeline-wrapper {
  position: relative;
}

.layout121_progress-bar-wrapper {
  z-index: 1;
  background-color: #f5f5f5;
  width: 2px;
  height: 75%;
  margin-bottom: 5rem;
  position: absolute;
  top: 10%;
  left: 2.4375rem;
  right: auto;
}

.layout121_progress-bar {
  background-color: #000;
  width: 2px;
  height: 100%;
}

.layout121_timeline-step {
  grid-column-gap: 2.5rem;
  grid-row-gap: 0px;
  grid-template-rows: auto;
  grid-template-columns: max-content 1fr;
  place-items: stretch stretch;
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}

.layout121_timeline-left {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  display: flex;
  position: relative;
}

.layout121_timeline-icon-wrapper {
  z-index: 1;
  background-color: var(--\<unknown\|relume-variable-color-scheme-1-background\>);
  margin-top: -1.5rem;
  padding: 1rem;
  position: relative;
}

.sticke-column {
  position: sticky;
  top: 10%;
}

.stellen-sektion, .bieten-sektion {
  background-color: #fff;
  border-top: 0 #bfbfbf;
}

.bieten-list-content {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  padding-top: .5rem;
  padding-bottom: .5rem;
  display: flex;
}

.entwicklung-sektion {
  color: var(--color);
  background-color: #fff;
}

.section_layout67 {
  position: relative;
}

.layout67_component {
  z-index: 1;
  grid-column-gap: 3rem;
  grid-row-gap: 3rem;
  flex-flow: column;
  display: flex;
  position: relative;
}

.layout67_content {
  grid-column-gap: 5rem;
  grid-row-gap: 4rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: start;
  display: grid;
}

.layout67_item-list {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  padding-top: 0;
  padding-bottom: 0;
  display: grid;
}

.layout67_item {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  display: flex;
}

.layout67_item-icon-wrapper {
  flex: none;
  align-self: flex-start;
}

.layout67_background-image-wrapper {
  z-index: 0;
  background-image: linear-gradient(#00000080, #00000080);
  position: absolute;
  inset: 0%;
}

.layout67_background-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.trainee-text-block {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  flex-flow: column;
  display: flex;
}

.trainee-box {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  border-bottom: 0px none var(--\<unknown\|relume-variable-color-scheme-1-border\>);
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-radius: 5px;
  justify-content: flex-start;
  align-items: center;
  padding: 3rem;
  display: flex;
}

.trainee-box.w--current {
  opacity: 1;
  cursor: default;
  background-color: #f5f5f5;
  border-left-color: #000;
  border-radius: 5px;
}

.trainee-box.know-how-box {
  cursor: pointer;
  background-color: #8784847a;
  justify-content: flex-start;
  height: auto;
}

.trainee-box.akkordion-anim {
  z-index: 4;
  -webkit-backdrop-filter: blur(15px);
  backdrop-filter: blur(15px);
  cursor: pointer;
  background-color: #8784847a;
  justify-content: flex-start;
  height: auto;
  position: relative;
}

.trainee-box-content {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  color: var(--color);
  flex-flow: column;
  flex: 1;
  display: flex;
}

.trainee-title {
  color: var(--color);
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.trainee-box-p {
  overflow: hidden;
}

.bewerben-button-group {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  display: flex;
}

.karriere-card {
  cursor: pointer;
  border: 1px solid #999;
  border-radius: 6px;
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  transition: background-color .2s cubic-bezier(.645, .045, .355, 1);
  display: flex;
  overflow: hidden;
}

.karriere-card:hover {
  background-color: var(--section-background);
}

.karriere-card-top {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  height: auto;
  display: flex;
  position: relative;
}

.karriere-card-content {
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  height: auto;
  padding: 3rem;
  display: flex;
}

.karriere-card-title {
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.karriere-card-item {
  grid-column-gap: .25rem;
  grid-row-gap: .25rem;
  border: 1px #000;
  border-radius: 3px;
  flex-flow: column;
  flex: 0 auto;
  padding: 0 1.563rem;
  display: flex;
}

.fragen-sektion {
  background-color: var(--section-background);
}

.layout408_component {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  position: sticky;
  top: 0;
}

.layout408_card {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  background-color: var(--color);
  border: 1px solid #999;
  border-radius: 8px;
  flex-flow: row;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-content: center;
  place-items: end start;
  height: 60vh;
  margin-bottom: 10vh;
  display: flex;
  position: sticky;
  top: 10%;
  overflow: hidden;
}

.layout408_card.card-1 {
  flex-flow: column;
  grid-template-columns: 1fr;
  place-content: space-around flex-end;
  place-items: flex-start start;
}

.layout408_card.card-2, .layout408_card.card-slide-1, .layout408_card.card-slide-2 {
  background-color: var(--color);
}

.layout408_card-content {
  z-index: 2;
  background-color: #fffc;
  border-radius: 8px;
  flex-flow: column;
  flex: 0 auto;
  order: 0;
  justify-content: flex-end;
  align-self: flex-end;
  align-items: flex-start;
  height: auto;
  margin-bottom: 3rem;
  margin-left: 3rem;
  padding: 3rem;
  display: flex;
  position: relative;
}

.layout408_image-wrapper {
  z-index: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60vh;
  display: flex;
  position: absolute;
}

.layout408_image {
  opacity: .51;
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.div-block-12 {
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.card-row1_component {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  background-color: var(--woelfli-grau);
  border-style: solid;
  border-width: 1px;
  border-radius: 8px;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  display: grid;
}

.card-row1_card-content {
  flex-direction: column;
  justify-content: center;
  padding: 3rem;
  display: flex;
}

.card-row1_image-wrapper {
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  display: flex;
}

.card-row16_card-small-content {
  flex-direction: column;
  flex: 1;
  justify-content: space-between;
  padding: 1.5rem;
  display: flex;
}

.karriere-grid-footer {
  grid-column-gap: 1.875rem;
  grid-row-gap: 1.875rem;
  flex-flow: column;
  max-width: 100%;
  margin-top: 0;
  margin-left: 0;
  margin-right: 0;
  display: flex;
}

.layout526_grid-list {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
}

.layout526_row {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr 1fr;
}

.layout526_column {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  grid-template-columns: 1fr;
}

.layout526_card-large {
  grid-column-gap: 0rem;
  grid-row-gap: 0rem;
  border-radius: var(--\<unknown\|relume-variable-radius-medium\>);
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  justify-content: center;
  align-items: stretch;
  min-height: 32rem;
  padding: 2rem;
  display: flex;
  position: relative;
  overflow: hidden;
}

.layout526_background-image-wrapper {
  z-index: 0;
  border-radius: 6px;
  position: absolute;
  inset: 0%;
  overflow: hidden;
}

.layout526_background-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  inset: 0%;
}

.layout526_background-image.opacity-1 {
  opacity: .9;
}

.layout526_card-large-content {
  flex-direction: column;
  justify-content: center;
  display: flex;
  position: relative;
}

.layout526_card-small {
  border-radius: var(--\<unknown\|relume-variable-radius-medium\>);
  flex-direction: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  align-items: stretch;
  padding: 4rem 2rem;
  display: flex;
  position: relative;
  overflow: hidden;
}

.layout526_card-small.text-color-white {
  min-height: 18rem;
}

.layout526_card-small-content {
  flex-direction: column;
  flex: 1;
  justify-content: center;
  display: flex;
  position: relative;
}

.layout526_item-icon-wrapper {
  display: inline-block;
}

.job-intro-wrapper {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  margin-bottom: 2rem;
  display: flex;
}

.section_contact2 {
  background-color: var(--woelfli-lightgrey);
}

.contact2_form-block {
  flex-direction: column;
  align-items: stretch;
  margin-bottom: 0;
}

.contact2_form {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  grid-template-rows: auto auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.form_field-2col {
  grid-column-gap: 1.5rem;
  grid-row-gap: 1.5rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.form_radio-2col {
  grid-column-gap: 1.5rem;
  grid-row-gap: .875rem;
  grid-template-rows: auto;
  grid-template-columns: 1fr 1fr;
  grid-auto-columns: 1fr;
  display: grid;
}

.success-text {
  font-weight: 600;
}

.error-text {
  color: #e23939;
}

.form-datenschutz-link {
  color: var(--woelfli-black);
  -webkit-text-decoration-skip-ink: auto;
  text-decoration-skip-ink: auto;
  text-decoration: underline;
}

.file-upload-form {
  border: 1px solid #999;
  border-radius: 6px;
  padding: 2rem 1rem;
}

.job-columns {
  grid-column-gap: 5rem;
  grid-row-gap: 5rem;
  display: flex;
  position: relative;
}

.job-details {
  color: var(--woelfli-grau);
  flex: 1;
  height: 100%;
  display: flex;
  position: sticky;
  top: 8rem;
}

.job-detail-list {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  grid-template-rows: auto;
  grid-template-columns: 1fr;
  grid-auto-columns: 1fr;
  padding-top: .5rem;
  padding-bottom: .5rem;
  display: flex;
}

.span-weight {
  font-weight: 400;
}

.code-embed-2 {
  width: 100%;
}

.map-link {
  color: var(--woelfli-blaugrau);
  text-decoration: underline;
}

.text-size-medium-copy {
  color: #000;
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 1.2;
}

.text-size-medium-copy.text-color-black {
  color: var(--woelfli-black);
  text-decoration: underline;
}

.text-size-medium-copy.text-weight-medium {
  margin-top: 0;
  font-weight: 500;
}

.contact-link-link {
  color: var(--woelfli-blaugrau);
  text-decoration: underline;
}

.job-text-wrap {
  grid-column-gap: .5rem;
  grid-row-gap: .5rem;
  flex-flow: column;
  display: flex;
}

.ueber-wrapper {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  flex-flow: column;
  display: flex;
}

.job-link {
  text-underline-position: auto;
  text-decoration: underline;
}

.jobs-maps-link {
  grid-column-gap: .2rem;
  grid-row-gap: .2rem;
  justify-content: flex-start;
  align-items: center;
  display: flex;
}

.success-list {
  grid-column-gap: 1rem;
  grid-row-gap: 1rem;
  flex-flow: column;
  justify-content: space-between;
  align-items: center;
  display: flex;
}

.bieten-section-heading {
  grid-column-gap: 2rem;
  grid-row-gap: 2rem;
  flex-flow: column;
  display: flex;
}

@media screen and (min-width: 1280px) {
  .padding-global.testimonial-padding {
    padding-left: 25%;
    padding-right: 25%;
  }

  .navbar2_link.text-color-black {
    color: var(--woelfli-black);
  }

  .layout433_image1 {
    aspect-ratio: 1;
  }

  .layout433_image2 {
    aspect-ratio: 3 / 2;
  }

  .social-link {
    color: #24384891;
    flex: 0 auto;
    text-decoration: underline;
  }
}

@media screen and (min-width: 1440px) {
  .header-gradient.landing-gradient, .header-gradient.project-gradient, .header-gradient.leistungen-gradient {
    z-index: 12;
  }

  .gallery13_component {
    min-height: 42.5rem;
    max-height: 100%;
    position: relative;
  }

  .gallery13_slider {
    height: 100%;
  }

  .icon-embed-small.intro-expand-icon, .icon-embed-small.fortschritt-expand-icon {
    display: flex;
  }

  .gallery13_mask {
    height: 100%;
  }

  .gallery13_slide {
    height: 100%;
    min-height: 42.5rem;
  }

  .slider-inner-content {
    justify-content: space-between;
    height: 42.5rem;
    overflow: visible;
  }

  .slider {
    min-height: 80svh;
  }
}

@media screen and (min-width: 1920px) {
  .padding-section-large {
    grid-column-gap: 3rem;
    grid-row-gap: 3rem;
  }

  .padding-global.testimonial-padding {
    padding-left: 30%;
    padding-right: 30%;
  }

  .heading-2 {
    font-size: 3.875rem;
  }

  .heading-3 {
    font-size: 2.25rem;
    line-height: 1.4;
  }

  .heading-4 {
    font-size: 1.75rem;
  }

  .heading-5 {
    font-size: 1.375rem;
  }

  .gallery13_component {
    max-height: 100%;
  }

  .gallery13_mask {
    height: auto;
  }

  .slider-inner-content {
    height: 50rem;
  }

  .slider {
    min-height: 80svh;
  }
}

@media screen and (max-width: 991px) {
  .close-block {
    position: static;
  }

  .hide-tablet {
    display: none;
  }

  .spacer-xxlarge {
    padding-top: 4.5rem;
  }

  .padding-xlarge {
    padding: 3.5rem;
  }

  .rl-styleguide_item-row {
    grid-template-columns: auto 1fr;
  }

  .margin-xlarge {
    margin: 3.5rem;
  }

  .padding-vertical {
    padding-left: 0;
    padding-right: 0;
  }

  .padding-xxlarge {
    padding: 4.5rem;
  }

  .padding-left {
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  .margin-bottom {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .margin-bottom.margin-small.intro-margin {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .margin-bottom.margin-small.max-width-xsmall {
    max-width: 28rem;
  }

  .margin-right-2 {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
  }

  .spacer-huge {
    padding-top: 5rem;
  }

  .margin-vertical {
    margin-left: 0;
    margin-right: 0;
  }

  .heading-style-h1 {
    font-size: 3.25rem;
  }

  .heading-style-h1.black-h1 {
    margin-bottom: 0;
  }

  .margin-huge {
    margin: 5rem;
  }

  .heading-style-h3 {
    font-size: 2.25rem;
  }

  .button {
    color: #fff0;
    font-size: 3rem;
  }

  .max-width-xsmall.vw-width {
    width: 100%;
    max-width: 90vw;
  }

  .max-width-xsmall.vw-width.wolf-icon-mitte {
    display: none;
  }

  .margin-top {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .margin-xxhuge {
    margin: 7.5rem;
  }

  .padding-section-large {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .padding-section-large.footer-padding {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .rl-styleguide_icons-list {
    grid-auto-flow: row;
  }

  .spacer-xhuge {
    padding-top: 6rem;
  }

  .spacer-xxhuge {
    padding-top: 7.5rem;
  }

  .heading-style-h4 {
    font-size: 1.75rem;
  }

  .max-width-full-tablet {
    width: 100%;
    max-width: none;
  }

  .padding-xhuge {
    padding: 6rem;
  }

  .margin-xhuge {
    margin: 6rem;
  }

  .padding-large {
    padding: 2.5rem;
  }

  .padding-horizontal {
    padding-top: 0;
    padding-bottom: 0;
  }

  .margin-xxlarge {
    margin: 4.5rem;
  }

  .spacer-xlarge {
    padding-top: 3.5rem;
  }

  .spacer-large {
    padding-top: 2.5rem;
  }

  .padding-bottom {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .padding-section-medium {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }

  .margin-horizontal {
    margin-top: 0;
    margin-bottom: 0;
  }

  .margin-left {
    margin-top: 0;
    margin-bottom: 0;
    margin-right: 0;
  }

  .margin-large {
    margin: 2.5rem;
  }

  .padding-right {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
  }

  .padding-xxhuge {
    padding: 7.5rem;
  }

  .heading-style-h2 {
    font-size: 2.75rem;
  }

  .padding-huge {
    padding: 5rem;
  }

  .padding-top {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .mapbox-wrapper {
    height: calc(var(--vh, 1vh) * 100);
  }

  .navbar2_component {
    justify-content: flex-start;
    align-items: center;
    left: 0%;
    right: 0%;
  }

  .navbar2_container {
    grid-template-columns: .25fr 1fr;
    display: flex;
  }

  .navbar2_menu {
    border-bottom: 1px solid var(--\<unknown\|relume-variable-border-color-1\>);
    background-color: var(--\<unknown\|relume-variable-background-color-1\>);
    -webkit-text-fill-color: inherit;
    background-clip: border-box;
    justify-content: center;
    width: 100%;
    padding: 1rem 5% 2.5rem;
    display: flex;
    position: absolute;
    overflow: auto;
  }

  .navbar2_menu.is-page-height-tablet {
    height: 100dvh;
    padding-bottom: 6.5rem;
    transition: height .5s;
  }

  .navbar2_link {
    text-align: center;
    padding: .75rem 0;
    font-size: 1.125rem;
  }

  .navbar2_button-wrapper {
    align-items: center;
    display: block;
  }

  .navbar2_menu-button {
    padding: 0;
    display: none;
  }

  .navbar2_menu-button.w--open {
    background-color: #0000;
  }

  .menu-icon2 {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 48px;
    height: 48px;
    margin-right: -.5rem;
    padding-bottom: 0;
    padding-right: 0;
    display: none;
  }

  .menu-icon2_line-top {
    background-color: var(--\<unknown\|relume-variable-text-color-1\>);
    width: 24px;
    height: 2px;
    padding-bottom: 0;
    padding-right: 0;
  }

  .menu-icon2_line-middle {
    background-color: var(--\<unknown\|relume-variable-text-color-1\>);
    justify-content: center;
    align-items: center;
    width: 24px;
    height: 2px;
    margin-top: 6px;
    margin-bottom: 6px;
    padding-bottom: 0;
    padding-right: 0;
    display: flex;
  }

  .menu-icon2_line-middle-inner {
    width: 4px;
    height: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  .menu-icon2_line-bottom {
    background-color: var(--\<unknown\|relume-variable-text-color-1\>);
    width: 24px;
    height: 2px;
    padding-bottom: 0;
    padding-right: 0;
  }

  .menu-dialog_inner {
    width: calc((var(--grid-column-width) * 8)  + (var(--grid-gutter) * (8 - 1)));
  }

  .menu-dialog_header-text {
    visibility: hidden !important;
  }

  .menu-dialog_nav {
    flex-flow: column;
    display: flex;
  }

  .menu-dialog_footer {
    width: calc((var(--grid-column-width) * 8)  + (var(--grid-gutter) * (8 - 1)));
  }

  .header46_component {
    justify-content: space-between;
    align-items: flex-end;
  }

  .layout242_list {
    grid-column-gap: 2rem;
  }

  .layout459_list {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column;
    display: flex;
  }

  .layout459_group-item.grid-item-double {
    grid-template-columns: 1fr;
    display: flex;
  }

  .layout459_group-item.grid-item-center {
    align-self: flex-start;
    max-width: 100%;
  }

  .layout459_group-item.grid-item-center.wolf-icon-mobile {
    display: none;
  }

  .card-grid-headline {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .category-card {
    aspect-ratio: 2.39;
  }

  .layout367_row {
    grid-template-columns: 1fr 1fr;
  }

  .layout367_card-small-content {
    padding: 2rem;
  }

  .layout367_card-small-content.footer-top-margin, .layout367_card-large {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
  }

  .layout367_card-large-content {
    padding: 2rem;
  }

  .after-header-headline {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    flex-flow: column;
  }

  .section_header67.text-color-alternate {
    justify-content: flex-end;
    align-items: flex-start;
    height: 70svh;
  }

  .header_card-2 {
    justify-content: center;
  }

  .header_card-2.text-color-alternate-2 {
    justify-content: flex-end;
    align-items: flex-start;
    height: 70svh;
    max-height: 70svh;
    margin-top: 0;
  }

  .button-row {
    flex-flow: column;
    display: none;
  }

  .header-gradient.landing-gradient.kontakt-gradient {
    background-image: linear-gradient(349deg, #243848d4 34%, #071b1e14 51%, #1f2839);
  }

  .header-gradient.leistungen-gradient {
    background-image: linear-gradient(349deg, #d5d0b300 11%, #071b1e14 24%, #1f2839);
  }

  .header-gradient.leistungen-gradient.kontakt-gradient {
    background-image: linear-gradient(349deg, #243848d4 34%, #071b1e14 51%, #1f2839);
  }

  .card-row4_component {
    grid-template-columns: 1fr;
  }

  .card-row4_card-content {
    padding: 2rem;
  }

  .project-header-wrapper {
    justify-content: center;
  }

  .project-back-button {
    color: #fff0;
    font-size: 3rem;
  }

  .header5_content.project-header-content {
    height: 75svh;
    min-height: auto;
    max-height: 75svh;
  }

  .project-header-nav {
    flex-flow: column;
    justify-content: flex-end;
    align-items: flex-start;
    display: flex;
  }

  .project-data-content {
    grid-column-gap: 3rem;
    grid-row-gap: 2rem;
  }

  .layout66_item-text-wrapper {
    font-weight: 400;
  }

  .splitter_handle-circle.is-1 {
    border-radius: 50%;
  }

  .header-content {
    margin-top: 5rem;
  }

  .after-intro.success-intro {
    margin-left: 5rem;
    margin-right: 5rem;
  }

  .logo-image {
    object-fit: fill;
  }

  .navbar-icon-link {
    display: none;
  }

  .navbar-icon-link.icon-link-mobile {
    color: #000;
    margin-left: 0;
    display: block;
  }

  .layout497_inner-wrapper {
    flex-flow: column;
    width: 100%;
    padding-right: 0;
    display: flex;
  }

  .layout497_header {
    width: 100%;
  }

  .layout497_tabs {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column;
    display: flex;
  }

  .layout497_tabs-content {
    width: 100%;
    padding-left: 0;
    position: relative;
  }

  .layout497_image {
    aspect-ratio: 2;
  }

  .layout497_lightbox-image {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }

  .layout497_tabs-menu {
    place-items: start stretch;
  }

  .layout497_tab-link {
    opacity: .48;
  }

  .layout497_tab-link.w--current {
    cursor: default;
  }

  .team8_list {
    grid-column-gap: 2rem;
  }

  .team8_social {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    flex-flow: column;
  }

  .gallery13_component {
    max-height: 100%;
  }

  .slider-prev.next-button {
    align-self: center;
    padding: .5rem 1rem;
  }

  .icon-embed-small.expand-icon, .icon-embed-small.intro-expand-icon, .icon-embed-small.fortschritt-expand-icon {
    display: block;
  }

  .icon-embed-small.mobile-wolf-icon {
    color: #000;
  }

  .slider-next {
    align-self: center;
    padding: .5rem 1rem;
  }

  .gallery13_mask {
    aspect-ratio: 1;
  }

  .slider-inner-content {
    justify-content: space-between;
    height: 100%;
  }

  .slider-image {
    object-fit: cover;
  }

  .layout358_card-content {
    padding: 2rem;
  }

  .layout294_list {
    grid-template-columns: 1fr 1fr;
  }

  .layout359_card-content {
    padding: 2rem;
  }

  .layout396_grid-list.expand-padding {
    padding-top: 3rem;
  }

  .layout396_row {
    grid-template-columns: 1fr;
  }

  .layout396_row._2-columns {
    flex-flow: column;
    display: flex;
  }

  .div-block-8 {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    flex-flow: column;
  }

  .marken-card-divider {
    display: none;
  }

  .reset-btn {
    border-radius: 4px;
    padding-top: .85em;
    padding-bottom: .85em;
  }

  .filter-list__item {
    width: 50%;
  }

  .layout128_content {
    grid-column-gap: 2rem;
    grid-row-gap: 4rem;
  }

  .layout19_content {
    grid-column-gap: 3rem;
    grid-template-columns: 1fr 1fr;
    grid-auto-flow: row;
    min-height: auto;
  }

  .card-row39_component {
    grid-template-columns: 1fr;
  }

  .stats14_content {
    grid-column-gap: 3rem;
    grid-row-gap: 3rem;
    grid-template-columns: 1fr 1fr;
    grid-auto-flow: row;
    min-height: auto;
  }

  .stats14_number {
    font-size: 4rem;
  }

  .marken-card-logo_wide {
    flex: 0 auto;
    max-width: 20rem;
  }

  .layout364_card-content {
    padding: 2rem;
  }

  .intro-akkordion-box {
    opacity: .48;
  }

  .intro-akkordion-box.w--current {
    cursor: default;
  }

  .intro-akkordion-box.know-how-box, .intro-akkordion-box.akkordion-anim {
    opacity: 100;
    background-color: #8784847a;
  }

  .event-header3_content {
    grid-template-columns: 1fr;
  }

  .leistung-item {
    opacity: 1;
  }

  .leistung-item.w--current {
    cursor: default;
  }

  .cw-cookie_banner {
    max-width: 80%;
  }

  .heading-style-h3-sans {
    font-size: 2.25rem;
  }

  .leistung-intro {
    opacity: 1;
  }

  .leistung-intro.w--current {
    cursor: default;
  }

  .kontakt-divider {
    display: none;
  }

  .svg-anim {
    align-self: center;
  }

  .leistungen-card-top {
    flex: 0 auto;
    max-width: 20rem;
  }

  .intro-boxes {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column;
    display: flex;
  }

  .footer-logos-mobile {
    display: none;
  }

  .initiaitv-card-content {
    padding: 2rem;
  }

  .layout121_content {
    grid-column-gap: 3rem;
    grid-row-gap: 2rem;
  }

  .layout121_progress-bar-wrapper {
    height: 80%;
  }

  .layout121_timeline-step {
    grid-column-gap: 1.5rem;
  }

  .layout67_content {
    grid-column-gap: 3rem;
    grid-row-gap: 2rem;
  }

  .trainee-box {
    opacity: .48;
  }

  .trainee-box.w--current {
    cursor: default;
  }

  .trainee-box.know-how-box, .trainee-box.akkordion-anim {
    opacity: 100;
    background-color: #8784847a;
  }

  .trainee-box-content {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
  }

  .karriere-card-content, .layout408_card-content, .card-row1_card-content, .card-row16_card-small-content {
    padding: 2rem;
  }

  .karriere-grid-footer {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .layout526_row {
    grid-template-columns: 1fr;
  }

  .layout526_column {
    grid-template-rows: auto;
    grid-template-columns: 1fr 1fr;
  }

  .layout526_card-large {
    min-height: 18rem;
  }
}

@media screen and (max-width: 767px) {
  .section {
    min-height: 90vh;
  }

  .section.mapbox-section {
    min-height: 100%;
  }

  .container {
    width: 86%;
  }

  .container.is-map {
    width: 100%;
    height: 90svh;
  }

  .locations-map_wrapper {
    width: 80%;
    margin-left: -30.2em;
  }

  .mapbox-wrap {
    height: 100%;
  }

  .locations-map_item {
    height: 80vh;
  }

  .close-block {
    position: static;
    inset: 20rem -30px auto auto;
  }

  .spacer-xxlarge {
    padding-top: 3rem;
  }

  .padding-xlarge {
    padding: 2.5rem;
  }

  .margin-xlarge {
    margin: 2.5rem;
  }

  .padding-vertical {
    padding-left: 0;
    padding-right: 0;
  }

  .padding-xxlarge {
    padding: 3rem;
  }

  .padding-left {
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  .margin-bottom {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .margin-bottom.margin-small {
    margin-bottom: 1rem;
  }

  .margin-bottom.margin-small.trainee-text-group {
    margin-bottom: 0;
  }

  .spacer-medium {
    padding-top: 1.5rem;
  }

  .margin-xsmall {
    margin: .75rem;
  }

  .icon-1x1-medium.step-icon {
    display: flex;
  }

  .margin-right-2 {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
  }

  .padding-small {
    padding: 1.25rem;
  }

  .spacer-huge {
    padding-top: 3.5rem;
  }

  .margin-vertical {
    margin-left: 0;
    margin-right: 0;
  }

  .heading-style-h1 {
    font-size: 2rem;
  }

  .heading-style-h1.hyphen {
    word-break: break-all;
  }

  .icon-embed-xlarge.larger-icon {
    width: 8rem;
    height: 8rem;
  }

  .spacer-small {
    padding-top: 1.25rem;
  }

  .margin-huge {
    margin: 3.5rem;
  }

  .heading-style-h6 {
    font-size: 1.125rem;
  }

  .heading-style-h3 {
    font-size: 2rem;
  }

  .button.is-navbar2-button {
    padding: .25rem 1rem;
  }

  .max-width-xsmall.vw-width {
    width: 100%;
    max-width: 100%;
  }

  .max-width-xsmall.vw-width.wolf-icon-mitte, .max-width-xsmall.vw-width.vw-width-left {
    max-width: 100%;
  }

  .margin-top {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .main-wrapper {
    margin-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .margin-xxhuge {
    margin: 5rem;
  }

  .padding-section-large {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .spacer-xhuge {
    padding-top: 4rem;
  }

  .spacer-xxhuge {
    padding-top: 5rem;
  }

  .heading-style-h5 {
    font-size: 1.25rem;
  }

  .padding-global {
    padding-left: 5%;
    padding-right: 5%;
  }

  .heading-style-h4 {
    font-size: 1.5rem;
    line-height: 1.4;
  }

  .padding-xhuge {
    padding: 4rem;
  }

  .text-style-quote {
    font-size: 1.125rem;
  }

  .margin-xhuge {
    margin: 4rem;
  }

  .text-style-nowrap {
    white-space: normal;
  }

  .padding-large {
    padding: 2rem;
  }

  .padding-horizontal {
    padding-top: 0;
    padding-bottom: 0;
  }

  .padding-xsmall {
    padding: .75rem;
  }

  .text-size-large {
    font-size: 1.125rem;
  }

  .hide-mobile-landscape {
    display: none;
  }

  .margin-xxlarge {
    margin: 3rem;
  }

  .spacer-xlarge {
    padding-top: 2.5rem;
  }

  .spacer-large {
    padding-top: 2rem;
  }

  .spacer-xsmall {
    padding-top: .75rem;
  }

  .padding-bottom {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .padding-section-medium {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .margin-horizontal {
    margin-top: 0;
    margin-bottom: 0;
  }

  .margin-left {
    margin-top: 0;
    margin-bottom: 0;
    margin-right: 0;
  }

  .max-width-full-mobile-landscape {
    width: 100%;
    max-width: none;
  }

  .margin-large {
    margin: 2rem;
  }

  .margin-medium {
    margin: 1.5rem;
  }

  .padding-right {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
  }

  .text-size-medium {
    font-size: 1rem;
  }

  .padding-xxhuge {
    padding: 5rem;
  }

  .heading-style-h2 {
    font-size: 2.25rem;
    line-height: 1.1;
  }

  .padding-medium {
    padding: 1.5rem;
  }

  .padding-huge {
    padding: 3.5rem;
  }

  .margin-small {
    margin: 1.25rem;
  }

  .padding-top {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .padding-section-small {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .mapbox-wrapper {
    height: 100svh;
  }

  .header_background-image {
    height: 75svh;
  }

  .navbar2_component {
    min-height: 4rem;
    top: 1rem;
  }

  .navbar2_menu.is-page-height-tablet {
    padding-bottom: 6rem;
  }

  .menu-dialog_outer {
    padding-top: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .menu-dialog_inner {
    width: 100%;
  }

  .menu-dialog_header-text.hide {
    visibility: hidden;
    display: block;
  }

  .menu-dialog_nav-link-outer.phone-number, .menu-dialog_nav-link-outer.email {
    font-size: 1.313rem;
  }

  .menu-dialog_footer {
    width: 100%;
  }

  .layout242_list {
    grid-row-gap: 1.875rem;
    grid-template-columns: 1fr;
  }

  .layout459_component {
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: flex;
  }

  .layout459_list {
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
  }

  .layout459_group-item {
    grid-column-gap: 3rem;
    grid-row-gap: 3rem;
  }

  .layout459_group-item.grid-item-double {
    align-self: flex-start;
  }

  .layout459_group-item.grid-item-center {
    justify-content: center;
    align-self: flex-start;
    align-items: flex-start;
    width: 100%;
  }

  .layout459_group-item.grid-item-center.wolf-icon-mobile {
    display: none;
  }

  .card-grid-headline {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .testimonial1_component {
    height: auto;
  }

  .layout367_grid-list {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    grid-auto-flow: row;
  }

  .layout367_row {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column;
    grid-template-columns: 1fr;
    display: flex;
  }

  .layout367_card-small-content {
    padding: 1.5rem;
  }

  .layout367_card-small-content-top {
    flex-flow: column;
    justify-content: center;
    align-items: flex-start;
    display: flex;
  }

  .layout367_card-small-content-top.footer-logo-grid {
    justify-content: space-between;
    align-self: stretch;
  }

  .layout367_card-large {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    order: 1;
    grid-template-columns: 1fr;
  }

  .layout367_card-large-content {
    padding: 1.5rem;
  }

  .layout367_card-large-footer.desktop-large-footer {
    display: none;
  }

  .layout367_card-large-footer.mobile-large-footer {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    flex-flow: column;
    padding-bottom: 1.5rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    display: flex;
  }

  .policy-column.policy-desktop {
    display: none;
  }

  .policy-column.policy-mobile {
    flex-flow: row;
    display: flex;
  }

  .copyright-column.copyright-desktop {
    display: none;
  }

  .copyright-column.copyright-mobile {
    display: block;
  }

  .marken-navi {
    flex-flow: column;
  }

  .section_header67.text-color-alternate {
    height: 65svh;
    margin-top: 0;
  }

  .header67_background-image-wrapper {
    height: 100%;
  }

  .image-overlay-layer.footer-overlay-bg {
    display: block;
  }

  .header_card-2.text-color-alternate-2 {
    justify-content: flex-end;
    align-items: flex-start;
    height: 75svh;
    max-height: 75svh;
    margin-top: 0;
    margin-bottom: 0;
  }

  .header-gradient.landing-gradient {
    opacity: .77;
  }

  .office-map_item {
    height: 80vh;
  }

  .cta-button.cta-secondary {
    padding-top: .5rem;
    padding-bottom: .5rem;
  }

  .header-intro-wrapper {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
  }

  .card-row4_component {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    grid-template-columns: 1fr;
  }

  .card-row4_card {
    grid-template-columns: 1fr;
  }

  .card-row4_card.propertylab-card {
    min-height: auto;
  }

  .card-row4_card-content {
    flex: 0 auto;
    padding: 1.5rem;
  }

  .marken-card-logo {
    max-width: 20rem;
  }

  .header114_content-left {
    justify-content: flex-start;
  }

  .project-header-nav {
    align-content: end;
  }

  .project-header-copy {
    grid-column-gap: 3rem;
    grid-row-gap: 3rem;
    flex-flow: column;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    justify-content: flex-end;
    align-self: center;
    align-items: flex-start;
    display: flex;
  }

  .next-project-link {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .project-data-content {
    grid-column-gap: 1.25rem;
    grid-row-gap: 1.25rem;
    grid-template-columns: 1fr;
  }

  .full-width-img-p {
    height: auto;
    max-height: none;
  }

  .header146_image {
    aspect-ratio: 3 / 2;
  }

  .splitter_handle-line {
    background-color: #fff;
    width: 2px;
    height: 50%;
  }

  .splitter_handle-circle.is-1 {
    height: 3rem;
  }

  .demo-section__title {
    font-size: 4em;
  }

  .sticky-tab__title {
    font-size: 2em;
  }

  .portfolio-expose {
    font-size: 1.125rem;
  }

  .after-intro-content.marken-intro {
    flex-flow: column;
  }

  .logo-image {
    aspect-ratio: auto;
    object-fit: contain;
    width: auto;
  }

  .footer-column.footer-column-mobile {
    justify-content: space-between;
    width: 100%;
  }

  .layout497_component {
    padding: 1.5rem;
  }

  .layout497_content {
    flex-flow: column;
    display: flex;
  }

  .layout497_inner-wrapper {
    width: 100%;
    padding-right: 0;
  }

  .layout497_header {
    width: 100%;
  }

  .layout497_tabs {
    grid-column-gap: 0rem;
    flex-flow: column;
    grid-template-columns: 1fr;
    display: flex;
  }

  .layout497_tabs-content {
    width: 100%;
    padding-left: 0;
    position: relative;
  }

  .layout497_tabs-menu {
    place-items: start stretch;
    margin-bottom: 0;
  }

  .layout497_tab-link {
    padding: 1rem;
  }

  .layout497_tab-link.w--current {
    cursor: pointer;
    padding: 1rem;
  }

  .layout433_content {
    grid-column-gap: 3rem;
    grid-row-gap: 3rem;
    grid-template-columns: 1fr;
  }

  .team8_list {
    grid-row-gap: 3rem;
    grid-template-columns: 1fr 1fr;
  }

  .team8_image-wrapper {
    height: 100%;
  }

  .team8_social {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    flex-flow: column;
  }

  .gallery13_mask {
    aspect-ratio: 2 / 3;
  }

  .gallery13_slide {
    padding-left: 0;
    padding-right: 0;
  }

  .slider-image {
    position: relative;
  }

  .layout358_grid-list {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout358_card {
    flex-flow: column;
    grid-template-columns: 1fr;
    grid-auto-flow: row;
    height: auto;
    display: flex;
  }

  .layout358_card-content {
    flex-flow: column;
    height: auto;
    min-height: auto;
    padding: 1.5rem;
    display: block;
  }

  .layout358_image-wrapper {
    aspect-ratio: 16 / 9;
    flex-flow: row;
    justify-content: center;
    align-items: stretch;
    width: 100%;
    height: auto;
    min-height: auto;
    display: block;
  }

  .layout358_image {
    aspect-ratio: auto;
  }

  .layout294_list {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    grid-template-columns: 1fr;
  }

  .layout359_grid-list {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout359_card {
    grid-template-columns: 1fr;
  }

  .layout359_card.no-border {
    border-style: none;
    border-width: 0;
  }

  .layout359_card-content {
    padding: 1.5rem 0;
  }

  .layout396_grid-list {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout396_row {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    grid-template-columns: 1fr;
  }

  .layout396_card-content {
    padding: 1.5rem;
  }

  .div-block-8 {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    flex-flow: column;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .kraft-content {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    flex-flow: column-reverse;
    grid-template-columns: 1fr;
  }

  .filter-group, .filter-buttons.last {
    padding-bottom: 0;
  }

  .filter-list__item {
    width: 100%;
  }

  .after-projects-content {
    grid-column-gap: 3rem;
    grid-row-gap: 3rem;
    grid-template-columns: 1fr;
  }

  .layout128_content {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    grid-template-columns: 1fr;
  }

  .layout128_item {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .kontakt-intro-wrapper {
    grid-column-gap: 1.235rem;
    grid-row-gap: 1.235rem;
  }

  .layout19_content {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column-reverse;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    justify-content: center;
    align-items: flex-start;
    display: flex;
    position: relative;
  }

  .contact-block-wrap {
    justify-content: flex-start;
    align-self: flex-start;
    width: 100%;
  }

  .contact-block-paragraph.max-width-xsmall {
    display: none;
  }

  .card-row39_component {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    grid-template-columns: 1fr;
  }

  .card-row39_card-content {
    padding: 1.5rem;
  }

  .stats14_content {
    grid-row-gap: 3rem;
    grid-template-columns: 1fr 1fr;
  }

  .stats14_item-list {
    grid-row-gap: 2rem;
  }

  .stats14_number {
    font-size: 3.5rem;
  }

  .wolf-symbol-footer.wolf-mobile-2 {
    display: none;
  }

  .marken-card-logo_wide {
    flex: 0 auto;
    justify-content: flex-start;
    align-items: center;
    max-width: 20rem;
    display: flex;
  }

  .layout364_grid-list {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout364_row {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    grid-template-columns: 1fr;
  }

  .layout364_card {
    grid-template-columns: 1fr;
  }

  .layout364_card-content {
    padding: 1.5rem;
  }

  .div-block-10 {
    flex: 0 auto;
  }

  .intro-akkordion-box {
    padding: 1rem;
  }

  .intro-akkordion-box.w--current {
    cursor: pointer;
    padding: 1rem;
  }

  .intro-akkordion-box.know-how-box {
    padding: 2rem;
  }

  .intro-akkordion-box.akkordion-anim {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: #8784847a;
    flex-flow: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    padding: 2rem;
  }

  .career9_accordion {
    margin-bottom: 0;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .career9_job-department {
    padding-top: 0;
    padding-bottom: 0;
  }

  .career9_icon-wrapper {
    width: 28px;
  }

  .event-header3_content {
    grid-template-columns: 1fr;
    align-items: flex-start;
  }

  .event-header3_list {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
  }

  .leistung-item {
    padding: 1rem;
  }

  .leistung-item.w--current {
    cursor: pointer;
    padding: 1rem;
  }

  .leistungen-accordion {
    margin-bottom: 0;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .leistungen-header {
    padding-top: 0;
    padding-bottom: 0;
  }

  .cw-cookie_banner {
    border-radius: .5rem;
    max-width: 100%;
    padding: 1rem;
    right: 20px;
  }

  .heading-style-h3-sans {
    font-size: 2rem;
  }

  .cw-cookie_buttons {
    flex-direction: column;
  }

  .cw-button_secondary, .cw-button_primary {
    padding-left: 40px;
    padding-right: 40px;
  }

  .cw-cookie_options {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
  }

  .leistung-intro {
    border-radius: 6px;
    padding: 1rem;
    overflow: hidden;
  }

  .leistung-intro.w--current {
    cursor: pointer;
    padding: 1rem;
  }

  .mobile-icon-expand {
    width: 28px;
  }

  .leistungen-card-top {
    flex: 0 auto;
    justify-content: flex-start;
    align-items: center;
    max-width: 20rem;
    display: flex;
  }

  .marken-desktop-close {
    width: 28px;
  }

  .intro-boxes {
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
  }

  .footer-logos-desktop {
    display: none;
  }

  .footer-logos-mobile {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    order: 1;
    align-self: stretch;
    display: flex;
  }

  .woelfli-logo-column {
    justify-content: space-between;
    width: 100%;
    display: flex;
  }

  .code-embed, .svg-embed {
    width: 100%;
    height: 100%;
  }

  .career19_tabs {
    align-items: flex-start;
  }

  .category-filter-menu {
    width: 100vw;
    margin-left: -5vw;
    padding-left: 5vw;
    overflow: scroll;
  }

  .category-filter-menu.is-center.no-scrollbar {
    margin-bottom: 2rem;
  }

  .career19_list {
    grid-row-gap: 1.5rem;
  }

  .career19_item {
    padding: 1.5rem;
  }

  .initiaitv-card-content {
    flex: 0 auto;
    padding: 1.5rem;
  }

  .section_layout121 {
    display: block;
  }

  .layout121_content {
    grid-template-columns: 1fr;
  }

  .layout121_progress-bar-wrapper {
    height: 80%;
    left: 2rem;
  }

  .layout121_timeline-icon-wrapper {
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .layout67_component {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout67_content {
    grid-column-gap: 1.25rem;
    grid-row-gap: 1.25rem;
    grid-template-columns: 1fr;
  }

  .trainee-box {
    padding: 1rem;
  }

  .trainee-box.w--current {
    cursor: pointer;
    padding: 1rem;
  }

  .trainee-box.know-how-box {
    padding: 2rem;
  }

  .trainee-box.akkordion-anim {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: #8784847a;
    flex-flow: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    padding: 2rem;
  }

  .trainee-box-content {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .bewerben-button-group {
    flex-flow: column;
  }

  .karriere-card {
    grid-template-columns: 1fr;
  }

  .karriere-card-content {
    padding: 1.5rem;
  }

  .karriere-card-item {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .section_layout408 {
    display: none;
  }

  .layout408_component {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout408_card {
    grid-template-columns: 1fr;
    height: auto;
    margin-bottom: 0;
    position: static;
  }

  .layout408_card-content {
    padding: 1.5rem;
  }

  .layout408_image-wrapper {
    height: auto;
  }

  .card-row1_component {
    grid-template-columns: 1fr;
  }

  .card-row1_card-content, .card-row16_card-small-content {
    padding: 2rem 1.5rem;
  }

  .karriere-grid-footer {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .layout526_row, .layout526_column {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout526_card-large, .layout526_card-small {
    padding: 1.5rem;
  }

  .job-intro-wrapper, .contact2_form {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
  }

  .form_field-2col.is-mobile-1col {
    grid-template-columns: 1fr;
  }

  .job-columns {
    flex-flow: column;
  }

  .text-size-medium-copy {
    font-size: 1rem;
  }

  .bieten-section-heading {
    grid-column-gap: 2rem;
    grid-row-gap: 2rem;
    flex-flow: column;
    display: flex;
  }
}

@media screen and (max-width: 479px) {
  .container.is-map {
    height: 90vh;
  }

  .locations-map_wrapper.is--show {
    height: 75vh;
  }

  .locations-map_item {
    height: 75vh;
    display: none;
  }

  .locations-map_item.is--show {
    height: 75vh;
  }

  .location-map_card-wrap {
    min-width: 90vw;
    max-width: 90vw;
  }

  .close-block {
    right: -30px;
  }

  .max-width-full-mobile-portrait {
    width: 100%;
    max-width: none;
  }

  .padding-vertical {
    padding-left: 0;
    padding-right: 0;
  }

  .padding-left {
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 0;
  }

  .max-width-full.job-header-cta {
    grid-column-gap: 1.875rem;
    grid-row-gap: 1.875rem;
    flex-flow: column;
    justify-content: space-between;
    align-items: flex-start;
  }

  .margin-bottom {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .icon-1x1-medium.step-icon {
    width: 2rem;
    height: 2rem;
  }

  .margin-right-2 {
    margin-top: 0;
    margin-bottom: 0;
    margin-left: 0;
  }

  .margin-vertical {
    margin-left: 0;
    margin-right: 0;
  }

  .heading-style-h1.black-h1 {
    margin-top: 0;
  }

  .heading-style-h1.absolute-title {
    text-shadow: 0 0 40px #0006;
    left: 1rem;
    right: 1rem;
  }

  .heading-style-h1.absolute-title.mobile-slider-title {
    color: #000;
    text-shadow: none;
    position: relative;
    top: auto;
    left: auto;
    right: auto;
  }

  .max-width-xsmall.vw-width {
    width: 90vw;
  }

  .margin-top {
    margin-bottom: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .padding-section-large {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .padding-section-large.padding-custom2 {
    padding-top: 3rem;
  }

  .padding-section-large.padding-custom {
    padding-bottom: 2rem;
  }

  .padding-section-large.footer-padding {
    grid-column-gap: 4rem;
    grid-row-gap: 4rem;
    padding-bottom: 1rem;
  }

  .max-width-medium.icon-job-columns {
    flex-flow: column;
  }

  .padding-global.testimonial-padding {
    padding-left: 5%;
    padding-right: 5%;
  }

  .form_field-wrapper {
    flex-flow: column;
    width: 100%;
    display: flex;
  }

  .padding-horizontal {
    padding-top: 0;
    padding-bottom: 0;
  }

  .hide-mobile-portrait {
    display: none;
  }

  .padding-bottom {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .margin-horizontal {
    margin-top: 0;
    margin-bottom: 0;
  }

  .margin-left {
    margin-top: 0;
    margin-bottom: 0;
    margin-right: 0;
  }

  .padding-right {
    padding-top: 0;
    padding-bottom: 0;
    padding-left: 0;
  }

  .heading-style-h2 {
    flex-flow: column;
    line-height: 1.2;
    display: block;
  }

  .heading-style-h2.text-weight-medium {
    line-height: 1.1;
  }

  .heading-style-h2.link-span {
    flex-flow: row;
    display: block;
  }

  .padding-top {
    padding-bottom: 0;
    padding-left: 0;
    padding-right: 0;
  }

  .header_background-image {
    height: 75svh;
  }

  .navbar2_component {
    top: 1rem;
  }

  .menu-dialog {
    min-height: 100svh;
  }

  .menu-dialog_nav-link.u-h2 {
    font-size: 1.25rem;
  }

  .layout242_list {
    grid-template-columns: 1fr;
  }

  .headline-tag-group {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout459_list {
    grid-template-columns: 1fr;
  }

  .layout459_group-item.grid-item-double, .layout459_group-item.grid-item-center {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    place-items: center stretch;
  }

  .know-how-box {
    padding: 2rem;
  }

  .card-grid-headline {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .category-card {
    aspect-ratio: 3 / 2;
    background-color: #ca7085;
  }

  .image-section {
    height: auto;
  }

  .layout367_row {
    grid-template-columns: 1fr;
  }

  .layout367_card-small-content {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
  }

  .layout367_card-small-content-top.footer-logo-grid {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    grid-template-rows: auto auto;
    grid-template-columns: 1fr 1fr;
    grid-auto-columns: 1fr;
    display: flex;
  }

  .layout367_card-large {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    order: 1;
  }

  .layout367_card-large.card-mobile {
    display: flex;
  }

  .layout367_card-large-content {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    flex-flow: row;
    display: flex;
  }

  .layout367_card-large-footer {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column-reverse;
    justify-content: space-between;
    align-items: flex-start;
    padding-bottom: 1.5rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .footer13_link-list.social-link-list {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
  }

  .footer-contact-text {
    font-size: 1rem;
  }

  .policy-column.policy-mobile {
    flex-flow: row-reverse wrap;
    justify-content: space-between;
  }

  .marken-navi {
    flex-flow: column;
  }

  .section_header67.text-color-alternate {
    justify-content: flex-end;
    align-items: flex-start;
    height: 60svh;
  }

  .header_card-2 {
    height: auto;
  }

  .header_card-2.text-color-alternate-2 {
    justify-content: flex-end;
    align-items: flex-start;
    height: 75svh;
    max-height: 75svh;
    margin-top: 0;
  }

  .header_card-2.text-color-alternate-2.success-header-card-2 {
    height: 100svh;
  }

  .header-gradient.image-overlay {
    background-image: linear-gradient(5deg, #24384899, #d5d0b300 38% 49%);
  }

  .office-map_item {
    height: 75vh;
    display: none;
  }

  .office-map_card-wrap {
    min-width: 90vw;
    max-width: 90vw;
  }

  .map-highlight-buttons {
    z-index: 17;
    bottom: 8%;
  }

  .header-intro-wrapper {
    grid-row-gap: 1rem;
    flex-direction: column;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    justify-items: center;
    display: flex;
  }

  .grid-header {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column;
    display: flex;
  }

  .card-row4_component {
    grid-template-columns: 1fr;
  }

  .card-row4_card-content {
    align-items: flex-start;
  }

  .marken-card-logo {
    justify-content: flex-start;
    align-items: center;
    display: block;
  }

  .project-header-wrapper {
    height: auto;
  }

  .project-header-copy {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    flex-flow: column;
    justify-content: flex-end;
    align-items: flex-start;
    display: flex;
  }

  .project-data-content {
    grid-template-columns: 1fr;
  }

  .header146_image {
    aspect-ratio: 3 / 2;
  }

  .splitter_handle-circle.is-1 {
    height: 3.7rem;
  }

  .after-intro.success-intro {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .after-intro-wrapper {
    grid-row-gap: .25rem;
    flex-direction: column;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    justify-items: center;
    display: flex;
  }

  .logo-image {
    object-fit: contain;
    width: auto;
  }

  .card-top.woelfli-logo-top, .card-top.initiativ-top {
    flex-flow: column;
  }

  .footer-email:hover {
    text-decoration: underline;
  }

  .footer-column {
    width: 100%;
  }

  .footer-column.footer-column-mobile {
    flex-flow: column;
  }

  .layout497_component {
    padding: 1.5rem;
  }

  .layout497_tabs {
    grid-template-columns: 1fr;
  }

  .layout497_tabs-menu {
    flex-flow: column;
    margin-bottom: 0;
    display: flex;
  }

  .layout497_tab-link {
    max-width: 100%;
  }

  .layout497_tab-title {
    max-width: 100%;
    position: relative;
  }

  .layout433_image-group {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .team8_list {
    grid-template-columns: 1fr;
  }

  .gallery13_slider {
    aspect-ratio: auto;
    flex-flow: column;
    height: auto;
    display: block;
  }

  .slider15_arrows {
    flex-flow: row;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    height: 100%;
    padding-top: 0;
    padding-left: 1.87rem;
    position: relative;
    bottom: 6rem;
  }

  .slider-prev.next-button {
    z-index: 22;
    padding-left: .5rem;
    padding-right: .5rem;
    position: relative;
    inset: 35% auto auto -2%;
  }

  .slider-next {
    z-index: 22;
    padding-left: .5rem;
    padding-right: .5rem;
    position: relative;
    top: 35%;
    right: -2%;
  }

  .gallery13_mask {
    aspect-ratio: 2 / 3;
  }

  .gallery13_slide {
    overflow: hidden;
  }

  .slider-inner-content {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: column-reverse;
  }

  .slider-background-image {
    position: relative;
  }

  .layout358_card {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout358_card-content {
    padding: 0;
  }

  .layout358_image-wrapper {
    width: 100%;
  }

  .layout294_list {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    grid-template-columns: 1fr;
  }

  .layout359_card {
    border-style: none;
  }

  .layout359_card-content {
    grid-column-gap: 3rem;
    grid-row-gap: 3rem;
    padding: 0;
  }

  .layout396_card-content {
    padding: 1rem;
  }

  .marken-card-description {
    width: 100%;
  }

  .kraft-content-right {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .kraft-content {
    grid-auto-flow: row;
    place-items: start;
  }

  .project-card {
    min-height: 20rem;
  }

  .project-card__bottom {
    padding: 1rem 1em 1em;
  }

  .after-project-image-group {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
  }

  .layout128_content {
    grid-column-gap: 0rem;
    grid-row-gap: 0rem;
    flex-flow: column;
    grid-template-columns: 1fr;
  }

  .layout128_item {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .kontakt-intro-wrapper {
    grid-row-gap: 1.875rem;
    flex-direction: column;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    justify-items: center;
    display: flex;
  }

  .layout19_content {
    grid-template-columns: 1fr;
  }

  .stats14_content {
    grid-row-gap: 3rem;
    grid-template-columns: 1fr 1fr;
  }

  .stats14_item-list {
    grid-row-gap: 2rem;
    grid-template-columns: 1fr;
  }

  .wolf-symbol-footer.wolf-desktop {
    display: none;
  }

  .wolf-symbol-footer.wolf-mobile-2 {
    display: block;
  }

  .marken-card-logo_wide {
    max-width: 100%;
  }

  .marken-card-logo_wide.coreal-logo {
    max-height: 3rem;
  }

  .layout364_row {
    grid-template-columns: 1fr;
  }

  .intro-akkordion-box {
    max-width: 100%;
  }

  .intro-akkordion-title {
    max-width: 100%;
    position: relative;
  }

  .career9_accordion {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .career9_job-department {
    padding-top: 0;
    padding-bottom: 0;
  }

  .career9_icon-wrapper {
    display: block;
  }

  .logo-column {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    flex-flow: row;
    justify-content: space-between;
    width: 100%;
  }

  .event-header3_content {
    grid-row-gap: 2rem;
  }

  .leistung-list-item {
    max-width: 100%;
    position: relative;
  }

  .leistung-item {
    max-width: 100%;
    padding: 0;
  }

  .leistungen-accordion {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .leistungen-header {
    padding-top: 0;
    padding-bottom: 0;
  }

  .cw-cookie_banner {
    bottom: 10px;
    left: 10px;
    right: 10px;
  }

  .cw-button_secondary, .cw-button_primary {
    width: 100%;
    padding-top: 18px;
    padding-bottom: 19px;
  }

  .cw-cookie_options {
    flex-wrap: wrap;
  }

  .cw-cookie_icon {
    z-index: 99;
    display: block;
  }

  .leistung-intro {
    max-width: 100%;
  }

  .mobile-icon-expand {
    display: block;
  }

  .leistungen-card-top {
    max-width: 100%;
  }

  .leistungen-card-top.coreal-logo {
    max-height: 3rem;
  }

  .marken-desktop-close {
    display: none;
  }

  .intro-boxes {
    grid-template-columns: 1fr;
  }

  .wolf-column-mobile {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    justify-content: space-between;
    width: 100%;
    display: flex;
  }

  .footer-logo:hover {
    text-decoration: underline;
  }

  .footer-logos-mobile {
    grid-column-gap: 1.5rem;
    grid-row-gap: 1.5rem;
    order: 1;
  }

  .slider {
    height: auto;
    min-height: auto;
  }

  .category-filter-menu.is-center.no-scrollbar {
    grid-column-gap: 1rem;
    grid-row-gap: 1rem;
    margin-bottom: 2rem;
  }

  .category-filter-link.w--current {
    flex: 0 auto;
    padding-left: .5rem;
    padding-right: .5rem;
  }

  .initiaitv-card-content {
    align-items: flex-start;
  }

  .layout121_content {
    grid-template-columns: 1fr;
  }

  .layout121_progress-bar-wrapper {
    left: 1.5rem;
  }

  .layout121_timeline-step {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .layout67_content {
    grid-template-columns: 1fr;
  }

  .trainee-box {
    max-width: 100%;
  }

  .trainee-box-content {
    max-width: 100%;
    position: relative;
  }

  .karriere-card-item {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .karriere-grid-footer {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .job-intro-wrapper {
    grid-row-gap: 1rem;
    flex-direction: column;
    grid-template-rows: auto;
    grid-template-columns: 1fr;
    grid-auto-columns: 1fr;
    justify-items: center;
    display: flex;
  }

  .contact2_form-block {
    width: 100%;
    position: relative;
  }

  .contact2_form {
    flex-flow: column;
    width: 100%;
    max-width: 100%;
    display: flex;
    position: relative;
  }

  .form_field-2col {
    flex-flow: column;
    max-width: 100%;
    display: flex;
  }

  .form_field-2col.is-mobile-1col {
    flex-flow: column;
    display: flex;
  }

  .captcha-wrapping {
    width: 100%;
    display: flex;
  }
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217cb-e717c714 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  place-self: center stretch;
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217cc-e717c714 {
  justify-self: start;
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217de-e717c714 {
  place-self: stretch end;
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217ef-e717c714 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  place-self: center;
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217f0-e717c714 {
  place-self: start center;
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217f2-e717c714 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  place-self: center stretch;
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217f3-e717c714 {
  place-self: stretch start;
}

#w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e221803-e717c714 {
  justify-self: end;
}

#w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b19-398b1b13 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b1a-398b1b13 {
  grid-area: 1 / 1 / 3 / 2;
}

#w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b2d-398b1b13 {
  grid-area: 1 / 2 / 3 / 3;
}

#w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b49-398b1b13 {
  align-self: start;
}

#w-node-d392c3b2-4450-74b5-1131-dbdbdf68d69c-df68d698 {
  justify-self: center;
}

#w-node-_7728f4df-2d5d-59ec-10e7-ad72bf765fec-bf765fec {
  justify-self: end;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efa9-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efab-e717c767 {
  justify-self: center;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efbf-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc0-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc4-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc5-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc9-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efca-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efce-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efcf-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd3-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd4-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd8-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd9-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe3-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe4-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe8-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe9-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efed-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efee-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff2-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff3-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff7-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff8-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8effc-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8effd-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f007-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f008-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f00c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f00d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f011-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f012-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f016-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f017-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f020-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f021-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f02a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f02b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f03d-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f03e-e717c767 {
  justify-self: start;
}

#w-node-_1d49633c-b1f3-b624-4b33-fa9d4c806ac2-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_1d49633c-b1f3-b624-4b33-fa9d4c806ac3-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f042-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f043-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f047-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f048-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f04c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f04d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f051-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f052-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f05c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f05d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f061-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f062-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f066-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f067-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f06b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f06c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f070-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f071-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f075-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f076-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f081-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f082-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f086-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f087-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f08b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f08c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f090-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f091-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f095-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f096-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f09a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f09b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f09f-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a0-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a4-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a5-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a9-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0aa-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0ae-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0af-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0b9-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0ba-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0be-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0bf-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0c3-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0c4-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0cb-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0cc-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f10d-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f110-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f111-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f114-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f115-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f118-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f11c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f11d-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f120-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f121-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f124-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f12b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f12c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f131-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f132-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f136-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f137-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f13b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f13c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f140-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f141-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f151-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f154-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f155-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f158-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f159-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f15c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f15d-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f161-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f164-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f165-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f168-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f169-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f16c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f174-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f175-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f176-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f178-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f17b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f17c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f17d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f181-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f184-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f185-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f186-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f18a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f18e-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f18f-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f195-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f198-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f199-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f19a-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f19e-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1a1-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1a2-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1a3-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1a9-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1ad-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1ae-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1b2-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1b5-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1ba-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1bb-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1bc-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1c0-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1c3-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1c4-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1c5-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1cb-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1ce-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1cf-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1d0-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1d6-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1d9-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1da-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1db-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1e3-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1e6-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1e7-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1e8-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1f4-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1f5-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1f6-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f1fa-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f1ff-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f200-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f201-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f207-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f20c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f20d-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f20e-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f214-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f219-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f21a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f21b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f223-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f229-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f22a-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f230-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f235-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f236-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f237-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f23b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f23e-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f23f-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f240-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f246-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f249-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f24a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f24b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f251-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f254-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f255-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f256-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f25e-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f261-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f262-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f263-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f269-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f26c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f26d-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f26e-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f276-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f27a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f27b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f281-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f28a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f28b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f28c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f28d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f28f-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f292-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f296-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f297-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f298-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2a1-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2a2-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2a3-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2ac-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2ad-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2ae-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2af-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2b5-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2b6-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2b7-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2b8-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2ba-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2be-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2bf-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2c0-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2c1-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2c3-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2cc-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2d3-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2d4-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2d5-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2d6-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2d8-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2db-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2df-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2e0-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2e1-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2ea-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2eb-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2ec-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2f5-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2f6-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2f7-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2f8-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f2fe-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f2ff-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f300-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f301-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f303-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f307-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f308-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f309-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f30a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f30c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f359-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f35a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f35f-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f360-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f368-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f369-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f370-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f371-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3a2-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3a6-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3a7-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3a9-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3ab-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3ac-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3ae-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3b0-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3b1-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3b3-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3b5-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3b6-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3b8-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3ba-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3bb-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3bd-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3bf-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3c0-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3c2-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3c4-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3c6-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3c8-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3ca-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3cb-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3cd-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3ce-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3d0-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3d1-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3d3-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3d4-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3d6-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3d7-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3d9-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3da-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3dc-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3dd-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3df-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3e5-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3e9-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3ea-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3ec-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f3ee-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3ef-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3f1-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3f3-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3f4-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3f6-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3f8-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3f9-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3fb-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3fd-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f3fe-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f400-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f402-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f403-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f405-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f407-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f408-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f40a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f40c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f40d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f40f-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f411-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f412-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f414-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f416-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f417-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f419-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f41b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f41c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f41e-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f423-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f427-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f429-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f42c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f431-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f436-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f43b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f440-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f447-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f448-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f44a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f44b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f44d-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f44e-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f450-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f451-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f453-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f454-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f456-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f457-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f459-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f45a-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f45c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f45d-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f45f-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f460-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f462-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f463-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f465-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f466-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f468-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f469-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f46b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f46c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f46e-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f46f-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f471-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f472-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f474-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f475-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f477-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f478-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f47a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f47b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f47d-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f47e-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f480-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f481-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f483-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f484-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f486-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f487-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f489-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f48a-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f48c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f48d-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f48f-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f490-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f492-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f493-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f495-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f496-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f498-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f499-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f49b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f49c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f49e-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f49f-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4a4-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4a8-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4aa-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4ad-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4b2-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4b7-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4bc-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4c1-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4c8-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4c9-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4cb-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4cc-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4ce-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4cf-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4d1-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4d2-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4d4-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4d5-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4d7-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4d8-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4da-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4db-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4dd-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4de-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4e0-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4e1-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4e3-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4e4-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4e6-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4e7-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4e9-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4ea-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4ec-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4ed-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4ef-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4f0-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4f2-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4f3-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4f5-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4f6-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4f8-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4f9-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4fb-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4fc-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f4fe-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f4ff-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f501-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f502-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f504-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f505-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f507-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f508-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f50a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f50b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f50d-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f50e-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f510-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f511-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f513-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f514-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f516-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f517-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f519-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f51a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f51c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f51d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f51f-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f520-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f525-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f528-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f529-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f52b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f52c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f52e-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f52f-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f531-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f532-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f534-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f535-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f537-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f538-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f53a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f53b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f53d-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f53e-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f540-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f541-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f543-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f544-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f546-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f547-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f549-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f54a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f54c-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f54d-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f54f-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f550-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f552-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f553-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f555-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f556-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f558-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f559-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f55b-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f55c-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f55e-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f55f-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f561-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f562-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f564-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f565-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f567-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f568-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f65a-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f65e-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f65f-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f663-e717c767, #w-node-ed9131e0-726d-8063-4442-d5da25f8f665-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f666-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f66a-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f66b-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f66f-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f670-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f674-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f675-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f679-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f67a-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f67e-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f67f-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f683-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f684-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f688-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f689-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f68d-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f68e-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f692-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f693-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f696-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f697-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f69b-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f69c-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6a0-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6a1-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6a5-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6a6-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6aa-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6ab-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6af-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6b0-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6b4-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6b5-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6b9-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6ba-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6be-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6bf-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6c3-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6c4-e717c767 {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6c8-e717c767 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f6c9-e717c767 {
  justify-self: start;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-e717c768 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-e717c768 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-e717c768 {
  justify-self: end;
}

#w-node-e7646857-a131-c976-9269-62c243ad606e-e717c768, #w-node-e7646857-a131-c976-9269-62c243ad6074-e717c768, #w-node-e7646857-a131-c976-9269-62c243ad607a-e717c768, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-e717c768 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-e717c768, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-e717c768 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_5dedc64d-da96-5107-79d4-1c23a05da388-e717c768, #w-node-e8606db9-45c7-4755-ca51-a5cbf99422d8-e717c768, #w-node-e10b277a-c69f-1b08-0c63-391848d2c4db-e717c768 {
  place-self: center end;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efbf-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc0-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc4-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc5-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efc9-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efca-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efce-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efcf-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd3-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd4-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd8-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efd9-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe3-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe4-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe8-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efe9-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efed-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8efee-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff2-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff3-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff7-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8eff8-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8effc-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8effd-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f007-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f008-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f00c-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f00d-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f011-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f012-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f016-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f017-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f020-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f021-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f02a-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f02b-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f03d-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f03e-e717c76d {
  justify-self: start;
}

#w-node-_1d49633c-b1f3-b624-4b33-fa9d4c806ac2-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_1d49633c-b1f3-b624-4b33-fa9d4c806ac3-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f042-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f043-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f047-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f048-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f04c-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f04d-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f051-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f052-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f05c-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f05d-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f061-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f062-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f066-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f067-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f06b-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f06c-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f070-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f071-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f075-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f076-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f081-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f082-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f086-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f087-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f08b-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f08c-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f090-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f091-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f095-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f096-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f09a-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f09b-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f09f-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a0-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a4-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a5-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0a9-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0aa-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0ae-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0af-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0b9-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0ba-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0be-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0bf-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0c3-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0c4-e717c76d {
  justify-self: start;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0cb-e717c76d {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-ed9131e0-726d-8063-4442-d5da25f8f0cc-e717c76d {
  justify-self: start;
}

#w-node-_728ec1b7-702a-083e-4101-d6c6fa104103-e717c76e {
  align-self: end;
}

#w-node-e0c6bddd-def3-6d01-47c1-5f2596f8460a-e717c76e {
  grid-area: 1 / 1 / 2 / 2;
}

#w-node-e0c6bddd-def3-6d01-47c1-5f2596f8460b-e717c76e {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-d25f582d-1950-2a4f-7e73-1bfd920af9cd-e717c76e {
  grid-area: 1 / 1 / 2 / 2;
}

#w-node-d25f582d-1950-2a4f-7e73-1bfd920af9ce-e717c76e {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_90e4d28f-4ec5-628a-01a7-edcbb11105b2-e717c76e {
  grid-area: 1 / 1 / 2 / 2;
}

#w-node-_90e4d28f-4ec5-628a-01a7-edcbb11105b3-e717c76e {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_4b44ba79-87a8-7181-1713-1133ae432c2d-e717c76e {
  grid-area: 1 / 1 / 2 / 2;
}

#w-node-_4b44ba79-87a8-7181-1713-1133ae432c2e-e717c76e {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-e24f7ec0-5f5a-0aa7-5edf-d941e19e096f-e717c782 {
  align-self: auto;
}

#w-node-e24f7ec0-5f5a-0aa7-5edf-d941e19e0970-e717c782 {
  grid-area: span 1 / span 3 / span 1 / span 3;
  align-self: auto;
}

#w-node-e24f7ec0-5f5a-0aa7-5edf-d941e19e0982-e717c782 {
  grid-area: span 1 / span 2 / span 1 / span 2;
}

#w-node-_7420c7bb-363f-b643-79b8-7ee65707d38f-e717c782 {
  place-self: auto;
}

#w-node-e820f1af-4822-173a-523b-6a3becb28194-e717c783, #w-node-e820f1af-4822-173a-523b-6a3becb28195-e717c783, #w-node-e820f1af-4822-173a-523b-6a3becb2819d-e717c783, #w-node-e820f1af-4822-173a-523b-6a3becb281a5-e717c783, #w-node-b3b29188-b739-a8bc-7684-dc3f9d41087d-e717c783 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-b3b29188-b739-a8bc-7684-dc3f9d41087e-e717c783 {
  grid-area: span 1 / span 3 / span 1 / span 3;
}

#w-node-b3b29188-b739-a8bc-7684-dc3f9d41088e-e717c783 {
  grid-area: span 1 / span 2 / span 1 / span 2;
}

#w-node-_06951a73-1c84-1bd2-93ed-f0ebd4b4fa7f-e717c783, #w-node-_06951a73-1c84-1bd2-93ed-f0ebd4b4fa80-e717c783, #w-node-_06951a73-1c84-1bd2-93ed-f0ebd4b4fa88-e717c783, #w-node-_06951a73-1c84-1bd2-93ed-f0ebd4b4fa90-e717c783, #w-node-_6039df62-34d2-0cc1-19ad-fc513eaac555-e717c783, #w-node-_6039df62-34d2-0cc1-19ad-fc513eaac556-e717c783, #w-node-_6039df62-34d2-0cc1-19ad-fc513eaac567-e717c783, #w-node-_6039df62-34d2-0cc1-19ad-fc513eaac578-e717c783, #w-node-bec5be7c-7631-1c76-6729-55f8842b80d9-e717c783, #w-node-bec5be7c-7631-1c76-6729-55f8842b80da-e717c783, #w-node-bec5be7c-7631-1c76-6729-55f8842b80e2-e717c783, #w-node-bec5be7c-7631-1c76-6729-55f8842b80ea-e717c783, #w-node-_297fd18f-a7b6-d2af-1747-b57f47938696-e717c783, #w-node-_297fd18f-a7b6-d2af-1747-b57f47938697-e717c783, #w-node-_297fd18f-a7b6-d2af-1747-b57f4793869f-e717c783, #w-node-_297fd18f-a7b6-d2af-1747-b57f479386a7-e717c783 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_2008e925-160a-9fb1-9bc9-53b150ff6c53-e717c783 {
  order: 0;
  justify-self: stretch;
}

#w-node-_2008e925-160a-9fb1-9bc9-53b150ff6c66-e717c783 {
  justify-self: end;
}

#w-node-a08621bd-6ebe-ad18-5b63-9d61b32f041a-e717c785, #w-node-_6e11b3d8-a424-edd9-098e-e11ffc9d0fe2-e717c785, #w-node-f2fb720b-ce81-6393-dd28-7b6ee84425bc-e717c785 {
  align-self: center;
}

#w-node-cfc31b3d-d96b-6949-f32e-59fa9252f09d-e717c798 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_65f68502-8d60-f4d7-5938-876c340f0974-e717c798, #w-node-_65f68502-8d60-f4d7-5938-876c340f097a-e717c798, #w-node-_65f68502-8d60-f4d7-5938-876c340f0980-e717c798 {
  align-self: start;
}

#w-node-_276aa052-235c-5692-08d5-cf28d7c82d55-e717c798 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_276aa052-235c-5692-08d5-cf28d7c82d59-e717c798, #w-node-_276aa052-235c-5692-08d5-cf28d7c82d5f-e717c798, #w-node-_276aa052-235c-5692-08d5-cf28d7c82d65-e717c798 {
  align-self: start;
}

#w-node-d81b36c1-a505-7b0d-2dc1-c0c3a205ee25-e717c798 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-d81b36c1-a505-7b0d-2dc1-c0c3a205ee29-e717c798, #w-node-d81b36c1-a505-7b0d-2dc1-c0c3a205ee2f-e717c798, #w-node-d81b36c1-a505-7b0d-2dc1-c0c3a205ee35-e717c798 {
  align-self: start;
}

#w-node-_480f4acc-a223-3bd7-5ebf-9614cf628125-e717c798 {
  justify-self: auto;
}

#w-node-_480f4acc-a223-3bd7-5ebf-9614cf62812c-e717c798 {
  justify-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-d6908b06 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-d6908b06 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-d6908b06 {
  justify-self: end;
}

#w-node-e7646857-a131-c976-9269-62c243ad606e-d6908b06, #w-node-e7646857-a131-c976-9269-62c243ad6074-d6908b06, #w-node-e7646857-a131-c976-9269-62c243ad607a-d6908b06, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-d6908b06, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-d6908b06 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-d6908b06, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-d6908b06 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_5dedc64d-da96-5107-79d4-1c23a05da388-d6908b06, #w-node-e8606db9-45c7-4755-ca51-a5cbf99422d8-d6908b06, #w-node-e10b277a-c69f-1b08-0c63-391848d2c4db-d6908b06 {
  place-self: center end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-e685a94f {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-e685a94f {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-e685a94f {
  justify-self: end;
}

#w-node-e7646857-a131-c976-9269-62c243ad606e-e685a94f, #w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-e685a94f, #w-node-e7646857-a131-c976-9269-62c243ad6074-e685a94f, #w-node-e7646857-a131-c976-9269-62c243ad607a-e685a94f, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-e685a94f, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-e685a94f {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-e685a94f, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-e685a94f {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-7b323e66 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-7b323e66 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-7b323e66 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-7b323e66, #w-node-e7646857-a131-c976-9269-62c243ad606e-7b323e66, #w-node-e7646857-a131-c976-9269-62c243ad6074-7b323e66, #w-node-e7646857-a131-c976-9269-62c243ad607a-7b323e66, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-7b323e66, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-7b323e66 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-7b323e66, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-7b323e66 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-f8f12ca9 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-f8f12ca9 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-f8f12ca9 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-f8f12ca9, #w-node-e7646857-a131-c976-9269-62c243ad606e-f8f12ca9, #w-node-e7646857-a131-c976-9269-62c243ad6074-f8f12ca9, #w-node-e7646857-a131-c976-9269-62c243ad607a-f8f12ca9, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-f8f12ca9, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-f8f12ca9, #w-node-dfc14fec-6e48-3f8a-0f8e-422778bce9c2-f8f12ca9 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-f8f12ca9, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-f8f12ca9 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-a0dc727c {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-a0dc727c {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-a0dc727c {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-a0dc727c, #w-node-e7646857-a131-c976-9269-62c243ad606e-a0dc727c, #w-node-e7646857-a131-c976-9269-62c243ad6074-a0dc727c, #w-node-e7646857-a131-c976-9269-62c243ad607a-a0dc727c, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-a0dc727c, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-a0dc727c {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-a0dc727c, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-a0dc727c {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-79e9f720 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-79e9f720 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-79e9f720 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-79e9f720, #w-node-e7646857-a131-c976-9269-62c243ad606e-79e9f720, #w-node-e7646857-a131-c976-9269-62c243ad6074-79e9f720, #w-node-e7646857-a131-c976-9269-62c243ad607a-79e9f720, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-79e9f720, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-79e9f720 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-79e9f720, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-79e9f720 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-cbc3d4f4 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-cbc3d4f4 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-cbc3d4f4 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-cbc3d4f4, #w-node-e7646857-a131-c976-9269-62c243ad606e-cbc3d4f4, #w-node-e7646857-a131-c976-9269-62c243ad6074-cbc3d4f4, #w-node-e7646857-a131-c976-9269-62c243ad607a-cbc3d4f4, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-cbc3d4f4, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-cbc3d4f4 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-cbc3d4f4, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-cbc3d4f4 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-2f5743c0 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2f5743c0 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-2f5743c0 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-2f5743c0, #w-node-e7646857-a131-c976-9269-62c243ad606e-2f5743c0, #w-node-e7646857-a131-c976-9269-62c243ad6074-2f5743c0, #w-node-e7646857-a131-c976-9269-62c243ad607a-2f5743c0, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-2f5743c0, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-2f5743c0 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-2f5743c0, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-2f5743c0 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-2899cc8c {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2899cc8c {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-2899cc8c {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-2899cc8c, #w-node-e7646857-a131-c976-9269-62c243ad606e-2899cc8c, #w-node-e7646857-a131-c976-9269-62c243ad6074-2899cc8c, #w-node-e7646857-a131-c976-9269-62c243ad607a-2899cc8c, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-2899cc8c, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-2899cc8c {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-2899cc8c, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-2899cc8c {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-503ce0c1 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-503ce0c1 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-503ce0c1 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-503ce0c1, #w-node-e7646857-a131-c976-9269-62c243ad606e-503ce0c1, #w-node-e7646857-a131-c976-9269-62c243ad6074-503ce0c1, #w-node-e7646857-a131-c976-9269-62c243ad607a-503ce0c1, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-503ce0c1, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-503ce0c1 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-503ce0c1, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-503ce0c1 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-d08eddee {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-d08eddee {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-d08eddee {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-d08eddee, #w-node-e7646857-a131-c976-9269-62c243ad606e-d08eddee, #w-node-e7646857-a131-c976-9269-62c243ad6074-d08eddee, #w-node-e7646857-a131-c976-9269-62c243ad607a-d08eddee, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-d08eddee, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-d08eddee {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-d08eddee {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-5f874676 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-5f874676 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-5f874676 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-5f874676, #w-node-e7646857-a131-c976-9269-62c243ad606e-5f874676, #w-node-e7646857-a131-c976-9269-62c243ad6074-5f874676, #w-node-e7646857-a131-c976-9269-62c243ad607a-5f874676, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-5f874676, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-5f874676 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-5f874676, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-5f874676 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-c6d60cfc {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-c6d60cfc {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-c6d60cfc {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-c6d60cfc, #w-node-e7646857-a131-c976-9269-62c243ad606e-c6d60cfc, #w-node-e7646857-a131-c976-9269-62c243ad6074-c6d60cfc, #w-node-e7646857-a131-c976-9269-62c243ad607a-c6d60cfc, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-c6d60cfc, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-c6d60cfc, #w-node-_1374cce5-1974-3ca5-8840-7ab04a7ed1c7-c6d60cfc {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-c6d60cfc {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-fbf63665 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-fbf63665 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-fbf63665 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-fbf63665, #w-node-e7646857-a131-c976-9269-62c243ad606e-fbf63665, #w-node-e7646857-a131-c976-9269-62c243ad6074-fbf63665, #w-node-e7646857-a131-c976-9269-62c243ad607a-fbf63665, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-fbf63665, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-fbf63665, #w-node-c4912a61-00fd-8874-6826-f17e13560c6e-fbf63665 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-fbf63665 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-2be64902 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2be64902 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-2be64902 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-2be64902, #w-node-e7646857-a131-c976-9269-62c243ad606e-2be64902, #w-node-e7646857-a131-c976-9269-62c243ad6074-2be64902, #w-node-e7646857-a131-c976-9269-62c243ad607a-2be64902, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-2be64902, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-2be64902 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-2be64902, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-2be64902 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-6cd8610c {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-6cd8610c {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-6cd8610c {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-6cd8610c, #w-node-e7646857-a131-c976-9269-62c243ad606e-6cd8610c, #w-node-e7646857-a131-c976-9269-62c243ad6074-6cd8610c, #w-node-e7646857-a131-c976-9269-62c243ad607a-6cd8610c, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-6cd8610c, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-6cd8610c {
  align-self: start;
}

#w-node-_14dbcc64-6ea4-0f58-caae-ceaa7b06eab1-6cd8610c {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-9eea27b6 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9eea27b6 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-9eea27b6 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-9eea27b6, #w-node-e7646857-a131-c976-9269-62c243ad606e-9eea27b6, #w-node-e7646857-a131-c976-9269-62c243ad6074-9eea27b6, #w-node-e7646857-a131-c976-9269-62c243ad607a-9eea27b6, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-9eea27b6, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-9eea27b6 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-9eea27b6, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-9eea27b6 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-6ce05041 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-6ce05041 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-6ce05041 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-6ce05041, #w-node-e7646857-a131-c976-9269-62c243ad606e-6ce05041, #w-node-e7646857-a131-c976-9269-62c243ad6074-6ce05041, #w-node-e7646857-a131-c976-9269-62c243ad607a-6ce05041, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-6ce05041, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-6ce05041 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-6ce05041, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-6ce05041 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-0ca9833b {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-0ca9833b {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-0ca9833b {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-0ca9833b, #w-node-e7646857-a131-c976-9269-62c243ad606e-0ca9833b, #w-node-e7646857-a131-c976-9269-62c243ad6074-0ca9833b, #w-node-e7646857-a131-c976-9269-62c243ad607a-0ca9833b, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-0ca9833b, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-0ca9833b {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-0ca9833b, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-0ca9833b {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-da0c3a61 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-da0c3a61 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-da0c3a61 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-da0c3a61, #w-node-e7646857-a131-c976-9269-62c243ad606e-da0c3a61, #w-node-e7646857-a131-c976-9269-62c243ad6074-da0c3a61, #w-node-e7646857-a131-c976-9269-62c243ad607a-da0c3a61, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-da0c3a61, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-da0c3a61 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-da0c3a61, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-da0c3a61 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-9311795a {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9311795a {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-9311795a {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-9311795a, #w-node-e7646857-a131-c976-9269-62c243ad606e-9311795a, #w-node-e7646857-a131-c976-9269-62c243ad6074-9311795a, #w-node-e7646857-a131-c976-9269-62c243ad607a-9311795a, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-9311795a, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-9311795a {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-9311795a, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-9311795a {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-2514d829 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2514d829 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-2514d829 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-2514d829, #w-node-e7646857-a131-c976-9269-62c243ad606e-2514d829, #w-node-e7646857-a131-c976-9269-62c243ad6074-2514d829, #w-node-e7646857-a131-c976-9269-62c243ad607a-2514d829, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-2514d829, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-2514d829 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-2514d829, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-2514d829 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-d7112355 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-d7112355 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-d7112355 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-d7112355, #w-node-e7646857-a131-c976-9269-62c243ad606e-d7112355, #w-node-e7646857-a131-c976-9269-62c243ad6074-d7112355, #w-node-e7646857-a131-c976-9269-62c243ad607a-d7112355, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-d7112355, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-d7112355 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-d7112355, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-d7112355 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-8de6c557 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-8de6c557 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-8de6c557 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-8de6c557, #w-node-e7646857-a131-c976-9269-62c243ad606e-8de6c557, #w-node-e7646857-a131-c976-9269-62c243ad6074-8de6c557, #w-node-e7646857-a131-c976-9269-62c243ad607a-8de6c557, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-8de6c557, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-8de6c557 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-8de6c557, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-8de6c557 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-0802ca71 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-0802ca71 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-0802ca71 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-0802ca71, #w-node-e7646857-a131-c976-9269-62c243ad606e-0802ca71, #w-node-e7646857-a131-c976-9269-62c243ad6074-0802ca71, #w-node-e7646857-a131-c976-9269-62c243ad607a-0802ca71, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-0802ca71, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-0802ca71 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-0802ca71, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-0802ca71 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-ea96a954 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-ea96a954 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-ea96a954 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-ea96a954, #w-node-e7646857-a131-c976-9269-62c243ad606e-ea96a954, #w-node-e7646857-a131-c976-9269-62c243ad6074-ea96a954, #w-node-e7646857-a131-c976-9269-62c243ad607a-ea96a954, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-ea96a954, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-ea96a954 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-ea96a954 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-0aaf30d1 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-0aaf30d1 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-0aaf30d1 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-0aaf30d1, #w-node-e7646857-a131-c976-9269-62c243ad606e-0aaf30d1, #w-node-e7646857-a131-c976-9269-62c243ad6074-0aaf30d1, #w-node-e7646857-a131-c976-9269-62c243ad607a-0aaf30d1, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-0aaf30d1, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-0aaf30d1 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-0aaf30d1 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-87493a23 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-87493a23 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-87493a23 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-87493a23, #w-node-e7646857-a131-c976-9269-62c243ad606e-87493a23, #w-node-e7646857-a131-c976-9269-62c243ad6074-87493a23, #w-node-e7646857-a131-c976-9269-62c243ad607a-87493a23, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-87493a23, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-87493a23 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-87493a23, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-87493a23 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-b9435c78 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-b9435c78 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-b9435c78 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-b9435c78, #w-node-e7646857-a131-c976-9269-62c243ad606e-b9435c78, #w-node-e7646857-a131-c976-9269-62c243ad6074-b9435c78, #w-node-e7646857-a131-c976-9269-62c243ad607a-b9435c78, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-b9435c78, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-b9435c78 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-b9435c78, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-b9435c78 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-9d0c7bee {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9d0c7bee {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-9d0c7bee {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-9d0c7bee, #w-node-e7646857-a131-c976-9269-62c243ad606e-9d0c7bee, #w-node-e7646857-a131-c976-9269-62c243ad6074-9d0c7bee, #w-node-e7646857-a131-c976-9269-62c243ad607a-9d0c7bee, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-9d0c7bee, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-9d0c7bee {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-9d0c7bee {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-749b9ae9 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-749b9ae9 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-749b9ae9 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-749b9ae9, #w-node-e7646857-a131-c976-9269-62c243ad606e-749b9ae9, #w-node-e7646857-a131-c976-9269-62c243ad6074-749b9ae9, #w-node-e7646857-a131-c976-9269-62c243ad607a-749b9ae9, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-749b9ae9, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-749b9ae9 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-749b9ae9, #w-node-_56c2bea1-8b06-d9fe-4949-1541e4474ec5-749b9ae9 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-9c6bfe73 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9c6bfe73 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-9c6bfe73 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-9c6bfe73, #w-node-e7646857-a131-c976-9269-62c243ad606e-9c6bfe73, #w-node-e7646857-a131-c976-9269-62c243ad6074-9c6bfe73, #w-node-e7646857-a131-c976-9269-62c243ad607a-9c6bfe73, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-9c6bfe73, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-9c6bfe73 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-9c6bfe73, #w-node-f47b56f8-ba11-b797-f7e5-ec6f07a3deed-9c6bfe73 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-14503724 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-14503724 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-14503724 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-14503724, #w-node-e7646857-a131-c976-9269-62c243ad606e-14503724, #w-node-e7646857-a131-c976-9269-62c243ad6074-14503724, #w-node-e7646857-a131-c976-9269-62c243ad607a-14503724, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-14503724, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-14503724 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-14503724 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-4267e076 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-4267e076 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-4267e076 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-4267e076, #w-node-e7646857-a131-c976-9269-62c243ad606e-4267e076, #w-node-e7646857-a131-c976-9269-62c243ad6074-4267e076, #w-node-e7646857-a131-c976-9269-62c243ad607a-4267e076, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-4267e076, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-4267e076 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-4267e076, #w-node-f47b56f8-ba11-b797-f7e5-ec6f07a3deed-4267e076 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-f91db6f4 {
  grid-area: 1 / 1 / 2 / 3;
  align-self: end;
}

#w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-f91db6f4 {
  grid-area: 2 / 1 / 3 / 4;
  justify-self: auto;
}

#w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-f91db6f4 {
  justify-self: end;
}

#w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-f91db6f4, #w-node-e7646857-a131-c976-9269-62c243ad606e-f91db6f4, #w-node-e7646857-a131-c976-9269-62c243ad6074-f91db6f4, #w-node-e7646857-a131-c976-9269-62c243ad607a-f91db6f4, #w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-f91db6f4, #w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-f91db6f4 {
  align-self: start;
}

#w-node-_34f9201c-9894-0354-22d4-83299abd871d-f91db6f4 {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_149c2370-48b9-74b6-0d06-0de7959b81fe-7187006a {
  align-self: start;
}

#submit-btn.w-node-_1ab0288c-ce23-3ca2-ba97-59016162d8c9-6162d873 {
  justify-self: start;
}

#w-node-eb3cdbcf-3dca-761a-2ae6-dc27e0ba0f3f-e0ba0f3e, #w-node-eb3cdbcf-3dca-761a-2ae6-dc27e0ba0f44-e0ba0f3e, #w-node-eb3cdbcf-3dca-761a-2ae6-dc27e0ba0f48-e0ba0f3e, #w-node-eb3cdbcf-3dca-761a-2ae6-dc27e0ba0f52-e0ba0f3e {
  grid-area: span 1 / span 1 / span 1 / span 1;
}

#w-node-_51d9177a-c709-775a-61a2-3b32f2031b2c-182d6528, #w-node-_51d9177a-c709-775a-61a2-3b32f2031b32-182d6528, #w-node-_51d9177a-c709-775a-61a2-3b32f2031b38-182d6528, #w-node-_72266d54-5df6-7a64-9088-344c8bc23bb5-182d6528, #w-node-_72266d54-5df6-7a64-9088-344c8bc23bbb-182d6528, #w-node-_72266d54-5df6-7a64-9088-344c8bc23bc1-182d6528, #w-node-aee9266a-f48c-373f-7028-4c86f0a5533d-182d6528, #w-node-aee9266a-f48c-373f-7028-4c86f0a55343-182d6528, #w-node-aee9266a-f48c-373f-7028-4c86f0a55349-182d6528, #w-node-_3209c964-a010-ef8c-ef24-19293d8d9e14-182d6528, #w-node-_3209c964-a010-ef8c-ef24-19293d8d9e1a-182d6528, #w-node-_3209c964-a010-ef8c-ef24-19293d8d9e20-182d6528, #w-node-_8c387e4f-2224-9aea-8be6-fb1a9e93ffc9-182d6528, #w-node-_149c2370-48b9-74b6-0d06-0de7959b81fe-d285da46 {
  align-self: start;
}

@media screen and (max-width: 991px) {
  #w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217ef-e717c714 {
    justify-self: start;
  }

  #w-node-fabb9201-1166-ecd7-5f4a-d3d41c8c6ab1-e717c714 {
    align-self: stretch;
  }

  #w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b1a-398b1b13 {
    grid-column-end: 3;
  }

  #w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b2d-398b1b13 {
    grid-area: span 2 / span 2 / span 2 / span 2;
  }

  #w-node-a08621bd-6ebe-ad18-5b63-9d61b32f0436-e717c785, #w-node-_6e11b3d8-a424-edd9-098e-e11ffc9d0ff8-e717c785, #w-node-f2fb720b-ce81-6393-dd28-7b6ee84425d2-e717c785 {
    align-self: stretch;
  }
}

@media screen and (max-width: 767px) {
  #w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217ef-e717c714 {
    justify-self: start;
  }

  #w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b2d-398b1b13 {
    grid-column: span 2 / span 2;
  }

  #w-node-_133a7bc0-1c8b-2514-e4cc-776b4b4befd4-e717c767 {
    order: -9999;
  }

  #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-e717c768 {
    grid-area: 2 / 1 / 3 / 3;
    place-self: auto start;
  }

  #w-node-_5dedc64d-da96-5107-79d4-1c23a05da376-e717c768, #w-node-e8606db9-45c7-4755-ca51-a5cbf99422d3-e717c768, #w-node-e10b277a-c69f-1b08-0c63-391848d2c4d6-e717c768 {
    order: -9999;
  }

  #w-node-e24f7ec0-5f5a-0aa7-5edf-d941e19e0982-e717c782 {
    grid-column: span 1 / span 1;
    justify-self: stretch;
  }

  #w-node-_2008e925-160a-9fb1-9bc9-53b150ff6c66-e717c783 {
    justify-self: start;
  }

  #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-d6908b06 {
    grid-area: 2 / 1 / 3 / 3;
    place-self: auto start;
  }

  #w-node-_5dedc64d-da96-5107-79d4-1c23a05da376-d6908b06, #w-node-e8606db9-45c7-4755-ca51-a5cbf99422d3-d6908b06, #w-node-e10b277a-c69f-1b08-0c63-391848d2c4d6-d6908b06 {
    order: -9999;
  }

  #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-e685a94f, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-7b323e66, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-f8f12ca9, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-a0dc727c, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-79e9f720, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-cbc3d4f4, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2f5743c0, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2899cc8c, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-503ce0c1, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-d08eddee, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-5f874676, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-c6d60cfc, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-fbf63665, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2be64902, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-6cd8610c, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9eea27b6, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-6ce05041, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-0ca9833b, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-da0c3a61, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9311795a, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-2514d829, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-d7112355, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-8de6c557, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-0802ca71, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-ea96a954, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-0aaf30d1, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-87493a23, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-b9435c78, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9d0c7bee, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-749b9ae9, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9c6bfe73, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-14503724, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-4267e076, #w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-f91db6f4 {
    grid-area: 2 / 1 / 3 / 3;
    place-self: auto start;
  }

  #w-node-e70b951f-8c83-b8d2-a6d1-c44bbf3b3252-182d6528, #w-node-e70b951f-8c83-b8d2-a6d1-c44bbf3b326e-182d6528, #w-node-_3c6d7a87-b462-26fd-19f7-86847e11baea-182d6528, #w-node-dac3e1a6-8a42-3bde-1edd-d1715a74de91-182d6528 {
    grid-area: span 1 / span 1 / span 1 / span 1;
  }

  #w-node-b2a8dd2e-498b-3f37-4044-17bca4926e5a-182d6528, #w-node-_07826999-f368-156f-2c7f-2d75d3508091-182d6528 {
    order: 9999;
  }
}

@media screen and (max-width: 479px) {
  #w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b2d-398b1b13 {
    grid-column: span 2 / span 2;
  }

  #w-node-e24f7ec0-5f5a-0aa7-5edf-d941e19e0970-e717c782, #w-node-e24f7ec0-5f5a-0aa7-5edf-d941e19e0982-e717c782 {
    grid-column: span 1 / span 1;
  }

  #w-node-_480f4acc-a223-3bd7-5ebf-9614cf62812c-e717c798 {
    justify-self: start;
  }

  #w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-d6908b06 {
    align-self: center;
  }
}


@font-face {
  font-family: 'Overused Grotesk Woff';
  src: url('../fonts/OverusedGrotesk-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Overused Grotesk Woff';
  src: url('../fonts/OverusedGrotesk-Roman.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Overused Grotesk Woff';
  src: url('../fonts/OverusedGrotesk-Light.woff2') format('woff2');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Overused Grotesk Woff';
  src: url('../fonts/OverusedGrotesk-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Overused Grotesk';
  src: url('../fonts/OverusedGrotesk-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Overused Grotesk';
  src: url('../fonts/OverusedGrotesk-Roman.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Overused Grotesk';
  src: url('../fonts/OverusedGrotesk-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Overusedgrotesk';
  src: url('../fonts/OverusedGrotesk-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}