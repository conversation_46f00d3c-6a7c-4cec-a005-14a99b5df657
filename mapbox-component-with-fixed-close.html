<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Mapbox Component - With Fixed Close Button</title>
    
    <style>
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            overscroll-behavior-y: contain;
        }
        
        /* Container styles */
        .container {
            width: 100%;
            height: auto;
            margin-left: auto;
            margin-right: auto;
            display: block;
            position: relative;
        }
        
        .container.is-map {
            height: 100dvh;
        }
        
        /* Section styles */
        .section {
            display: flex;
        }
        
        .section.mapbox-section {
            min-height: 100%;
            overflow: visible;
        }
        
        /* Mapbox wrapper */
        .mapbox-wrapper {
            height: 100vh;
            position: relative;
            overflow: visible;
        }
        
        /* Map container */
        .mapbox-wrap {
            z-index: 1;
            background-color: #222;
            flex: 1;
            width: 100%;
            height: 100%;
            position: relative;
            overflow: visible;
        }
        
        /* Location map wrappers */
        .locations-map_wrapper {
            z-index: 20;
            width: 25em;
            margin: 1em 1em 1em -27.3em;
            transition: margin .2s;
            display: block;
            position: absolute;
            left: 0;
            top: 0;
            bottom: auto;
            right: auto;
        }
        
        .locations-map_wrapper.is--show {
            align-items: flex-start;
            margin-left: 1em;
            display: flex;
        }
        
        .locations-map_list {
            z-index: 2;
            display: block;
            position: relative;
        }
        
        /* Location map items */
        .locations-map_item {
            color: #000;
            display: none;
        }
        
        .locations-map_item.is--show {
            height: auto;
            display: block;
        }
        
        .locations-map_card {
            display: none;
        }
        
        /* Location map card styles */
        .location-map_card-wrap {
            z-index: 5;
            background-color: #fff;
            border-radius: 5px;
            min-width: 20em;
            max-width: 20em;
            margin-bottom: 1rem;
            padding: 0;
            transition: all .2s;
            display: block;
            position: relative;
            overflow: hidden;
            box-shadow: 1px 1px 3px rgba(0,0,0,0.15);
        }
        
        .location-map_card-text-wrap {
            padding: 1.5rem 1rem;
        }
        
        /* Office map styles */
        .office-map_list {
            display: block;
        }
        
        .office-map_item {
            color: #000;
            display: none;
        }
        
        .office-map_card-wrap {
            z-index: 5;
            background-color: #fff;
            border-radius: 5px;
            min-width: 20em;
            max-width: 20em;
            margin-bottom: 1rem;
            padding: 0;
            transition: all .2s;
            display: block;
            position: relative;
            overflow: hidden;
            box-shadow: 1px 1px 3px rgba(0,0,0,0.15);
        }
        
        .office-map_card-text-wrap {
            padding: 1.5rem 1rem;
        }
        
        .office-map_card {
            display: none;
        }
        
        /* Map highlight buttons */
        .map-highlight-buttons {
            z-index: 17;
            gap: 1rem;
            justify-content: center;
            align-items: center;
            width: 100%;
            margin-top: 0;
            margin-bottom: 0;
            padding: 1rem;
            display: flex;
            position: absolute;
            bottom: 5%;
            left: 0;
            right: 0;
        }
        
        /* Button styles */
        .button-2 {
            background-color: #ced2dd;
            border-radius: 5px;
            padding: 12px 24px;
            text-decoration: none;
            color: #000;
            font-weight: 500;
            transition: all 0.2s;
            display: inline-block;
            border: none;
            cursor: pointer;
        }
        
        .button-2:hover {
            background-color: #b8bcc7;
        }
        
        .w-button {
            display: inline-block;
            padding: 9px 15px;
            background-color: #3898EC;
            color: white;
            border: 0;
            line-height: inherit;
            text-decoration: none;
            cursor: pointer;
            border-radius: 0;
        }
        
        /* Mapbox GL styles */
        .mapboxgl-popup-content {
            pointer-events: auto;
            border-radius: 4px;
            box-shadow: none;
            padding: 12px 16px;
            color: #161616;
            background-color: #fff;
        }
        
        .mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
            border-top-color: #fefae0;
        }
        
        .mapboxgl-canvas {
            touch-action: manipulation !important;
        }
        
        /* Project type styles */
        .project-type-tag {
            display: inline-block;
            background-color: #f0f0f0;
            color: #333;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 12px;
            font-weight: 500;
        }
        
        .project-type {
            font-size: 13px;
            color: #666;
            margin-top: 4px;
        }
        
        .card_activity-title {
            font-weight: 600;
            font-size: 16px;
            margin-top: 12px;
            margin-bottom: 8px;
        }
        
        .card_activity {
            font-size: 14px;
            line-height: 1.5;
            color: #333;
        }
        
        .card_heading {
            line-height: 1 !important;
        }
        
        /* FIXED CLOSE BUTTON STYLES */
        /* Hide the original arrow icon */
        .close-block .icon-embed-xxsmall svg {
            display: none;
        }
        
        /* Style the close button container as a circular X button */
        .close-block {
            position: absolute !important;
            top: 15px !important;
            right: 15px !important;
            width: 32px !important;
            height: 32px !important;
            background-color: rgba(255, 255, 255, 0.95) !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            z-index: 1000 !important;
            transition: background-color 0.2s ease !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
            border: none !important;
            padding: 0 !important;
        }
        
        .close-block:hover {
            background-color: rgba(255, 255, 255, 1) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
        }
        
        /* Create X icon using Unicode character */
        .close-block .icon-embed-xxsmall::before {
            content: '×';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 20px;
            font-weight: bold;
            color: #333;
            line-height: 1;
        }
        
        /* Ensure the icon container is positioned correctly */
        .close-block .icon-embed-xxsmall {
            position: relative;
            width: 18px !important;
            height: 18px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }
        
        /* Mobile close button styling (already has correct X icon) */
        .mobile-close-button {
            position: absolute !important;
            top: 15px !important;
            right: 15px !important;
            width: 32px !important;
            height: 32px !important;
            background-color: rgba(255, 255, 255, 0.95) !important;
            border-radius: 50% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            cursor: pointer !important;
            z-index: 1000 !important;
            transition: background-color 0.2s ease !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
            border: none !important;
        }
        
        .mobile-close-button:hover {
            background-color: rgba(255, 255, 255, 1) !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
        }
        
        .mobile-close-button svg {
            width: 18px !important;
            height: 18px !important;
            color: #333 !important;
        }
        
        /* Responsive styles */
        @media screen and (max-width: 767px) {
            .mobile-close-button {
                display: flex !important;
            }
            
            .close-block {
                display: none !important;
            }
            
            .container.is-map {
                height: 90vh;
            }
            
            .locations-map_wrapper.is--show {
                height: 75vh;
            }
            
            .location-map_card-wrap {
                min-width: 90vw;
                max-width: 90vw;
            }
            
            .office-map_card-wrap {
                min-width: 90vw;
                max-width: 90vw;
            }
        }
        
        @media screen and (min-width: 768px) {
            .mobile-close-button {
                display: none !important;
            }
            
            .close-block {
                display: flex !important;
            }
        }
        
        @media screen and (max-width: 991px) {
            .mapbox-wrapper {
                height: 100vh;
            }
            
            .container.is-map {
                width: 100%;
                height: 90vh;
            }
            
            .locations-map_wrapper {
                width: 80%;
                margin-left: -30.2em;
            }
            
            .mapbox-wrap {
                height: 100%;
            }
            
            .locations-map_item {
                height: 80vh;
            }
            
            .office-map_item {
                height: 80vh;
            }
        }
    </style>
</head>
<body>
    <!-- Main Mapbox Section -->
    <section class="mapbox-wrapper">
        <div class="section mapbox-section">
            <div class="container is-map">
                <!-- Office locations sidebar -->
                <div class="locations-map_wrapper">
                    <div id="office-list" class="locations-map_list"></div>
                </div>
                
                <!-- Project locations sidebar -->
                <div class="locations-map_wrapper">
                    <div id="location-list" class="locations-map_list"></div>
                </div>
                
                <!-- Map container -->
                <div id="map" class="mapbox-wrap"></div>
            </div>
        </div>
        
        <!-- Map filter buttons -->
        <div class="map-highlight-buttons">
            <a id="show-offices" href="#" class="button-2 w-button">Unsere Büros</a>
            <a id="show-projects" href="#" class="button-2 w-button">Laufende Projekte</a>
        </div>
    </section>

    <!-- External JavaScript Dependencies -->
    <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/mapbox/mapbox-static.js?v=28" defer></script>
    <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/mapbox/locations-data.js?=v5" defer></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.3/jquery.min.js" async></script>
</body>
</html>
