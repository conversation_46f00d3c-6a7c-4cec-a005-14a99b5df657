<!DOCTYPE html><!--  Last Published: Mon Aug 11 2025 09:01:09 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="68257cde3c60ae59e717c76d" data-wf-site="68257cde3c60ae59e717c715" lang="de">
<head>
  <meta charset="utf-8">
  <title>Styleguide_fluid</title>
  <meta content="Styleguide_fluid" property="og:title">
  <meta content="Styleguide_fluid" property="twitter:title">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/woelfli-staging-de009659c94824f4bd1e533.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js" as="script">
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/animation/beautified_scroll_animations_noheader.js?=v7" defer=""></script>
  <script defer="" src="https://cloud.umami.is/script.js" data-website-id="634ce2ab-8a79-4bd9-b693-703b47938caf"></script>
  <style>
body {
  overscroll-behavior-y: contain;
}
  .image-3 {
  content-visibility: auto;
  contain: layout style paint;
}
.text-size-regular {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
  .text-size-regular {
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
  -o-hyphens: auto;
  hyphens: auto;
}
  .no-hyphen {
  word-break: normal;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  -o-hyphens: none;
  hyphens: none;
}
  .text-size-medium{
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto ;
  -ms-hyphens: auto ;
  -o-hyphens: auto ;
  hyphens: auto ;
}
/*.heading-style-h1 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h2 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h3 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}*/
.heading-style-h4 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h5 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
</style>
</head>
<body class="body">
  <div class="page-wrapper">
    <div class="global-styles w-embed">
      <style>
/* Ensure all elements inherit the color from its parent */
body * {
    color: inherit;
}
a,
.w-input,
.w-select,
.w-tab-link,
.w-nav-link,
.w-slider-arrow-left,
.w-slider-arrow-right,
.w-dropdown-btn,
.w-dropdown-toggle,
.w-dropdown-link {
  color: inherit;
  text-decoration: inherit;
  font-size: inherit;
}
/* Focus state style for keyboard navigation for the focusable elements */
*[tabindex]:focus-visible,
  input[type="file"]:focus-visible {
   outline: 0.125rem solid #4d65ff;
   outline-offset: 0.125rem;
}
/* Get rid of top margin on first element in any rich text element */
.w-richtext > :not(div):first-child, .w-richtext > div:first-child > :first-child {
  margin-top: 0 !important;
}
/* Get rid of bottom margin on last element in any rich text element */
.w-richtext>:last-child, .w-richtext ol li:last-child, .w-richtext ul li:last-child {
	margin-bottom: 0 !important;
}
/* Prevent all click and hover interaction with an element */
.pointer-events-off {
	pointer-events: none;
}
/* Enables all click and hover interaction with an element */
.pointer-events-on {
  pointer-events: auto;
}
/* Create a class of .div-square which maintains a 1:1 dimension of a div */
.div-square::after {
	content: "";
	display: block;
	padding-bottom: 100%;
}
/* Make sure containers never lose their center alignment */
.container-medium,.container-small, .container-large {
	margin-right: auto !important;
  margin-left: auto !important;
}
/* Apply "..." after 3 lines of text */
.text-style-3lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}
/* Apply "..." after 2 lines of text */
.text-style-2lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
/* Adds inline flex display */
.display-inlineflex {
  display: inline-flex;
}
/* These classes are never overwritten */
.hide {
  display: none !important;
}
@media screen and (max-width: 991px) {
    .hide, .hide-tablet {
        display: none !important;
    }
}
  @media screen and (max-width: 767px) {
    .hide-mobile-landscape{
      display: none !important;
    }
}
  @media screen and (max-width: 479px) {
    .hide-mobile{
      display: none !important;
    }
}
.margin-0 {
  margin: 0rem !important;
}
.padding-0 {
  padding: 0rem !important;
}
.spacing-clean {
padding: 0rem !important;
margin: 0rem !important;
}
.margin-top {
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-top {
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-right {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-right {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-bottom {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-bottom {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
.margin-left {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-left {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-horizontal {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-horizontal {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-vertical {
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-vertical {
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
/* Apply "..." at 100% width */
.truncate-width { 
		width: 100%; 
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 
}
/* Removes native scrollbar */
.no-scrollbar {
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none; 
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
</style>
    </div>
    <div class="main-wrapper">
      <div class="padding-global">
        <div class="container-large">
          <div class="rl-styleguide_elements">
            <div id="typography" class="rl-styleguide_typography">
              <div class="rl-styleguide_heading">Typography</div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">HTML Headings Tags<br><span class="rl-styleguide_subheading-small">HTML tags define default Heading styles.</span><br></div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efbf-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efc0-e717c76d" class="rl-styleguide_label is-html-tag">All H1 Headings</div>
                  <h1 class="heading-2">Heading 1</h1>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efc4-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efc5-e717c76d" class="rl-styleguide_label is-html-tag">All H2 Headings</div>
                  <h2 class="heading-3">Heading 2</h2>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efc9-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efca-e717c76d" class="rl-styleguide_label is-html-tag">All H3 Headings</div>
                  <h3 class="heading-4">Heading 3</h3>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efce-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efcf-e717c76d" class="rl-styleguide_label is-html-tag">All H4 Headings</div>
                  <h4 class="heading-5">Heading 4</h4>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efd3-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efd4-e717c76d" class="rl-styleguide_label is-html-tag">All H5 Headings</div>
                  <h5>Heading 5</h5>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efd8-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efd9-e717c76d" class="rl-styleguide_label is-html-tag">All H6 Headings</div>
                  <h6>Heading 6</h6>
                </div>
              </div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">Heading Classes<br><span class="rl-styleguide_subheading-small">Heading classes when typography style doesn&#x27;t match the default HTML tag.</span></div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efe3-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efe4-e717c76d" class="rl-styleguide_label">heading-style-h1</div>
                  <h2 class="heading-style-h1">Heading-style-h1</h2>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efe8-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efe9-e717c76d" class="rl-styleguide_label">heading-style-h2</div>
                  <h2 class="heading-style-h2">Heading-style-h2</h2>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efed-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8efee-e717c76d" class="rl-styleguide_label">heading-style-h3</div>
                  <h2 class="heading-style-h3">Heading-style-h3</h2>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8eff2-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8eff3-e717c76d" class="rl-styleguide_label">heading-style-h4</div>
                  <h2 class="heading-style-h4">Heading-style-h4</h2>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8eff7-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8eff8-e717c76d" class="rl-styleguide_label">heading-style-h5</div>
                  <h2 class="heading-style-h5">Heading-style-h5</h2>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8effc-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8effd-e717c76d" class="rl-styleguide_label">heading-style-h6</div>
                  <h2 class="heading-style-h6">Heading-style-h6</h2>
                </div>
              </div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">Other HTML Tags<br><span class="rl-styleguide_subheading-small">HTML tags define default text styles.</span></div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f007-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f008-e717c76d" class="rl-styleguide_label is-html-tag">All Paragraphs</div>
                  <p class="paragraph">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</p>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f00c-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f00d-e717c76d" class="rl-styleguide_label is-html-tag">All Links</div>
                  <a href="#">All Links</a>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f011-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f012-e717c76d" class="rl-styleguide_label is-html-tag">All Block Quotes</div>
                  <blockquote class="block-quote">Block Quote</blockquote>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f016-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f017-e717c76d" class="rl-styleguide_label is-html-tag">All Unordered Lists</div>
                  <ul role="list" class="w-list-unstyled">
                    <li>
                      <p>No bullet list</p>
                    </li>
                    <li>
                      <p>No bullet list</p>
                    </li>
                  </ul>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f020-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f021-e717c76d" class="rl-styleguide_label is-html-tag">All Unordered Lists</div>
                  <ul role="list">
                    <li>
                      <p>No bullet list</p>
                    </li>
                    <li>
                      <p>No bullet list</p>
                    </li>
                  </ul>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f02a-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f02b-e717c76d" class="rl-styleguide_label is-html-tag">All Ordered Lists</div>
                  <ol role="list">
                    <li>
                      <p>No bullet list</p>
                    </li>
                    <li>
                      <p>No bullet list</p>
                    </li>
                    <li>
                      <p>No bullet list</p>
                    </li>
                  </ol>
                </div>
              </div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">Text Sizes<br><span class="rl-styleguide_subheading-small">Text sizes classes when typography size doesn&#x27;t match the default HTML tag.</span></div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f03d-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f03e-e717c76d" class="rl-styleguide_label">text-size-large</div>
                  <p class="text-size-large">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</p>
                </div>
                <div id="w-node-_1d49633c-b1f3-b624-4b33-fa9d4c806ac2-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-_1d49633c-b1f3-b624-4b33-fa9d4c806ac3-e717c76d" class="rl-styleguide_label">portfolio-expose</div>
                  <p class="portfolio-expose">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</p>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f042-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f043-e717c76d" class="rl-styleguide_label">text-size-medium</div>
                  <p class="text-size-medium">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</p>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f047-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f048-e717c76d" class="rl-styleguide_label">text-size-regular</div>
                  <p class="text-size-regular">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</p>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f04c-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f04d-e717c76d" class="rl-styleguide_label">text-size-small</div>
                  <p class="text-size-small">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</p>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f051-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f052-e717c76d" class="rl-styleguide_label">text-size-tiny</div>
                  <p class="text-size-tiny">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</p>
                </div>
              </div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">Text Weights<br><span class="rl-styleguide_subheading-small">Text weight classes when typography weight doesn&#x27;t match the default HTML tag.</span></div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f05c-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f05d-e717c76d" class="rl-styleguide_label">text-weight-xbold</div>
                  <div class="text-weight-xbold">text-weight-xbold (800)</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f061-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f062-e717c76d" class="rl-styleguide_label">text-weight-bold</div>
                  <div class="text-weight-bold">text-weight-bold (700)</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f066-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f067-e717c76d" class="rl-styleguide_label">text-weight-semibold</div>
                  <div class="text-weight-semibold">text-weight-semibold (600)</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f06b-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f06c-e717c76d" class="rl-styleguide_label">text-weight-medium</div>
                  <div class="text-weight-medium">text-weight-medium (500)</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f070-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f071-e717c76d" class="rl-styleguide_label">text-weight-normal</div>
                  <div class="text-weight-normal">text-weight-normal (400)</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f075-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f076-e717c76d" class="rl-styleguide_label">text-weight-light</div>
                  <div class="text-weight-light">text-weight-light (300)</div>
                </div>
              </div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">Text Styles<br><span class="rl-styleguide_subheading-small">Text style classes when typography style doesn&#x27;t match the default HTML tag.</span><br></div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f081-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f082-e717c76d" class="rl-styleguide_label">text-style-italic</div>
                  <div class="text-style-italic">text-style-italic</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f086-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f087-e717c76d" class="rl-styleguide_label">text-style-strikethrough</div>
                  <div class="text-style-strikethrough">text-style-strikethrough</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f08b-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f08c-e717c76d" class="rl-styleguide_label">text-style-allcaps</div>
                  <div class="text-style-allcaps">text-style-allcaps</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f090-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f091-e717c76d" class="rl-styleguide_label">text-style-nowrap</div>
                  <div class="text-style-nowrap">text-style-nowrap</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f095-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f096-e717c76d" class="rl-styleguide_label">text-style-quote</div>
                  <div class="text-style-quote">text-style-quote</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f09a-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f09b-e717c76d" class="rl-styleguide_label">text-style-link</div>
                  <div class="text-style-link">text-style-link</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f09f-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0a0-e717c76d" class="rl-styleguide_label">text-style-2lines</div>
                  <div class="text-style-2lines">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0a4-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0a5-e717c76d" class="rl-styleguide_label">text-style-3lines</div>
                  <div class="text-style-3lines">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat. Aenean faucibus nibh et justo cursus id rutrum lorem imperdiet. Nunc ut sem vitae risus tristique posuere.</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0a9-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0aa-e717c76d" class="rl-styleguide_label">text-style-muted</div>
                  <div class="text-style-muted">text-style-muted</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0ae-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0af-e717c76d" class="rl-styleguide_label">text-style-tagline</div>
                  <div class="text-style-tagline">text-style-tagline</div>
                </div>
              </div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">Text Alignment<br><span class="rl-styleguide_subheading-small">Text alignment classes when typography alignment doesn&#x27;t match the default HTML tag.</span></div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0b9-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0ba-e717c76d" class="rl-styleguide_label">text-align-left</div>
                  <div class="text-align-left">text-align-left</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0be-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0bf-e717c76d" class="rl-styleguide_label">text-align-center</div>
                  <div class="text-align-center">text-align-center</div>
                </div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0c3-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0c4-e717c76d" class="rl-styleguide_label">text-align-right</div>
                  <div class="text-align-right">text-align-right</div>
                </div>
              </div>
              <div class="w-layout-grid rl-styleguide_list">
                <div class="rl-styleguide_subheading">Rich Text</div>
                <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0cb-e717c76d" class="w-layout-grid rl-styleguide_item-row">
                  <div id="w-node-ed9131e0-726d-8063-4442-d5da25f8f0cc-e717c76d" class="rl-styleguide_label">text-rich-text</div>
                  <div class="text-rich-text w-richtext">
                    <h1>Heading 1</h1>
                    <h2>Heading 2</h2>
                    <h3>Heading 3</h3>
                    <h4>Heading 4</h4>
                    <h5>Heading 5</h5>
                    <h6>Heading 6</h6>
                    <blockquote>This is a block quote</blockquote>
                    <p>The rich text element allows you to create and format headings, paragraphs, blockquotes, images, and video all in one place instead of having to add and format them individually. Just double-click and easily create content.</p>
                    <p>
                      <a href="https://relume.io">This is a link inside of a rich text</a>
                    </p>
                    <ul role="list">
                      <li>List item</li>
                      <li>List item</li>
                      <li>List item</li>
                    </ul>
                    <ol start="" role="list">
                      <li>List item</li>
                      <li>List item</li>
                      <li>List item</li>
                    </ol>
                    <figure class="w-richtext-align-normal w-richtext-figure-type-image">
                      <div><img loading="lazy" src="images/Placeholder-Image.png" alt=""></div>
                      <figcaption>Caption goes here</figcaption>
                    </figure>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=68257cde3c60ae59e717c715" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script src="https://instant.page/5.2.0" type="module" integrity="sha384-jnZyxPjiipYXnSU0ygqeac2q7CVYMbh84q0uHVRRxEtvFPiQYbXWUorga2aqZJ0z"></script>
  <script>
//Navbar Scroll Script
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar2_component');
    const navLinks = document.querySelectorAll('.navbar2_link');
    const navButton = document.querySelector('.button.is-navbar2-button');
    const logoSvgPaths = document.querySelectorAll('.navbar2_component .w-embed svg path');
    const menuLines = document.querySelectorAll('.button_dialog-menu-line');
    let scrollThreshold = 50; // Adjust this value to change when the animation triggers
    function updateNavbar() {
        if (window.scrollY > scrollThreshold) {
            // Scrolled state
            navbar.style.position = 'fixed';
            navbar.style.top = '0px';
            navbar.style.left = '0px';
            navbar.style.width = '100%';
            navbar.style.zIndex = '1000'; // Ensure it's on top
            navbar.style.backgroundColor = 'white';
            // Update nav links color
            navLinks.forEach(link => {
                link.style.color = '#000000'; // Dark grey color
            });
            // Update button background
            if (navButton) {
                navButton.style.backgroundColor = '#f5f5f5'; // Light grey background
            }
            // Update SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = '#000000';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = '#000000';
                }
            });
            // Update hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = '#000000';
            });
        } else {
            // Reset to original state
            navbar.style.position = ''; // Revert to original position
            navbar.style.top = '';
            navbar.style.left = '';
            navbar.style.width = '';
            navbar.style.zIndex = '';
            navbar.style.backgroundColor = 'transparent';
            // Reset nav links color
            navLinks.forEach(link => {
                link.style.color = ''; // Reset to original color
            });
            // Reset button background
            if (navButton) {
                navButton.style.backgroundColor = ''; // Reset to original background
            }
            // Reset SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = 'white';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = 'white';
                }
            });
            // Reset hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = 'white';
            });
        }
    }
    // Add smooth transition styles
    navbar.style.transition = 'all 0.3s ease';
    navLinks.forEach(link => {
        link.style.transition = 'color 0.3s ease';
    });
    if (navButton) {
        navButton.style.transition = 'background-color 0.3s ease';
    }
    logoSvgPaths.forEach(path => {
        path.style.transition = 'fill 0.3s ease, stroke 0.3s ease';
    });
    menuLines.forEach(line => {
        line.style.transition = 'color 0.3s ease';
    });
    // Initial check
    updateNavbar();
    // Add scroll event listener
    window.addEventListener('scroll', updateNavbar);
});
</script>
</body>
</html>