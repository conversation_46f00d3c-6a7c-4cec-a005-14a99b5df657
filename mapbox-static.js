// Function to load Mapbox resources
function loadMapboxResources() {
  return new Promise((resolve) => {
    // Load CSS
    const mapboxCSS = document.createElement('link');
    mapboxCSS.href = 'https://api.mapbox.com/mapbox-gl-js/v3.8.0/mapbox-gl.css';
    mapboxCSS.rel = 'stylesheet';
    document.head.appendChild(mapboxCSS);

    // Load Mapbox GL JS
    const mapboxScript = document.createElement('script');
    mapboxScript.src = 'https://api.mapbox.com/mapbox-gl-js/v3.8.0/mapbox-gl.js';
    document.head.appendChild(mapboxScript);

    // Load Language Plugin
    const languageScript = document.createElement('script');
    languageScript.src = 'https://api.mapbox.com/mapbox-gl-js/plugins/mapbox-gl-language/v1.0.0/mapbox-gl-language.js';
    
    mapboxScript.onload = () => {
      document.head.appendChild(languageScript);
      languageScript.onload = () => {
        resolve();
      };
    };
  });
}

// Function to initialize Mapbox
function initializeMapbox() {
  // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
  // !!! REPLACE ACCESS TOKEN WITH YOURS HERE !!!
  mapboxgl.accessToken = "pk.eyJ1IjoiZmVzdGdlYmFlY2siLCJhIjoiY2thM3VvbzFpMDFmdTNtbGI4ZHBhM3pheCJ9.rU2OiBj9hfnH2-7X-I9rjw";
  // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
  // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
  
  // Create empty locations geojson objects
  let officeLocations = {
    type: "FeatureCollection",
    features: [],
  };
  
  // Add new collection for in-progress projects
  let inProgressLocations = {
    type: "FeatureCollection",
    features: [],
  };
  
  // Initialize map and load in #map wrapper
  let map = new mapboxgl.Map({
    container: "map",
    style: "mapbox://styles/festgebaeck/cm48buvm4019v01si9t8b1jvd",
    center: [8.108183, 46.869920],
    zoom: 10.34,
    scrollZoom: false,
    cooperativeGestures: true,
    config: {
      mapbox: {
        language: 'de',
        language_code: 'de'
      }
    }
  });
  
  // Store initial map position and zoom
  let initialMapState = {
    center: [8.48196, 47.38423], // Default center
    zoom: 8 // Default zoom
  };

  map.on('load', () => {
    // Store the initial state when map loads
    initialMapState = {
      center: map.getCenter(),
      zoom: map.getZoom()
    };
    
    // Hide POIs and motorway signage
    const layers = map.getStyle().layers;
    for (const layer of layers) {
      if (layer.type === 'symbol') {
        // Check if layer is a POI or motorway sign
        if (
          layer.id.includes('poi') ||
          layer.id.includes('motorway-junction') ||
          layer.id.includes('motorway-shield') ||
          layer.id.includes('airport') ||
          layer.id.includes('park') ||
          layer.id.includes('landmark') ||
          layer.id.includes('transit')
        ) {
          map.setLayoutProperty(layer.id, 'visibility', 'none');
        }
      }
    }
    
    // Initialize map points
    addMapPoints();
    
    // Set initial view to show all markers after a short delay to ensure markers are loaded
    setTimeout(() => {
      fitMapToAllMarkers();
    }, 500);
  });

  // Add zoom and rotation controls to the map.
  map.addControl(new mapboxgl.NavigationControl());

  // Explicitly disable scroll wheel zoom (redundant with option, but requested)
  map.scrollZoom.disable();
  
  // Note: Initial zoom is now handled by fitMapToAllMarkers() in the load event
  // This ensures all markers are visible on initial page load
  
  // Process static data from locations-data.js
  function getGeoData() {
    // Process in-progress project locations
    LOCATION_DATA.in_progress_projects.forEach(function (project, i) {
      let coordinates = [project.longitude, project.latitude];
      let geoData = {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: coordinates,
        },
        properties: {
          id: project.id,
          description: generateInProgressPopupHTML(project),
          arrayID: i,
          type: 'in_progress'
        },
      };
      
      inProgressLocations.features.push(geoData);
    });

    // Process office locations
    LOCATION_DATA.offices.forEach(function (office, i) {
      let coordinates = [office.longitude, office.latitude];
      let geoData = {
        type: "Feature",
        geometry: {
          type: "Point",
          coordinates: coordinates,
        },
        properties: {
          id: office.id,
          description: generateOfficePopupHTML(office),
          arrayID: i,
          type: 'office'
        },
      };
      
      officeLocations.features.push(geoData);
    });
  }
  
  // Generate HTML for in-progress project popups
  function generateInProgressPopupHTML(project) {
    return `
      <div class="locations-map_card">
        <div class="w-embed">
          <input type="hidden" id="locationID" value="${project.id}">
          <input type="hidden" id="locationLatitude" value="${project.latitude}">
          <input type="hidden" id="locationLongitude" value="${project.longitude}">
        </div>
        <div class="locations-map_name"><div class="text-block">${project.name}</div></div>
        <div class="locations-map_population-wrapper">
          <div>${project.city}</div>
          <div class="project-type">${project.projectType}</div>
        </div>
      </div>
    `;
  }
  
  // Generate HTML for office popups
  function generateOfficePopupHTML(office) {
    return `
      <div class="locations-map_card">
        <div class="w-embed">
          <input type="hidden" id="locationID" value="${office.id}">
          <input type="hidden" id="locationLatitude" value="${office.latitude}">
          <input type="hidden" id="locationLongitude" value="${office.longitude}">
        </div>
        <div class="locations-map_name"><div>${office.name}</div></div>
        <div class="locations-map_population-wrapper"><div>${office.city}</div></div>
      </div>
    `;
  }
  
  // Generate HTML for in-progress project cards
  function generateInProgressCardHTML(project) {
    return `
      <div role="listitem" class="locations-map_item w-dyn-item" data-id="${project.id}">
        <div class="scroll-css w-embed">
          <style>
            .location-map_card-wrap {
              height: auto !important; /* Override any fixed height */
              max-height: none !important; /* Allow card to grow beyond 70vh on mobile */
              overflow-y: visible !important; /* Ensure scrollbars are prevented */
              
              &::-webkit-scrollbar {
                background-color: transparent;
                width: 0px;
              }
            }
          </style>
        </div>
        <div class="location-map_card-wrap">
          <div class="mobile-close-button">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </div>
          <div class="location-map_card-text-wrap">
            <div class="locations-map_name"><h2 class="card_heading">${project.name}</h2></div>
            <div class="project-type-tag">${project.projectType}</div>
            <img src="${project.image}" loading="lazy" alt="" class="mapbox-img">
            <div class="locations-map_card">
              <div class="w-embed">
                <input type="hidden" id="locationID" value="${project.id}">
                <input type="hidden" id="locationLatitude" value="${project.latitude}">
                <input type="hidden" id="locationLongitude" value="${project.longitude}">
              </div>
              <div class="locations-map_name"><div class="text-block">${project.name}</div></div>
              <div class="locations-map_population-wrapper"><div>${project.city}</div></div>
            </div>
            <div class="locations-map_card-info">
              <div class="locations-map_population-wrapper"><h3 class="locations-map_city">${project.city}</h3></div>
              <div class="card_divider"></div>
              <div class="card_description w-richtext">${project.description}</div>
              <div class="card_divider"></div>
              <div class="card_activity-title">Unsere Leistung</div>
              <div class="card_activity w-richtext">${project.activity}</div>
            </div>
          </div>
        </div>
        <div class="card-fade"></div>
        <div class="close-block">
          <div class="icon-embed-xxsmall w-embed">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--ph" width="100%" height="100%" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256">
              <path fill="currentColor" d="M160 214a5.8 5.8 0 0 1-4.2-1.8l-80-80a5.8 5.8 0 0 1 0-8.4l80-80a5.9 5.9 0 0 1 8.4 8.4L88.5 128l75.7 75.8A6 6 0 0 1 160 214Z"></path>
            </svg>
          </div>
        </div>
      </div>
    `;
  }

  // Generate HTML for office cards
  function generateOfficeCardHTML(office) {
    // Generate contact button HTML if URL exists
    let contactButtonHTML = '';
    if (office.contactUrl && office.contactUrl !== '#') {
      contactButtonHTML = `
        <div class="card_divider"></div>
        <a href="${office.contactUrl}" target="_blank" class="office-contact-button">Kontakt</a>
      `;
    }
  
    return `
      <div role="listitem" class="locations-map_item w-dyn-item" data-id="${office.id}">
        <div class="location-map_card-wrap">
          <div class="mobile-close-button">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </div>
          <div class="location-map_card-text-wrap">
            <div class="locations-map_name"><h2 class="card_heading">${office.name}</h2></div>
            <img src="${office.image}" loading="lazy" alt="" class="office-img">
            <div class="locations-map_card">
              <div class="w-embed">
                <input type="hidden" id="locationID" value="${office.id}">
                <input type="hidden" id="locationLatitude" value="${office.latitude}">
                <input type="hidden" id="locationLongitude" value="${office.longitude}">
              </div>
              <div class="locations-map_name"><div>${office.name}</div></div>
              <div class="locations-map_population-wrapper"><div>${office.city}</div></div>
            </div>
            <div class="locations-map_card-info">
              <div class="w-richtext">${office.description}</div>
              ${contactButtonHTML}
            </div>
          </div>
        </div>
        <div class="card-fade"></div>
        <div class="close-block">
          <div class="icon-embed-xxsmall w-embed">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--ph" width="100%" height="100%" preserveAspectRatio="xMidYMid meet" viewBox="0 0 256 256">
              <path fill="currentColor" d="M160 214a5.8 5.8 0 0 1-4.2-1.8l-80-80a5.8 5.8 0 0 1 0-8.4l80-80a5.9 5.9 0 0 1 8.4 8.4L88.5 128l75.7 75.8A6 6 0 0 1 160 214Z"></path>
            </svg>
          </div>
        </div>
      </div>
    `;
  }
  
  // Function to generate and insert HTML for all locations
  function generateLocationHTML() {
    // Generate in-progress project cards HTML
    const inProgressHTML = LOCATION_DATA.in_progress_projects.map(project => generateInProgressCardHTML(project)).join('');
    
    // Generate office cards HTML
    const officesHTML = LOCATION_DATA.offices.map(office => generateOfficeCardHTML(office)).join('');
    
    // Insert in-progress projects HTML - create a new container if needed
    let inProgressContainer = document.getElementById('in-progress-list');
    if (!inProgressContainer) {
      // Create the container if it doesn't exist
      const inProgressWrapper = document.createElement('div');
      inProgressWrapper.className = 'locations-map_wrapper';
      
      inProgressContainer = document.createElement('div');
      inProgressContainer.id = 'in-progress-list';
      inProgressContainer.className = 'locations-map_list';
      
      inProgressWrapper.appendChild(inProgressContainer);
      
      // Insert after the office wrapper if it exists
      const officeWrapper = document.getElementById('office-list')?.closest('.locations-map_wrapper');
      if (officeWrapper) {
        officeWrapper.parentNode.insertBefore(inProgressWrapper, officeWrapper.nextSibling);
      } else {
        // If no office wrapper, append to the map container
        const mapContainer = document.querySelector('.container.is-map');
        if (mapContainer) {
          mapContainer.appendChild(inProgressWrapper);
        }
      }
    }
    inProgressContainer.innerHTML = inProgressHTML;
    
    // Insert offices HTML
    const officesContainer = document.getElementById('office-list');
    if (officesContainer) {
      officesContainer.innerHTML = officesHTML;
    }
  }
  
  // Generate HTML for locations
  generateLocationHTML();
  
  // Invoke function to process data
  getGeoData();
  
  // Add click handlers for close buttons
  $('.close-block').on('click', function() {
    $(this).closest('.locations-map_item').removeClass('is--show');
    $('.locations-map_wrapper').removeClass('is--show');
    // Remove card-open class to restore button visibility
    $('.mapbox-section').removeClass('card-open');
  });

  // Add click handler for mobile close button
  $(document).on('click', '.mobile-close-button', function() {
    $(this).closest('.locations-map_item').removeClass('is--show');
    $('.locations-map_wrapper').removeClass('is--show');
    // Remove card-open class to restore button visibility
    $('.mapbox-section').removeClass('card-open');
  });

  // Remove the old "Projekte" button and replace with "Laufende Projekte"
  const projectsButton = document.getElementById('show-projects');
  if (projectsButton) {
    // Update the button text and ID
    projectsButton.textContent = 'Laufende Projekte';
    projectsButton.id = 'show-in-progress';
  }

  // Handle office and in-progress project highlight buttons
  let officesHighlighted = false;
  let inProgressHighlighted = false;

  function resetHighlights() {
    // Reset all markers to default state
    map.setPaintProperty('office-locations', 'icon-opacity', 1);
    map.setPaintProperty('in-progress-locations', 'icon-opacity', 1);
    map.setLayoutProperty('office-locations', 'icon-size', 0.5);
    map.setLayoutProperty('in-progress-locations', 'icon-size', 0.4);
    
    // Reset button states
    $('#show-offices, #show-in-progress').removeClass('is-active');
    officesHighlighted = false;
    inProgressHighlighted = false;

    // Fit map to show all markers
    fitMapToAllMarkers();
  }

  function fitMapToAllMarkers() {
    // Combine all coordinates
    const allCoordinates = [
      ...LOCATION_DATA.offices.map(office => [office.longitude, office.latitude]),
      ...LOCATION_DATA.in_progress_projects.map(project => [project.longitude, project.latitude])
    ];
    
    if (allCoordinates.length > 0) {
      const bounds = new mapboxgl.LngLatBounds();
      allCoordinates.forEach(coord => bounds.extend(coord));
      
      map.fitBounds(bounds, {
        padding: { top: 100, bottom: 100, left: 100, right: 100 },
        maxZoom: 12,
        essential: true
      });
    }
  }

  function fitMapToOffices() {
    const officeCoordinates = LOCATION_DATA.offices.map(office => [office.longitude, office.latitude]);
    
    if (officeCoordinates.length > 0) {
      const bounds = new mapboxgl.LngLatBounds();
      officeCoordinates.forEach(coord => bounds.extend(coord));
      
      map.fitBounds(bounds, {
        padding: { top: 100, bottom: 100, left: 100, right: 100 },
        maxZoom: 12,
        essential: true
      });
    }
  }

  function fitMapToInProgressProjects() {
    const projectCoordinates = LOCATION_DATA.in_progress_projects.map(project => [project.longitude, project.latitude]);
    
    if (projectCoordinates.length > 0) {
      const bounds = new mapboxgl.LngLatBounds();
      projectCoordinates.forEach(coord => bounds.extend(coord));
      
      map.fitBounds(bounds, {
        padding: { top: 100, bottom: 100, left: 100, right: 100 },
        maxZoom: 12,
        essential: true
      });
    }
  }

  $('#show-offices').on('click', function() {
    if (officesHighlighted) {
      // If already highlighted, reset everything
      resetHighlights();
    } else {
      // Highlight offices
      resetHighlights();
      officesHighlighted = true;
      $(this).addClass('is-active');
      
      map.setPaintProperty('in-progress-locations', 'icon-opacity', 0.3);
      map.setPaintProperty('office-locations', 'icon-opacity', 1);
      map.setLayoutProperty('office-locations', 'icon-size', 0.8);
      
      // Fit map to show all offices
      fitMapToOffices();
    }
  });
  
  // Add event handler for in-progress projects button
  $(document).on('click', '#show-in-progress', function() {
    if (inProgressHighlighted) {
      // If already highlighted, reset everything
      resetHighlights();
    } else {
      // Highlight in-progress projects
      resetHighlights();
      inProgressHighlighted = true;
      $(this).addClass('is-active');
      
      map.setPaintProperty('office-locations', 'icon-opacity', 0.3);
      map.setPaintProperty('in-progress-locations', 'icon-opacity', 1);
      map.setLayoutProperty('in-progress-locations', 'icon-size', 0.8);
      
      // Fit map to show all in-progress projects
      fitMapToInProgressProjects();
    }
  });

  // Create dropdown for mobile/tablet if it doesn't exist
  const buttonsContainer = document.querySelector('.map-filter-buttons');
  if (buttonsContainer && !document.getElementById('map-category-dropdown')) {
    const dropdown = document.createElement('select');
    dropdown.id = 'map-category-dropdown';
    dropdown.className = 'map-category-dropdown';
    
    // Add options to dropdown (only offices and in-progress)
    const options = [
      { value: 'in-progress', text: 'Laufende Projekte', selected: true },
      { value: 'offices', text: 'Unsere Büros' }
    ];
    
    options.forEach(option => {
      const optionElement = document.createElement('option');
      optionElement.value = option.value;
      optionElement.textContent = option.text;
      if (option.selected) optionElement.selected = true;
      dropdown.appendChild(optionElement);
    });
    
    buttonsContainer.appendChild(dropdown);
  }
  
  // Add change handler for dropdown
  $('#map-category-dropdown').on('change', function() {
    const selectedValue = $(this).val();
    
    // Reset all highlights first
    resetHighlights();
    
    // Apply the selected highlight
    if (selectedValue === 'offices') {
      officesHighlighted = true;
      $('#show-offices').addClass('is-active');
      
      map.setPaintProperty('in-progress-locations', 'icon-opacity', 0.3);
      map.setPaintProperty('office-locations', 'icon-opacity', 1);
      map.setLayoutProperty('office-locations', 'icon-size', 0.8);
      
      // Fit map to show all offices
      fitMapToOffices();
    } else {
      // Default to in-progress projects
      inProgressHighlighted = true;
      $('#show-in-progress').addClass('is-active');
      
      map.setPaintProperty('office-locations', 'icon-opacity', 0.3);
      map.setPaintProperty('in-progress-locations', 'icon-opacity', 1);
      map.setLayoutProperty('in-progress-locations', 'icon-size', 0.8);
      
      // Fit map to show all in-progress projects
      fitMapToInProgressProjects();
    }
  });
  
  // define mapping function to be invoked later
  function addMapPoints() {
    console.log('Adding map points...');
    console.log('Number of in-progress locations:', inProgressLocations.features.length);
    console.log('Number of office locations:', officeLocations.features.length);
    
    // Load marker images
    Promise.all([
      new Promise((resolve, reject) => {
        map.loadImage('https://cdn.prod.website-files.com/68257cde3c60ae59e717c715/6834166094a300ddf57eaa7d_office-icon.png', (error, image) => {
          if (error) reject(error);
          map.addImage('office-marker', image);
          resolve();
        });
      }),
      // Load in-progress marker image
      new Promise((resolve, reject) => {
        map.loadImage('https://cdn.prod.website-files.com/68257cde3c60ae59e717c715/68340244e279d2a9f7430450_6ff11924d2ca435beed3c72aeb96d75d_crane-icon.png', (error, image) => {
          if (error) reject(error);
          map.addImage('in-progress-marker', image);
          resolve();
        });
      })
    ]).then(() => {
      // Add in-progress projects layer
      map.addLayer({
        id: "in-progress-locations",
        type: "symbol",
        source: {
          type: "geojson",
          data: inProgressLocations,
        },
        layout: {
          'icon-image': 'in-progress-marker',
          'icon-size': 0.5,
          'icon-allow-overlap': true
        }
      });

      // Add office locations layer
      map.addLayer({
        id: "office-locations",
        type: "symbol",
        source: {
          type: "geojson",
          data: officeLocations,
        },
        layout: {
          'icon-image': 'office-marker',
          'icon-size': 0.6,
          'icon-allow-overlap': true
        }
      });

      // Add click handlers for all location types
      ["in-progress-locations", "office-locations"].forEach(layerId => {
        map.on("click", layerId, (e) => {
          const feature = e.features[0];
          const coordinates = feature.geometry.coordinates.slice();
          const type = feature.properties.type;
          
          console.log('Marker clicked:', {
            type: type,
            coordinates: coordinates
          });
          
          // POPUP FUNCTIONALITY DISABLED
          // No longer creating popups on marker click
          
          // Remove show class from any currently shown items
          $(".locations-map_item").removeClass("is--show");
          
          // Remove is--show from both wrappers first
          $(".locations-map_wrapper").removeClass("is--show");
          
          // Show the correct item based on type and ID
          if (type === 'office') {
            // Find the office item by data-id attribute
            $(`.locations-map_item[data-id="${feature.properties.id}"]`).addClass("is--show");
            $('#office-list').closest('.locations-map_wrapper').addClass("is--show");
          } else if (type === 'in_progress') {
            // Find the in-progress project item by data-id attribute
            $(`.locations-map_item[data-id="${feature.properties.id}"]`).addClass("is--show");
            $('#in-progress-list').closest('.locations-map_wrapper').addClass("is--show");
          }
          
          // Add card-open class for z-index override
          $('.mapbox-section').addClass('card-open');
          
          // Move to marker location while maintaining current zoom
          map.flyTo({
            center: coordinates,
            zoom: map.getZoom(), // Keep current zoom level
            essential: true // This animation is considered essential with respect to prefers-reduced-motion
          });
        });

        // Change cursor on hover
        map.on("mouseenter", layerId, () => {
          map.getCanvas().style.cursor = "pointer";
        });
        
        map.on("mouseleave", layerId, () => {
          map.getCanvas().style.cursor = "";
        });
      });
    }).catch(error => console.error('Error loading markers:', error));
  }
  
  //close side nav with button
  $(".close-block").click(function(){
    $(".locations-map_wrapper").removeClass("is--show");
    $(".locations-map_item").removeClass("is--show");
    // Remove card-open class to restore button visibility
    $('.mapbox-section').removeClass('card-open');
  });
  
  // Create a simple hover popup
  let hoverPopup = null;
  const popup = new mapboxgl.Popup({
    closeButton: false,
    closeOnClick: false,
    className: 'hover-popup',
    offset: 15
  });

  // Add hover handlers for in-progress and office locations
  ['in-progress-locations', 'office-locations'].forEach(layerId => {
    map.on('mouseenter', layerId, (e) => {
      // Change the cursor style as a UI indicator.
      map.getCanvas().style.cursor = 'pointer';
      
      // Get the feature data
      const feature = e.features[0];
      const coordinates = feature.geometry.coordinates.slice();
      
      // Create a simple tooltip with just the name
      let tooltipContent = '';
      if (feature.properties.type === 'in_progress') {
        // Find the in-progress project in our data
        const project = LOCATION_DATA.in_progress_projects.find(p => p.id === feature.properties.id);
        if (project) {
          tooltipContent = `<div class="tooltip-content">${project.name}</div>`;
        }
      } else {
        // Find the office in our data
        const office = LOCATION_DATA.offices.find(o => o.id === feature.properties.id);
        if (office) {
          tooltipContent = `<div class="tooltip-content">${office.name}</div>`;
        }
      }
      
      // Only show tooltip if we have content
      if (tooltipContent) {
        // Ensure tooltip appears over the correct marker
        while (Math.abs(e.lngLat.lng - coordinates[0]) > 180) {
          coordinates[0] += e.lngLat.lng > coordinates[0] ? 360 : -360;
        }
        
        // Show the tooltip
        hoverPopup = popup
          .setLngLat(coordinates)
          .setHTML(tooltipContent)
          .addTo(map);
      }
    });
    
    map.on('mouseleave', layerId, () => {
      map.getCanvas().style.cursor = '';
      if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
      }
    });
    
    // Remove hover popup when clicking markers
        map.on('click', layerId, (e) => {
      // Scroll the map wrapper into view
      const mapWrapper = document.querySelector('.mapbox-wrapper');
      if (mapWrapper) {
        mapWrapper.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
      if (hoverPopup) {
        hoverPopup.remove();
        hoverPopup = null;
      }
    });
  });

  // Add some custom CSS for the hover tooltip, touch interactions, contact button, and in-progress projects
  const style = document.createElement('style');
  style.textContent = `
    .hover-popup .mapboxgl-popup-content {
      padding: 8px 10px;
      background-color: white;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      font-weight: bold;
    }
    .hover-popup .mapboxgl-popup-tip {
      border-top-color: white;
    }
    .tooltip-content {
      white-space: nowrap;
      font-size: 14px;
    }
    .mapboxgl-canvas {
      touch-action: manipulation; /* Allow pan/zoom, help distinguish page scroll */
    }
    /* Minimal styling for the contact button */
    .office-contact-button {
      display: inline-block;
      padding: 8px 15px;
      margin-top: 15px;
      background-color: #f0f0f0;
      color: #333;
      text-decoration: none;
      border-radius: 4px;
      border: 1px solid #ccc;
      text-align: center;
      transition: background-color 0.2s ease;
    }
    .office-contact-button:hover {
      background-color: #e0e0e0;
    }
    /* Styling for in-progress project status */
    .project-status-tag {
      display: inline-block;
      padding: 4px 8px;
      background-color: #F5F5F5;
      color: #333;
      font-size: 12px;
      border-radius: 4px;
      margin-bottom: 10px;
      font-weight: bold;
    }
    .status-badge {
      display: inline-block;
      padding: 2px 6px;
      background-color: #F5F5F5;
      color: #333;
      font-size: 11px;
      border-radius: 3px;
      margin-left: 5px;
    }
    /* Dropdown styling for mobile/tablet - HIDDEN FOR NOW */
    .map-category-dropdown {
      display: none !important; /* HIDDEN FOR DEBUGGING */
      padding: 10px 15px;
      background-color: white;
      color: #333;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-weight: bold;
      font-size: 14px;
      cursor: pointer;
      min-width: 150px;
      position: relative !important; /* Ensure it's not positioned fixed */
      z-index: inherit !important; /* Inherit z-index from parent */
      max-width: 250px !important; /* Prevent it from getting too wide */
    }
    
    /* Responsive behavior: show buttons on all screen sizes (dropdown disabled) */
    @media (max-width: 767px) {
      .map-filter-button {
        display: inline-block !important; /* Show buttons on mobile/tablet */
      }
      .map-category-dropdown {
        display: none !important; /* Hide dropdown on mobile/tablet */
      }
      .map-filter-buttons {
        position: absolute !important;
        top: auto !important;
        bottom: 20px !important;
        left: 50% !important;
        right: auto !important;
        transform: translateX(-50%) !important;
        z-index: 5 !important;
        display: flex !important;
        justify-content: center !important;
        width: auto !important;
        max-width: calc(100vw - 40px) !important;
        gap: 10px !important;
      }
    }
    
    /* Keep buttons visible on desktop, hide dropdown */
    @media (min-width: 768px) {
      .map-category-dropdown {
        display: none !important; /* Hide dropdown on desktop */
      }
      .map-filter-button {
        display: inline-block !important; /* Show buttons on desktop */
      }
      .map-filter-buttons {
        gap: 10px !important; /* Ensure gap between buttons */
      }
    }
    
    /* Fixed styling for actual img tags (not background images) */
    .office-img, .mapbox-img {
      width: 100%;
      height: auto;
      aspect-ratio: 16 / 9; /* Modern CSS for 16:9 aspect ratio */
      object-fit: cover;
      object-position: center;
      border-radius: 4px;
      margin-bottom: 15px;
      display: block;
    }
    
    /* Fallback for browsers that don't support aspect-ratio */
    @supports not (aspect-ratio: 16 / 9) {
      .office-img, .mapbox-img {
        height: 200px; /* Fixed height fallback */
      }
    }
    
    /* Fix z-index hierarchy and positioning */
    .mapbox-section {
      position: relative !important;
      z-index: 2 !important; /* Default: below buttons */
    }
    
    /* When card is open, ensure proper stacking */
    .mapbox-section.card-open {
      z-index: 1000 !important;
    }
    
    .locations-map_item.is--show {
      display: block !important; /* Ensure the card is visible */
      z-index: 1001 !important;
      position: relative !important;
    }
    
    .close-block {
      z-index: 1002 !important;
    }
    
    .map-filter-buttons {
      z-index: 1003 !important; /* Above everything, including open cards */
      position: absolute !important;
    }
    
    /* Mobile/tablet behavior: hide buttons when card is open */
    @media (max-width: 767px) {
      .mapbox-section.card-open .map-filter-buttons {
        z-index: 1 !important; /* Hide behind cards on mobile/tablet */
      }
    }
    
    /* Ensure mapbox wrapper properly contains positioned elements */
    .mapbox-wrapper {
      position: relative !important;
      overflow: hidden !important;
    }
    
    .mapbox-section {
      position: relative !important;
    }
    
    /* Create mobile-only close button */
    .mobile-close-button {
      display: none; /* Hidden by default */
      position: absolute;
      top: 15px;
      right: 15px;
      width: 32px;
      height: 32px;
      background-color: rgba(255, 255, 255, 0.9);
      border: 1px solid #e0e0e0;
      border-radius: 50%;
      cursor: pointer;
      z-index: 1002;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      transition: background-color 0.2s ease;
    }
    
    .mobile-close-button:hover {
      background-color: #f5f5f5;
    }
    
    .mobile-close-button svg {
      width: 18px;
      height: 18px;
      color: #333;
    }
    
    /* Dynamic height for card content - adjusts to content size */
    .location-map_card-wrap {
      height: auto !important; /* Let height adjust to content */
      position: relative;
    }
    
    /* Enhanced mobile and tablet responsiveness with Safari fixes */
    @media screen and (max-width: 767px) {
      /* Show mobile close button on both mobile AND tablet, hide original */
      .mobile-close-button {
        display: flex !important;
      }
      
      .close-block {
        display: none !important; /* Hide original close button on mobile AND tablet */
      }
      
             /* Basic card container settings */
       .location-map_card-wrap {
         max-height: 70vh !important; /* Reasonable max height for mobile/tablet */
         min-height: 300px !important; /* Ensure minimum usable height */
       }
      
             /* Card positioning for mobile and tablet */
       .locations-map_item.is--show {
         display: block !important; /* Ensure the card is visible */
         z-index: 1001 !important;
       }
      
      /* Add padding to top of card content to prevent mobile close button overlap */
      .location-map_card-text-wrap {
        padding-top: 50px !important; /* Space for mobile close button */
        padding-bottom: 20px !important;
        overflow-y: auto !important;
        max-height: calc(85vh - 100px) !important; /* Account for padding and close button */
      }
    }
    
    /* Mobile-specific adjustments (smaller screens) */
    @media screen and (max-width: 479px) {
      /* Reduce image height on mobile for better content distribution */
      .office-img, .mapbox-img {
        aspect-ratio: 16 / 10 !important; /* Slightly taller on mobile */
        max-height: 150px !important;
      }
      
      /* For browsers without aspect-ratio support */
      @supports not (aspect-ratio: 16 / 10) {
        .office-img, .mapbox-img {
          height: 150px !important;
        }
      }
      
      /* Ensure text content is readable */
      .card_description {
        font-size: 14px !important;
        line-height: 1.4 !important;
      }
      
      
    }
    
    /* Tablet-specific adjustments (480px - 767px) */
    @media screen and (min-width: 480px) and (max-width: 767px) {
      .office-img, .mapbox-img {
        max-height: 180px !important;
      }
      
      
    }
    
    /* Desktop - keep original close button behavior */
    @media screen and (min-width: 768px) {
      .mobile-close-button {
        display: none !important;
      }
      
      .close-block {
        display: block !important; /* Ensure original close button is visible */
      }
      
      /* Larger max-height on desktop where there's more screen space */
      .location-map_card-wrap {
        max-height: 90vh !important;
      }
    }
    
         /* Safari-specific fixes for mobile close button */
     @supports (-webkit-appearance: none) {
       @media screen and (max-width: 767px) {
         /* Ensure mobile close button works properly in Safari */
         .mobile-close-button {
           /* Prevent touch events from going through to map */
           pointer-events: auto !important;
           /* Ensure button stays in position during Safari address bar changes */
           -webkit-transform: translateZ(0) !important;
           transform: translateZ(0) !important;
         }
       }
     }
  `;
  document.head.appendChild(style);
  
  const language = new MapboxLanguage();
  map.addControl(language);
}

// Create and setup Intersection Observer
const mapObserver = new IntersectionObserver((entries, observer) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      // Load Mapbox resources and initialize
      loadMapboxResources().then(() => {
        initializeMapbox();
      });
      // Disconnect the observer after loading
      observer.disconnect();
    }
  });
}, {
  // Adjust rootMargin to start loading well before the element comes into view
  rootMargin: '800px' /* Increased from 400px to load earlier */
});

// Start observing the map container
document.addEventListener('DOMContentLoaded', () => {
  const mapContainer = document.querySelector('.mapbox-wrapper');
  if (mapContainer) {
    mapObserver.observe(mapContainer);
  }
}); 