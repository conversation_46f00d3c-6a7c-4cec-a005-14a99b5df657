<!DOCTYPE html><!--  Last Published: Mon Aug 11 2025 09:01:09 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="68511fadbd180ab291c1c1d5" data-wf-site="68257cde3c60ae59e717c715" lang="de">
<head>
  <meta charset="utf-8">
  <title>Not Found</title>
  <meta content="Not Found" property="og:title">
  <meta content="Not Found" property="twitter:title">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/woelfli-staging-de009659c94824f4bd1e533.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js" as="script">
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/animation/beautified_scroll_animations_noheader.js?=v7" defer=""></script>
  <script defer="" src="https://cloud.umami.is/script.js" data-website-id="634ce2ab-8a79-4bd9-b693-703b47938caf"></script>
  <style>
body {
  overscroll-behavior-y: contain;
}
  .image-3 {
  content-visibility: auto;
  contain: layout style paint;
}
.text-size-regular {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
  .text-size-regular {
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
  -o-hyphens: auto;
  hyphens: auto;
}
  .no-hyphen {
  word-break: normal;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  -o-hyphens: none;
  hyphens: none;
}
  .text-size-medium{
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto ;
  -ms-hyphens: auto ;
  -o-hyphens: auto ;
  hyphens: auto ;
}
/*.heading-style-h1 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h2 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h3 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}*/
.heading-style-h4 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h5 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
</style>
</head>
<body>
  <div class="utility-page-wrap">
    <div class="utility-page-content"><img src="https://d3e54v103j8qbb.cloudfront.net/static/page-not-found.211a85e40c.svg" alt="">
      <h2>Seite nicht gefunden</h2>
      <div class="text-size-regular">Die Seite die Sie suchen konnte nicht gefunden werden.</div>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=68257cde3c60ae59e717c715" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script src="https://instant.page/5.2.0" type="module" integrity="sha384-jnZyxPjiipYXnSU0ygqeac2q7CVYMbh84q0uHVRRxEtvFPiQYbXWUorga2aqZJ0z"></script>
  <script>
//Navbar Scroll Script
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar2_component');
    const navLinks = document.querySelectorAll('.navbar2_link');
    const navButton = document.querySelector('.button.is-navbar2-button');
    const logoSvgPaths = document.querySelectorAll('.navbar2_component .w-embed svg path');
    const menuLines = document.querySelectorAll('.button_dialog-menu-line');
    let scrollThreshold = 50; // Adjust this value to change when the animation triggers
    function updateNavbar() {
        if (window.scrollY > scrollThreshold) {
            // Scrolled state
            navbar.style.position = 'fixed';
            navbar.style.top = '0px';
            navbar.style.left = '0px';
            navbar.style.width = '100%';
            navbar.style.zIndex = '1000'; // Ensure it's on top
            navbar.style.backgroundColor = 'white';
            // Update nav links color
            navLinks.forEach(link => {
                link.style.color = '#000000'; // Dark grey color
            });
            // Update button background
            if (navButton) {
                navButton.style.backgroundColor = '#f5f5f5'; // Light grey background
            }
            // Update SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = '#000000';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = '#000000';
                }
            });
            // Update hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = '#000000';
            });
        } else {
            // Reset to original state
            navbar.style.position = ''; // Revert to original position
            navbar.style.top = '';
            navbar.style.left = '';
            navbar.style.width = '';
            navbar.style.zIndex = '';
            navbar.style.backgroundColor = 'transparent';
            // Reset nav links color
            navLinks.forEach(link => {
                link.style.color = ''; // Reset to original color
            });
            // Reset button background
            if (navButton) {
                navButton.style.backgroundColor = ''; // Reset to original background
            }
            // Reset SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = 'white';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = 'white';
                }
            });
            // Reset hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = 'white';
            });
        }
    }
    // Add smooth transition styles
    navbar.style.transition = 'all 0.3s ease';
    navLinks.forEach(link => {
        link.style.transition = 'color 0.3s ease';
    });
    if (navButton) {
        navButton.style.transition = 'background-color 0.3s ease';
    }
    logoSvgPaths.forEach(path => {
        path.style.transition = 'fill 0.3s ease, stroke 0.3s ease';
    });
    menuLines.forEach(line => {
        line.style.transition = 'color 0.3s ease';
    });
    // Initial check
    updateNavbar();
    // Add scroll event listener
    window.addEventListener('scroll', updateNavbar);
});
</script>
</body>
</html>