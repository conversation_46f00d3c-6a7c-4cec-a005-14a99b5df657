<!DOCTYPE html><!--  Last Published: Mon Aug 11 2025 09:01:09 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="68595fd408a505cd9c6bfe73" data-wf-site="68257cde3c60ae59e717c715" lang="de">
<head>
  <meta charset="utf-8">
  <title>Alterszentrum Rubiswil – Umbau / Erweiterung</title>
  <meta content="Umbau Alterszentrum Rubiswil: 6 neue Zimmer bei laufendem Betrieb." name="description">
  <meta content="Alterszentrum Rubiswil – Umbau / Erweiterung" property="og:title">
  <meta content="Umbau Alterszentrum Rubiswil: 6 neue Zimmer bei laufendem Betrieb." property="og:description">
  <meta content="Alterszentrum Rubiswil – Umbau / Erweiterung" property="twitter:title">
  <meta content="Umbau Alterszentrum Rubiswil: 6 neue Zimmer bei laufendem Betrieb." property="twitter:description">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="../css/normalize.css" rel="stylesheet" type="text/css">
  <link href="../css/webflow.css" rel="stylesheet" type="text/css">
  <link href="../css/woelfli-staging-de009659c94824f4bd1e533.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="../images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="../images/webclip.png" rel="apple-touch-icon">
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js" as="script">
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/animation/beautified_scroll_animations_noheader.js?=v7" defer=""></script>
  <script defer="" src="https://cloud.umami.is/script.js" data-website-id="634ce2ab-8a79-4bd9-b693-703b47938caf"></script>
  <style>
body {
  overscroll-behavior-y: contain;
}
  .image-3 {
  content-visibility: auto;
  contain: layout style paint;
}
.text-size-regular {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
  .text-size-regular {
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
  -o-hyphens: auto;
  hyphens: auto;
}
  .no-hyphen {
  word-break: normal;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  -o-hyphens: none;
  hyphens: none;
}
  .text-size-medium{
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto ;
  -ms-hyphens: auto ;
  -o-hyphens: auto ;
  hyphens: auto ;
}
/*.heading-style-h1 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h2 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h3 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}*/
.heading-style-h4 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h5 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
</style><!--  CSS  -->
  <link rel="stylesheet" href="https://unpkg.com/flickity@2.3.0/dist/flickity.css">
  <!--  JS  -->
  <script defer="" src="https://unpkg.com/flickity@2.3.0/dist/flickity.pkgd.min.js"></script>
</head>
<body>
  <div class="menu-main-css u-embed w-embed">
    <style>
:root {
	--font-family-serif: "Overused Grotesk", 
  iowan old style,
  apple garamond,
  baskerville,
  times new roman,
  droid serif,
  times,
  source serif pro,
  serif,
  apple color emoji,
  segoe ui emoji,
  segoe ui symbol;
  --font-family-normal: "Overused Grotesk",
  -apple-system,
  blinkmacsystemfont,
  avenir next,
  avenir,
  segoe ui,
  helvetica neue,
  helvetica,
  cantarell,
  ubuntu,
  roboto,
  noto,
  arial,
  sans-serif;
  --font-weight-regular: 400;
	--font-size-h1: clamp(3rem, 0.578rem + 6.92vw, 7.5rem);
	--font-size-h2: clamp(2.25rem, 0.7695rem + 4.23vw, 5rem);
	--font-size-h3: clamp(1.25rem, 0.8475rem + 1.15vw, 2rem);
	--size-48: clamp(2rem, 1.461rem + 1.54vw, 3rem);
	--size-64: clamp(2.5rem, 1.6915rem + 2.31vw, 4rem);
	--size-80: clamp(3rem, 1.922rem + 3.08vw, 5rem);
	--size-96: clamp(4rem, 2.922rem + 3.08vw, 6rem);
	--size-120: clamp(5rem, 3.6525rem + 3.85vw, 7.5rem);
	--size-144: clamp(6rem, 4.383rem + 4.62vw, 9rem);
  --z-below: -1;
  --z-above: 1;
  --z-dialog: 300;
  --grid-margin: clamp(1rem, 0.1915rem + 2.31vw, 2.5rem);
  --grid-gutter: 1rem;
  --grid-columns: 12;
  --grid-column-width:  calc(((var(--vw, 1vw) * 100) - (2 * var(--grid-margin))) / var(--grid-columns) - (var(--grid-gutter) * (var(--grid-columns) - 1) / var(--grid-columns)));
}
html {
	-webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
  text-size-adjust: none;
  touch-action: manipulation;
  scrollbar-width: thin;
  scrollbar-color: color-mix(in hsl, var(--color), transparent 50%) var(--color-background);
}
::selection {
  background-color: var(--selection-background);
  color: var(--selection-foreground);
  text-shadow: none;
}
*:focus-visible:not(input, textarea, select) {
  outline: var(--color) auto 6px;
  outline-offset: 4px;
}
/* theme */
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
[data-theme="light"] .link {
	color: var(--color);
}
/* webflow */
.wf-design-mode .wf-empty {
  padding: 0;
}
.wf-editor-mode .wf-empty {
  padding: 0;
}
/* utilities */
.u-screen-reader-text {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  margin: 0;
  padding: 0;
  width: 1px;
  height: 1px;
  border: 0;
}
.u-embed {
	position: fixed;
  inset: 0 auto auto 0;
}
.u-embed::before {
	content: none !important;
}
/* icons */
.icon.is-small {
	--icon-width: .75rem;
  --icon-height: .75rem;
}
/* header */
.header_breadcrumb-item:nth-child(1),
.header_breadcrumb-item:nth-child(2) {
	opacity: 60%;
}
.header_breadcrumb-item:nth-child(1)::after {
	content: "//";
  margin-left: var(--size-4);
}
.header_breadcrumb-item:nth-child(2)::after {
	content: "/";
  margin-left: var(--size-4);
}
/* buttons */
.button_cloneable::after,
.button_info::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.25rem;
}
.button_dialog {
	-webkit-tap-highlight-color: transparent;
  transition: transform .15s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog[data-active] {
	transform: translate3d(0, .125rem, 0);
}
.button_dialog[data-active] .button_dialog-bg {
	background-color: color-mix(in hsl, var(--color-accent), transparent 25%);
}
.button_dialog[data-active] .button_dialog-bg::after {
	background-color: color-mix(in hsl, var(--color), transparent 25%);
}
.button_cloneable-bg {
	transition: box-shadow .3s cubic-bezier(.62, .08, 0, 1);
}
.button_info .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-bg {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1), background-color .15s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-menu-line-wrap {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-bg::before {
  content: "";
  display: block;
  position: absolute;
  background-color: var(--color-accent-orange);
  inset: 0;
  width: 100%;
  height: 100%;
  clip-path: inset(100% 0 0 0 round 0 0 .5rem .5rem);
  transition: clip-path .3s cubic-bezier(.36, .08, 0, 1);
  transition-delay: 0.15s;
}
@media screen and (prefers-reduced-motion) {
	.button_dialog-bg::before {
  	content: none;
  }
}
.button_dialog-bg::after {
  content: "";
  display: block;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  clip-path: inset(100% 0 0 0 round 0 0 .5rem .5rem);
  transition: clip-path .45s cubic-bezier(.62, .08, 0, 1), background-color .15s cubic-bezier(.62, .08, 0, 1);
}
@media screen and (prefers-reduced-motion) {
	.button_dialog-bg::after {
  	clip-path: inset(0 0 0 0 round .5rem);
    transition: opacity .3s cubic-bezier(.62, .08, 0, 1);
    opacity: 0;
  }
}
@media (hover: hover) and (pointer: fine) {
  .button_cloneable:hover .button_cloneable-bg,
  .button_cloneable:focus-visible .button_cloneable-bg {
    box-shadow: 0 0 0 .1875rem var(--color);
  }
  .button_info:hover .icon,
  .button_info:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
  .button_dialog:hover .button_dialog-bg,
  .button_dialog:focus-visible .button_dialog-bg {
    transform: scale(1.05) translateZ(0);
  }
  .button_dialog:hover .button_dialog-bg::before,
  .button_dialog:focus-visible .button_dialog-bg::before,
  .button_dialog:hover .button_dialog-bg::after,
  .button_dialog:focus-visible .button_dialog-bg::after {
    clip-path: inset(0 0 0 0 round .5rem);
  }
  .button_dialog:hover .button_dialog-bg::before,
  .button_dialog:focus-visible .button_dialog-bg::before {
    transition-delay: 0s;
  }
  .button_dialog:hover .button_dialog-menu-line-wrap,
  .button_dialog:focus-visible .button_dialog-menu-line-wrap {
  	transform: scaleX(.9) translateZ(0);
  }
  .button_dialog:focus-visible {
    outline-offset: .5rem;
  }
}
@media screen and (prefers-reduced-motion) and (hover: hover) and (pointer: fine) {
	.button_dialog:hover .button_dialog-bg,
  .button_dialog:focus-visible .button_dialog-bg {
    transform: scale(1) translateZ(0);
  }
	.button_dialog:hover .button_dialog-bg::after,
  .button_dialog:focus-visible .button_dialog-bg::after {
    opacity: 1;
  }
}
/* External Links */
.link[target="_blank"]::after {
	content: "";
  display: inline-block;
  background-color: currentColor;
  mask: url("https://cdn.prod.website-files.com/66d5c7d781a3c8330fefd851/66eade6255eed3286f87a03c_icon-external.svg") no-repeat 50% 50%;
	height: .625rem;
  width: .625rem;
  margin-left: .25rem;
}
/* header info dialog */
.header_dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --content-speed: .45s;
  --content-ease: cubic-bezier(.215, .61, .355, 1);
  --content-stagger: .05s;
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .header_dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.header_dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.header_dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.header_dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.header_dialog:not([open]) .header_dialog-inner {
	transform: translate3d(100%, 0, 0);
}
.header_dialog-inner {
	transform: translate3d(0, 0, 0);
	contain: layout style paint;
	transition: transform var(--modal-speed) var(--modal-ease);  
}
.header_dialog-inner:focus-visible {
	outline-offset: -.25rem;
}
/* move wrapper */
@media screen and (prefers-reduced-motion: no-preference) {
  .hero,
  .block-text,
  .footer {
    transition: transform .75s cubic-bezier(.62, .08, 0, 1);  
  }
  .wrapper:has(.header_dialog[open]) .hero,
  .wrapper:has(.header_dialog[open]) .block-text,
  .wrapper:has(.header_dialog[open]) .footer {
    transform: translate3d(-10rem, 0 , 0);
  }
}
@media screen and (prefers-reduced-motion) {
  .header_dialog:not([open]) .header_dialog-inner {
  	transform: translate3d(0, 0, 0);
    opacity: 0;
  }
  .header_dialog-inner {
    transition: opacity var(--modal-speed) var(--modal-ease);  
  }
}
/* initial state of close button */
.header_dialog:not([open]) .header_dialog-button-close {
	transform: translate3d(.75rem, 0, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.header_dialog-button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.header_dialog-button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.header_dialog-button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .header_dialog-button-close:hover .icon,
  .header_dialog-button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.header_dialog:not([open]) .header_dialog-backdrop {
	opacity: 0;
}
.header_dialog-backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
</style>
  </div>
  <div class="css-dialog-styles u-embed w-embed">
    <style>
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
.css-dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --content-speed: .45s;
  --content-ease: cubic-bezier(.215, .61, .355, 1);
  --content-stagger: .05s;
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .css-dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.css-dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.css-dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.css-dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.css-dialog:not([open]) .css-dialog_inner {
	transform: translate3d(0, 100%, 0);
}
.css-dialog_inner {
	contain: content;
	transition: transform var(--modal-speed) var(--modal-ease);  
}
.css-dialog_inner:focus-visible {
	outline-offset: -.25rem;
}
@media screen and (prefers-reduced-motion) {
  .css-dialog:not([open]) .css-dialog_inner {
  	transform: translate3d(0, 0, 0);
    opacity: 0;
  }
  .css-dialog_inner {
    transition: opacity var(--modal-speed) var(--modal-ease);  
  }
}
/* preload content for better performance */
.css-dialog_title,
.css-dialog_paragraph,
.css-dialog_visual-number,
.css-dialog_visual-author,
.css-dialog_visual-img {
  content-visibility: auto;
  contain: layout style paint;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
/* initial state of close button */
.css-dialog:not([open]) .css-dialog_button-close {
	transform: translate3d(0, -.75rem, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.css-dialog_button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.css-dialog_button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.css-dialog_button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .css-dialog_button-close:hover .icon,
  .css-dialog_button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.css-dialog:not([open]) .css-dialog_backdrop {
	opacity: 0;
}
.css-dialog_backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
/* initial state of content when modal is not opened (only when reduce motion is not activated) */
@media screen and (prefers-reduced-motion: no-preference) {
  .css-dialog:not([open]) .css-dialog_title,
  .css-dialog:not([open]) .css-dialog_paragraph.u-h3,
  .css-dialog:not([open]) .css-dialog_visual-img {
    transform: translate3d(0, 2.5rem, 0);
    opacity: 0;
  }
  .css-dialog:not([open]) .css-dialog_paragraph,
  .css-dialog:not([open]) .css-dialog_visual-number,
  .css-dialog:not([open]) .css-dialog_visual-author {
    transform: translate3d(0, 1.25rem, 0);
    opacity: 0;
  }
  .css-dialog_title,
  .css-dialog_paragraph,
  .css-dialog_visual-number,
  .css-dialog_visual-author,
  .css-dialog_visual-img {
    transition: transform var(--content-speed) var(--content-ease), opacity var(--content-speed) var(--content-ease);
    transition-delay: 0s, 0s;
    opacity: 1;
  }
  .css-dialog[open] .css-dialog_title,
  .css-dialog[open] .css-dialog_paragraph,
  .css-dialog[open] .css-dialog_visual-number,
  .css-dialog[open] .css-dialog_visual-author,
  .css-dialog[open] .css-dialog_visual-img {
    transition-delay: calc((var(--dialog-animation-speed) - .2s) + var(--content-stagger) * (var(--index) - 1)), calc((var(--dialog-animation-speed) - .2s) + var(--content-stagger) * (var(--index) - 1));
  }
}
</style>
  </div>
  <div class="css-dialog-javascript u-embed w-embed w-script">
    <script defer="">
// handles scrollbar jump when opening modal and disabling scrolling behind the modal
// normaly scrollbar-gutter: stable; would handle this, but safari doesn't support it at this time
class CssScrollbarHandler {
  constructor(options = {}) {
    this.html = document.documentElement;
    this.scrollbarThreshold = options.scrollbarThreshold || 5;
    this.scrollbarWidth = 0;
    this.calculateScrollbarWidth();
    this.handleScrollbar();
  }
  calculateScrollbarWidth() {
    // calculate scrollbar width
    const documentWidth = this.html.clientWidth;
    this.scrollbarWidth = window.innerWidth - documentWidth;
  }
  handleScrollbar() {
    if (this.scrollbarWidth > this.scrollbarThreshold) {   
      this.html.style.paddingRight = `${this.scrollbarWidth}px`;
    }
  }
  destroy() {
    this.html.style.paddingRight = '';
  }
}
// JavaScript for Dialog open/close
class CssDialog {
	constructor(element, options = {}) {
  	this.toggler = element;
		this.dialog = this.toggler.parentNode.querySelector("[data-css-dialog]");
    this.dialogClose = this.dialog.querySelector("[data-css-dialog-close]");
		this.dialogBackdrop = this.dialog.querySelector("[data-css-dialog-backdrop]");
    this.activeClass = "is-active";
    // get transition-duration from CSS variable --dialog-animation-speed
    this.dialogStyles = getComputedStyle(this.dialog);
    this.transitionDuration = parseFloat(this.dialogStyles.getPropertyValue("--dialog-animation-speed")) * 1000; // convert to miliseconds
    // ScrollbarHandler configuration
    this.scrollbarOptions = {
      scrollbarThreshold: 5,
      ...options  // add to options
    };
    // bind methods
    this.onKeyDown = this.onKeyDown.bind(this);
    this.removeStyles = this.removeStyles.bind(this);
    this.init();
  }
	init() {
  	this.toggler.addEventListener("click", () => this.toggle());
    this.dialogClose.addEventListener("click", () => this.hide());
    this.dialogBackdrop.addEventListener("click", () => this.hide());
    return this;
  }
  // add ESC trigger so this.activeClass will also be removed
  onKeyDown(e) {
    if ("Escape" === e.key) {
      e.preventDefault();
      this.dialogClose.click();
    }
  }
  // toggle between show & hide
  toggle() {
    if (this.dialog.classList.contains(this.activeClass)) {
      this.hide();
    } else {
      this.show();
    }
  }
  // show dialog, add event listener for ESC key, add overflow hidden on body to disable scroll
  show() {
  	document.documentElement.style.overflow = "hidden";
    window.addEventListener("keydown", this.onKeyDown);
    this.scrollbarHandler = new CssScrollbarHandler(this.scrollbarOptions);
    this.dialog.classList.add(this.activeClass);
    this.dialog.showModal();
  }
  // hide dialog, remove event listener for ESC key, remove overflow hidden on body to enable scroll
  hide() {
    window.removeEventListener("keydown", this.onKeyDown);
    this.dialog.classList.remove(this.activeClass);
    this.dialog.close();
    setTimeout(() => {
      this.removeStyles();
      // cleanup ScrollbarHandler
      if (this.scrollbarHandler) {
        this.scrollbarHandler.destroy();
        this.scrollbarHandler = null;
      }
    }, this.transitionDuration);
  }
  // remove styles from html
  removeStyles() {
  	document.documentElement.style.overflow = "";
  }
}
// add to webflow
window.Webflow ||= [];
window.Webflow.push(async () => {	
  const cssDialogElements = document.querySelectorAll("[data-css-dialog-toggle]");
  if (cssDialogElements.length > 0) {
  	cssDialogElements.forEach(element => {
    	new CssDialog(element);
  	});
  }
});
</script>
  </div>
  <div class="menu-dialog-styles u-embed w-embed">
    <style>
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
.menu-dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-close-ease: cubic-bezier(.36, 0, .087, 1);
  --modal-opacity-speed: .45s;
  --modal-opacity-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --nav-speed: .45s;
  --nav-ease: cubic-bezier(.215, .61, .355, 1);
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .menu-dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --modal-opacity-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.menu-dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.menu-dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.menu-dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.menu-dialog:not([open]) .menu-dialog_inner {
  opacity: 0;
  transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
}
.menu-dialog:not([open]) .menu-dialog_footer {
  opacity: 0;
  transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
}
.menu-dialog_inner,
.menu-dialog_footer {
	transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);  
}
.menu-dialog_inner:focus-visible {
	outline-offset: -.25rem;
}
@media screen and (prefers-reduced-motion: no-preference) {
  .menu-dialog:not([open]) .menu-dialog_inner {
    transform: translate3d(0, 8rem, 0) rotate(-3deg);
    transition: transform var(--modal-speed) var(--modal-close-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
  }
  .menu-dialog:not([open]) .menu-dialog_footer {
    transform: translate3d(0, 14rem, 0) rotate(3deg);
    transition: transform var(--modal-speed) var(--modal-close-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
  }
  .menu-dialog_inner,
  .menu-dialog_footer {
    transition: transform var(--modal-speed) var(--modal-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);  
  }
}
/* initial state of close button */
.menu-dialog:not([open]) .menu-dialog_button-close {
	transform: translate3d(0, -.75rem, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.menu-dialog_button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.menu-dialog_button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.menu-dialog_button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .menu-dialog_button-close:hover .icon,
  .menu-dialog_button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.menu-dialog:not([open]) .menu-dialog_backdrop {
	opacity: 0;
}
.menu-dialog_backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
/* hide scrollbar on transform animation */
.menu-dialog[open] .menu-dialog_outer {
	animation: hide-scroll var(--dialog-animation-speed) backwards;
}
@keyframes hide-scroll {
  from, to { overflow: hidden; } 
}
/* menu navigation */
.menu-dialog_nav-link {
	transition: opacity calc(var(--nav-speed) * .5) var(--nav-ease);
}
.menu-dialog_nav-list:has(.menu-dialog_nav-link:hover) .menu-dialog_nav-link:not(:hover) {
	opacity: .5;
}
@media screen and (prefers-reduced-motion: no-preference) {
  .menu-dialog_nav-link {
    transition: padding-left var(--nav-speed) var(--nav-ease), opacity calc(var(--nav-speed) * .5) var(--nav-ease);
  }
  .menu-dialog_nav-link::before {
    content: "";
    display: block;
    position: absolute;
    background-color: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E %3Cpath fill='%23000' fill-rule='evenodd' d='M9 5h2v2h2v2h2v2h-2v2h-2v2H9v-2H7v-2H5V9h2V7h2V5Z' clip-rule='evenodd'/%3E %3C/svg%3E") no-repeat 50% 50%;
    left: calc((var(--icon-height) + var(--size-8)) * -1);
    height: var(--icon-height);
    width: var(--icon-width);
    pointer-events: none;
    transform: translate3d(-.5rem, 0, 0);
    transition: transform calc(var(--nav-speed) * .75) var(--nav-ease), opacity calc(var(--nav-speed) * .5) var(--nav-ease);
    opacity: 0;
  }
  .menu-dialog_nav-link:hover,
  .menu-dialog_nav-link:focus-visible {
    padding-left: var(--size-4);
  }
  .menu-dialog_nav-link:hover::before,
  .menu-dialog_nav-link:focus-visible::before {
    transform: translate3d(.5rem, 0, 0);
    opacity: 1;
  }
}
.button_dialog-bg::after {
background-color:none!important;
position:relative!important;
}
</style>
  </div>
  <div class="menu-dialog-javascript u-embed w-embed w-script">
    <script defer="">
// handles scrollbar jump when opening modal and disabling scrolling behind the modal
// normaly scrollbar-gutter: stable; would handle this, but safari doesn't support it at this time
class MenuScrollbarHandler {
  constructor(options = {}) {
    this.html = document.documentElement;
    this.scrollbarThreshold = options.scrollbarThreshold || 5;
    this.scrollbarWidth = 0;
    this.calculateScrollbarWidth();
    this.handleScrollbar();
  }
  calculateScrollbarWidth() {
    // Berechne die tatsächliche Scrollbar-Breite
    const documentWidth = this.html.clientWidth;
    this.scrollbarWidth = window.innerWidth - documentWidth;
  }
  handleScrollbar() {
    if (this.scrollbarWidth > this.scrollbarThreshold) {   
      this.html.style.paddingRight = `${this.scrollbarWidth}px`;
    }
  }
  destroy() {
    this.html.style.paddingRight = '';
  }
}
// JavaScript for Dialog open/close
class MenuDialog {
	constructor(element, options = {}) {
  	this.toggler = element;
		this.dialog = this.toggler.parentNode.querySelector("[data-menu-dialog]");
    this.dialogClose = this.dialog.querySelector("[data-menu-dialog-close]");
		this.dialogBackdrop = this.dialog.querySelector("[data-menu-dialog-backdrop]");
    this.activeClass = "is-active";
    // Hole die transition-duration aus der CSS Variable
    this.dialogStyles = getComputedStyle(this.dialog);
    this.transitionDuration = parseFloat(this.dialogStyles.getPropertyValue("--dialog-animation-speed")) * 1000; // convert to miliseconds
    // ScrollbarHandler Konfiguration
    this.scrollbarOptions = {
      scrollbarThreshold: 5,
      ...options  // Hier fügen wir die übergebenen Optionen hinzu
    };
    // Bind methods
    this.onKeyDown = this.onKeyDown.bind(this);
    this.removeStyles = this.removeStyles.bind(this);
    this.init();
  }
	init() {
  	this.toggler.addEventListener("click", () => this.toggle());
    this.dialogClose.addEventListener("click", () => this.hide());
    this.dialogBackdrop.addEventListener("click", () => this.hide());
    return this;
  }
  // add ESC trigger so this.activeClass will also be removed
  onKeyDown(e) {
    if ("Escape" === e.key) {
      e.preventDefault();
      this.dialogClose.click();
    }
  }
  // toggle between show & hide
  toggle() {
    if (this.dialog.classList.contains(this.activeClass)) {
      this.hide();
    } else {
      this.show();
    }
  }
  // show dialog, add event listener for ESC key, add overflow hidden on body to disable scroll
  show() {
  	document.documentElement.style.overflow = "hidden";
    console.log("test")
    window.addEventListener("keydown", this.onKeyDown);
    this.scrollbarHandler = new MenuScrollbarHandler(this.scrollbarOptions);
    this.dialog.classList.add(this.activeClass);
    this.dialog.showModal();
  }
  // hide dialog, remove event listener for ESC key, remove overflow hidden on body to enable scroll
  hide() {
    window.removeEventListener("keydown", this.onKeyDown);
    this.dialog.classList.remove(this.activeClass);
    this.dialog.close();
    setTimeout(() => {
      this.removeStyles();
      // Cleanup ScrollbarHandler
      if (this.scrollbarHandler) {
        this.scrollbarHandler.destroy();
        this.scrollbarHandler = null;
      }
    }, this.transitionDuration);
  }
  // remove styles from html
  removeStyles() {
  	document.documentElement.style.overflow = "";
  }
}
window.Webflow ||= [];
window.Webflow.push(async () => {	
  const menuDialogElements = document.querySelectorAll("[data-menu-dialog-toggle]");
  if (menuDialogElements.length > 0) {
  	menuDialogElements.forEach(element => {
    	new MenuDialog(element);
  	});
  }
});
</script>
  </div>
  <div class="flickity w-embed">
    <style>
.flickity-slider-group {
  width: 100%;
  position: relative;
}
.flickity-viewport {
  overflow: visible;
  width: 100%;
}
.flickity-list {
  width: 100%;
  display: flex;
  /* Prevent text selection during drag */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  /* Remove outline */
  outline: none !important;
}
/* Remove outline from all flickity elements */
.flickity-enabled,
.flickity-viewport,
.flickity-slider,
.flickity-slider > * {
  outline: none !important;
}
.flickity-item {
  width: calc((99.99% / var(--flick-col, 3))  - (var(--flick-gap, 2em) * ((var(--flick-col, 3)  - 1) / var(--flick-col, 3))));
  margin-right: var(--flick-gap, 2em);
  flex-shrink: 0;
  /* Prevent text selection during drag */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  /* Remove outline */
  outline: none !important;
}
.flickity-viewport {
  overflow: visible;
  width: 100%;
}
.flickity-controls {
  pointer-events: none;
  flex-flow: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
}
.flickity-arrows {
  pointer-events: none;
  justify-content: space-between;
  align-items: center;
  width: calc(100% + 3em);
  display: flex;
  position: relative;
}
.flickity-arrow {
  pointer-events: auto;
  color: #efeeec;
  cursor: pointer;
  background-color: #131313;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 3em;
  height: 3em;
  padding-left: .75em;
  padding-right: .75em;
  display: flex;
}
.flickity-arrow.is--flipped {
  transform: scaleX(-1);
}
[data-flickity-control][disabled] {
  visibility: hidden;
  opacity: 0;
  pointer-events: none;
}
.flickity-dots {
  width: 100%;
  padding-top: 4em;
  position: absolute;
  top: 100%;
  left: 0;
}
.flickity-dots-list {
  grid-column-gap: .75em;
  grid-row-gap: .75em;
  justify-content: center;
  align-items: center;
  display: flex;
}
.flickity-dot {
  pointer-events: auto;
  background-color: #d0cfcd;
  border-radius: 50%;
  width: .75em;
  height: .75em;
  cursor: pointer;
}
[data-flickity-dot="active"] {
  background-color: #131313;
}
/* Turn Flickity on */
[data-flickity-status="active"] [data-flickity-list]::after {
  content: "flickity";
  display: none;
}
[data-flickity-status="active"] [data-flickity-list] {
  display: block;
}
/* ------------ Flickity Slider - Cards  ------------ */
/* Desktop */
@media screen and (min-width: 992px) {
  [data-flickity-type="cards"] {
    --flick-col: 1.2;
    --flick-gap: 2em;
  }
  /* Turn Flickity OFF & Hide Controls - Firefox compatible version */
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list]::after,
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list]::after,
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list]::after {
    content:""; 
    display: block;
  }
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list],
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list],
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list] {
    display: flex;
  }
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-controls],
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-controls],
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-controls] {
    display: none;
  }
  [data-flickity-type="cards"] [data-flickity-dot]:nth-last-child(-n+2) {display: none;} /* Hide last two dots */
}
/* Tablet */
@media (min-width: 768px) and (max-width: 991px) {
  [data-flickity-type="cards"] {
    --flick-col: 1.2;
    --flick-gap: 1.5em;
  }
  /* Turn Flickity OFF & Hide Controls - Firefox compatible version */
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list]::after,
  [data-flickity-type="cards"][data-flickity-count="2"] [data-flickity-list]::after {
    content:""; 
    display: block;
  }
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list],
  [data-flickity-type="cards"][data-flickity-count="2"] [data-flickity-list] {
    display: flex;
  }
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-controls],
  [data-flickity-type="cards"][data-flickity-count="2"] [data-flickity-controls] {
    display: none;
  }
  [data-flickity-type="cards"] [data-flickity-dot]:nth-last-child(1) {display: none;} /* Hide last dot */
}
/* Mobile */
@media screen and (max-width: 767px) {
  [data-flickity-type="cards"] {
    --flick-col: 1.2;
    --flick-gap: 1em;
  }
  /* Turn Flickity OFF & Hide Controls - Firefox compatible version */
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list]::after {
    content:""; 
    display: block;
  }
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-list] {
    display: flex;
  }
  [data-flickity-type="cards"][data-flickity-count="1"] [data-flickity-controls] {
    display: none;
  }
}
/* Demo Card */
.demo-card {
  grid-column-gap: 1.25em;
  grid-row-gap: 1.25em;
  background-color: #efeeec;
  border-radius: 1.5em;
  flex-flow: column;
  width: 100%;
  padding: 1em 1em 1.5em;
  display: flex;
  position: relative;
}
.before__125 {
  pointer-events: none;
  padding-top: 100%;
}
.demo-card__image {
  background-color: #e2e1df;
  border-radius: .5em;
  justify-content: center;
  align-items: center;
  width: 100%;
  display: flex;
  position: relative;
}
.demo-card__h2 {
  margin-top: 0;
  margin-bottom: 0;
  margin-left: .5em;
  font-size: 2em;
  font-weight: 500;
  line-height: 1;
}
.demo-card__emoji {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 5em;
  font-weight: 500;
  line-height: 1;
  position: absolute;
}
</style>
  </div>
  <div class="page-wrapper">
    <div class="global-styles w-embed">
      <style>
/* Set color style to inherit */
.inherit-color * {
    color: inherit;
}
/* Focus state style for keyboard navigation for the focusable elements */
*[tabindex]:focus-visible,
  input[type="file"]:focus-visible {
   outline: 0.125rem solid #4d65ff;
   outline-offset: 0.125rem;
}
/* Get rid of top margin on first element in any rich text element */
.w-richtext > :not(div):first-child, .w-richtext > div:first-child > :first-child {
  margin-top: 0 !important;
}
/* Get rid of bottom margin on last element in any rich text element */
.w-richtext>:last-child, .w-richtext ol li:last-child, .w-richtext ul li:last-child {
	margin-bottom: 0 !important;
}
/* Prevent all click and hover interaction with an element */
.pointer-events-off {
	pointer-events: none;
}
/* Enables all click and hover interaction with an element */
.pointer-events-on {
  pointer-events: auto;
}
/* Create a class of .div-square which maintains a 1:1 dimension of a div */
.div-square::after {
	content: "";
	display: block;
	padding-bottom: 100%;
}
/* Make sure containers never lose their center alignment */
.container-medium,.container-small, .container-large {
	margin-right: auto !important;
  margin-left: auto !important;
}
/* 
Make the following elements inherit typography styles from the parent and not have hardcoded values. 
Important: You will not be able to style for example "All Links" in Designer with this CSS applied.
Uncomment this CSS to use it in the project. Leave this message for future hand-off.
*/
/*
a,
.w-input,
.w-select,
.w-tab-link,
.w-nav-link,
.w-dropdown-btn,
.w-dropdown-toggle,
.w-dropdown-link {
  color: inherit;
  text-decoration: inherit;
  font-size: inherit;
}
*/
/* Apply "..." after 3 lines of text */
.text-style-3lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}
/* Apply "..." after 2 lines of text */
.text-style-2lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
/* Adds inline flex display */
.display-inlineflex {
  display: inline-flex;
}
/* These classes are never overwritten */
.hide {
  display: none !important;
}
@media screen and (max-width: 991px) {
    .hide, .hide-tablet {
        display: none !important;
    }
}
  @media screen and (max-width: 767px) {
    .hide-mobile-landscape{
      display: none !important;
    }
}
  @media screen and (max-width: 479px) {
    .hide-mobile{
      display: none !important;
    }
}
.margin-0 {
  margin: 0rem !important;
}
.padding-0 {
  padding: 0rem !important;
}
.spacing-clean {
padding: 0rem !important;
margin: 0rem !important;
}
.margin-top {
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-top {
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-right {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-right {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-bottom {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-bottom {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
.margin-left {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-left {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-horizontal {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-horizontal {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-vertical {
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-vertical {
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
/* Apply "..." at 100% width */
.truncate-width { 
		width: 100%; 
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 
}
/* Removes native scrollbar */
.no-scrollbar {
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none; 
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
.hyphen
 {
  word-break: break-word !important;
  -webkit-hyphens: auto !important;
  -moz-hyphens: auto !important;
  -ms-hyphens: auto !important;
  -o-hyphens: auto !important;
  hyphens: auto !important;
}
</style>
      <style>
/* Fluid Typography System for Woelfli Bauplanung */
:root {
  /* Base responsive variables */
  --fluid-min-width: 320;
  --fluid-max-width: 1200;
  --fluid-screen: 100vw;
  --fluid-bp: calc((var(--fluid-screen) - var(--fluid-min-width) / 16 * 1rem) / (var(--fluid-max-width) - var(--fluid-min-width)));
}
/* Heading Styles */
/* H1 - Main Headings */
/* Heading Styles - Adjusted to maintain hierarchy */
/* H1 - Main Headings */
h1, .heading-style-h1 {
  font-size: clamp(26px, calc(26px + 26 * var(--fluid-bp)), 52px);
  line-height: clamp(31px, calc(31px + 26.72 * var(--fluid-bp)), 56.72px);
}
/* H2 - Section Headings */
h2, .heading-style-h2 {
  font-size: clamp(18px, calc(18px + 10 * var(--fluid-bp)), 28px);
  line-height: clamp(25px, calc(25px + 16.4 * var(--fluid-bp)), 48.4px);
}
/* H3 - Sub-Section Headings */
h3, .heading-style-h3 {
  font-size: clamp(22px, calc(22px + 6 * var(--fluid-bp)), 28px);
  line-height: clamp(28px, calc(28px + 5.6 * var(--fluid-bp)), 33.6px);
}
/* H4 - Minor Headings */
h4, .heading-style-h4 {
  font-size: clamp(18px, calc(18px + 4 * var(--fluid-bp)), 22px);
  line-height: clamp(20px, calc(20px + 2 * var(--fluid-bp)), 22px);
}
/* H5 */
.heading-style-h5 {
  font-size: clamp(20px, calc(20px + 4 * var(--fluid-bp)), 24px);
  line-height: clamp(28px, calc(28px + 5.6 * var(--fluid-bp)), 33.6px);
}
/* H6 */
.heading-style-h6 {
  font-size: clamp(18px, calc(18px + 2 * var(--fluid-bp)), 20px);
  line-height: clamp(24px, calc(24px + 4 * var(--fluid-bp)), 28px);
}
/* Text Size Styles */
/* Large Text */
.text-size-large, .portfolio-expose {
  font-size: clamp(18px, calc(18px + 4 * var(--fluid-bp)), 22px);
  line-height: clamp(28px, calc(28px + 5.88 * var(--fluid-bp)), 33.88px);
}
/* Medium Text */
.text-size-medium {
  font-size: clamp(17px, calc(17px + 2 * var(--fluid-bp)), 18px);
  line-height: clamp(24px, calc(24px + 3 * var(--fluid-bp)), 27px);
}
/* Regular Text */
.text-size-regular, p {
  font-size: clamp(16px, calc(16px + 1 * var(--fluid-bp)), 16px);
  line-height: clamp(22px, calc(22px + 2 * var(--fluid-bp)), 24px);
}
/* Small Text */
.text-size-small {
  font-size: clamp(13px, calc(13px + 1 * var(--fluid-bp)), 14px);
  line-height: clamp(19px, calc(19px + 2 * var(--fluid-bp)), 21px);
}
/* Tiny Text */
.text-size-tiny {
  font-size: clamp(11px, calc(11px + 1 * var(--fluid-bp)), 12px);
  line-height: clamp(16px, calc(16px + 2 * var(--fluid-bp)), 18px);
}
/* Special Elements */
/* Buttons */
.button, .btn {
  font-size: clamp(15px, calc(15px + 1 * var(--fluid-bp)), 16px);
  line-height: clamp(22px, calc(22px + 2 * var(--fluid-bp)), 24px);
}
/* Navigation Links */
.nav-link, .menu-link {
  font-size: clamp(14px, calc(14px + 2 * var(--fluid-bp)), 16px);
  line-height: clamp(20px, calc(20px + 4 * var(--fluid-bp)), 24px);
}
</style>
    </div>
    <main class="main-wrapper">
      <header class="project-header text-color-white">
        <div data-collapse="medium" data-animation="default" data-duration="400" fs-scrolldisable-element="smart-nav" data-easing="ease" data-easing2="ease" role="banner" class="navbar2_component w-nav">
          <div class="navbar2_container">
            <a href="../index.html" class="navbar2_logo-link w-nav-brand">
              <div class="w-embed"><svg width="81" height="34" viewbox="0 0 81 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M0.330078 11.272V10.568C1.01609 10.4199 2.03156 10.3631 3.37649 10.3974C3.86031 10.4082 4.14644 10.6384 4.2349 11.0879C4.52916 12.5682 5.3966 16.9451 6.83722 24.2186C6.86791 24.3757 6.91575 24.484 6.98074 24.5436C7.0005 24.5617 7.02458 24.5744 7.05067 24.5806C7.07676 24.5868 7.10399 24.5862 7.12978 24.5788C7.15557 24.5715 7.17905 24.5577 7.19799 24.5387C7.21693 24.5198 7.2307 24.4962 7.23799 24.4704L10.9262 10.8171C10.9582 10.6997 11.0278 10.596 11.1242 10.5221C11.2206 10.4482 11.3385 10.4082 11.4596 10.4082H13.1034C13.2349 10.4083 13.3627 10.4517 13.467 10.5315C13.5713 10.6113 13.6462 10.7231 13.6801 10.8496C15.6786 18.2422 16.9053 22.7536 17.3602 24.3838C17.418 24.5932 17.5209 24.6275 17.6689 24.4867C17.6899 24.4673 17.7042 24.4427 17.7095 24.4163L20.4039 10.9227C20.4334 10.7725 20.5143 10.6373 20.6328 10.54C20.7514 10.4427 20.9002 10.3894 21.0538 10.3893H23.8592C23.9259 10.3891 23.9918 10.4038 24.0521 10.4323C24.1124 10.4608 24.1656 10.5023 24.2078 10.5539C24.2501 10.6055 24.2803 10.6659 24.2964 10.7306C24.3124 10.7953 24.3139 10.8628 24.3006 10.9281L19.8569 33.1873C19.8269 33.3347 19.747 33.4672 19.6305 33.5625C19.5141 33.6577 19.3683 33.7098 19.2178 33.7099H17.0434C16.6733 33.7099 16.4404 33.5321 16.3447 33.1764L12.4724 18.8001C12.4651 18.7731 12.4506 18.7487 12.4305 18.7296C12.4105 18.7105 12.3856 18.6974 12.3587 18.6917C12.2197 18.6611 12.1258 18.7414 12.077 18.9327C11.1961 22.3086 9.92064 27.0782 8.25075 33.2414C8.2134 33.3776 8.13275 33.4977 8.0212 33.5833C7.90966 33.6689 7.77339 33.7153 7.63335 33.7153H5.42368C5.2609 33.7153 5.10314 33.659 4.97724 33.5558C4.85134 33.4526 4.76509 33.309 4.73316 33.1493L0.330078 11.272Z" fill="white"></path>
                  <path d="M48.9372 29.0686C49.5952 29.8268 50.4428 29.8024 51.3337 30.0082C51.4495 30.0342 51.5532 30.0985 51.6283 30.1908C51.7034 30.2831 51.7456 30.398 51.748 30.5173C51.7679 31.2647 51.7778 31.9895 51.7778 32.6918C51.776 33.2424 51.4998 33.5195 50.9492 33.5231C49.2648 33.5393 47.5128 33.2442 46.2401 32.0716C44.6289 30.5823 44.3066 27.7823 44.3012 25.5076C44.2868 18.3623 44.2877 10.605 44.3039 2.23575C44.3039 1.45858 44.2118 0.857422 45.2517 0.857422C45.9558 0.857422 46.7203 0.864643 47.5453 0.879085C47.7304 0.882632 47.9067 0.958657 48.0363 1.09082C48.166 1.22298 48.2386 1.40073 48.2385 1.58585C48.2476 6.83381 48.2448 14.7436 48.2304 25.3154C48.2277 26.515 48.2196 28.2399 48.9372 29.0686Z" fill="white"></path>
                  <path d="M59.1849 14.1371C59.0351 14.1371 58.9602 14.2121 58.9602 14.3619V32.7487C58.9602 33.2614 58.7038 33.5177 58.1911 33.5177H55.7757C55.2774 33.5177 55.0283 33.2677 55.0283 32.7676V14.3727C55.0283 14.2157 54.9488 14.1371 54.79 14.1371H54.2376C54.0803 14.1371 53.9294 14.0747 53.8182 13.9634C53.707 13.8522 53.6445 13.7014 53.6445 13.5441V11.218C53.6445 11.0794 53.6996 10.9465 53.7976 10.8484C53.8956 10.7504 54.0285 10.6954 54.1672 10.6954H54.7575C54.8257 10.6954 54.8911 10.6683 54.9394 10.62C54.9876 10.5718 55.0147 10.5063 55.0147 10.4381C55.0147 9.51381 55.0319 8.62471 55.0662 7.77081C55.1203 6.4349 55.3803 5.17842 55.8461 4.00138C56.0717 3.42549 56.4445 2.87578 56.9644 2.35225C58.183 1.12827 60.3385 0.800613 62.0499 0.879143C62.5698 0.903514 62.7567 1.19055 62.7756 1.68881C62.8063 2.55354 62.8009 3.2928 62.7594 3.9066C62.7341 4.29654 62.5265 4.50415 62.1366 4.52942C60.4739 4.63503 59.2851 5.13871 59.0766 6.96926C58.9557 8.02896 58.9232 9.17351 58.9791 10.4029C58.9882 10.5979 59.0902 10.6954 59.2851 10.6954H61.4406C61.8956 10.6954 62.123 10.9228 62.123 11.3778V13.4547C62.123 13.5444 62.1054 13.6331 62.0711 13.7159C62.0368 13.7987 61.9865 13.8739 61.9232 13.9373C61.8598 14.0006 61.7846 14.0509 61.7018 14.0852C61.619 14.1195 61.5303 14.1371 61.4406 14.1371H59.1849Z" fill="white"></path>
                  <path d="M70.2085 29.0634C70.8666 29.8216 71.7141 29.7972 72.6051 30.003C72.7204 30.0289 72.8238 30.0928 72.8989 30.1846C72.9739 30.2763 73.0163 30.3906 73.0194 30.5094C73.0392 31.2568 73.0501 31.9816 73.0519 32.6839C73.0501 33.2345 72.7738 33.5116 72.2232 33.5152C70.5389 33.5341 68.7869 33.239 67.5141 32.0665C65.9029 30.5798 65.578 27.7798 65.5726 25.5052C65.5545 18.3617 65.5518 10.6062 65.5644 2.23868C65.5644 1.46151 65.4724 0.860352 66.5122 0.860352C67.2163 0.860352 67.9808 0.86667 68.8058 0.879307C68.9909 0.882853 69.1672 0.958879 69.2969 1.09104C69.4265 1.2232 69.4991 1.40095 69.4991 1.58607C69.5099 6.83403 69.5108 14.7421 69.5018 25.3102C69.4991 26.5098 69.4909 28.2347 70.2085 29.0634Z" fill="white"></path>
                  <path d="M30.3661 8.10338C31.6179 8.10338 32.6327 7.08862 32.6327 5.83684C32.6327 4.58507 31.6179 3.57031 30.3661 3.57031C29.1144 3.57031 28.0996 4.58507 28.0996 5.83684C28.0996 7.08862 29.1144 8.10338 30.3661 8.10338Z" fill="white"></path>
                  <path d="M36.3356 8.10684C37.5889 8.10684 38.6049 7.09087 38.6049 5.8376C38.6049 4.58433 37.5889 3.56836 36.3356 3.56836C35.0824 3.56836 34.0664 4.58433 34.0664 5.8376C34.0664 7.09087 35.0824 8.10684 36.3356 8.10684Z" fill="white"></path>
                  <path d="M78.2829 8.10684C79.5362 8.10684 80.5521 7.09087 80.5521 5.8376C80.5521 4.58433 79.5362 3.56836 78.2829 3.56836C77.0296 3.56836 76.0137 4.58433 76.0137 5.8376C76.0137 7.09087 77.0296 8.10684 78.2829 8.10684Z" fill="white"></path>
                  <path d="M33.3314 10.0234C36.1693 10.0234 38.4331 11.4965 39.5353 14.1314C40.5724 16.6037 40.3341 19.5445 40.3341 21.8679C40.3341 24.1913 40.5751 27.1321 39.538 29.6044C38.4331 32.2392 36.172 33.7151 33.3341 33.7151C30.4962 33.7151 28.2351 32.2392 27.1303 29.6071C26.0931 27.1321 26.3341 24.1913 26.3341 21.8679C26.3314 19.5472 26.0931 16.6064 27.1276 14.1314C28.2324 11.4993 30.4935 10.0234 33.3314 10.0234ZM30.2633 21.8706C30.2633 24.9197 30.2715 26.5607 30.2877 26.7936C30.4177 28.5538 31.3763 30.081 33.3368 30.081C35.3001 30.0837 36.2587 28.5538 36.3887 26.7936C36.4049 26.5607 36.413 24.9197 36.413 21.8706C36.413 18.8197 36.4049 17.1778 36.3887 16.9449C36.2587 15.1847 35.3001 13.6575 33.3395 13.6575C31.3763 13.6575 30.4177 15.1847 30.2877 16.9449C30.2715 17.1778 30.2633 18.8197 30.2633 21.8706Z" fill="white"></path>
                  <path d="M79.5267 10.1943H77.0029C76.614 10.1943 76.2988 10.5096 76.2988 10.8984V32.8163C76.2988 33.2052 76.614 33.5204 77.0029 33.5204H79.5267C79.9155 33.5204 80.2307 33.2052 80.2307 32.8163V10.8984C80.2307 10.5096 79.9155 10.1943 79.5267 10.1943Z" fill="white"></path>
                </svg></div>
            </a>
            <nav role="navigation" id="w-node-d392c3b2-4450-74b5-1131-dbdbdf68d69c-df68d698" class="navbar2_menu is-page-height-tablet w-nav-menu">
              <a href="../team.html" class="navbar2_link text-size-medium w-nav-link">Team</a>
              <a href="../leistungen.html" class="navbar2_link text-size-medium w-nav-link">Leistungen</a>
              <a href="../marken.html" class="navbar2_link text-size-medium w-nav-link">Marken</a>
              <a href="../projekte.html" class="navbar2_link text-size-medium w-nav-link">Projekte</a>
              <a href="../karriere.html" class="navbar2_link text-size-medium w-nav-link">Karriere</a>
              <a href="../kontakt.html" class="navbar2_link text-size-medium w-nav-link">Kontakt</a>
            </nav>
            <a href="../identitaet.html" class="navbar-icon-link w-nav-brand">
              <div class="icon-embed-small w-embed"><svg width="191" height="194" viewbox="0 0 191 194" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M183.945 9.64457C183.926 9.25248 183.877 8.86021 183.807 8.46873C183.796 8.41001 183.782 8.3527 183.77 8.29435C183.713 8.00986 183.642 7.72715 183.556 7.44559C183.532 7.36472 183.507 7.28434 183.481 7.20432C183.393 6.94382 183.292 6.68607 183.18 6.43009C183.148 6.35709 183.12 6.28288 183.086 6.21085C183.064 6.16373 183.048 6.11533 183.025 6.0684C182.727 5.46134 182.366 4.90635 181.96 4.39841C181.94 4.37302 181.917 4.34946 181.897 4.32426C181.692 4.07298 181.474 3.83591 181.246 3.61082C181.204 3.5687 181.16 3.52781 181.117 3.48655C180.884 3.26585 180.642 3.05839 180.39 2.86509C180.361 2.84287 180.333 2.81937 180.304 2.79746C179.714 2.35624 179.076 1.99046 178.404 1.70524C178.361 1.68674 178.317 1.67112 178.273 1.65342C177.984 1.53599 177.69 1.43412 177.391 1.34678C177.336 1.33079 177.282 1.314 177.226 1.29911C175.826 0.918678 174.336 0.871864 172.877 1.19956C172.872 1.2006 172.868 1.20145 172.863 1.20255C172.178 1.35758 171.5 1.5853 170.843 1.91074L95.4955 38.9214L20.1513 1.91074C19.4994 1.58891 18.8269 1.36234 18.1481 1.20725C18.1288 1.20286 18.1095 1.19938 18.0902 1.19517C17.7393 1.11741 17.3874 1.0615 17.0345 1.02634C17.0257 1.02543 17.0169 1.02433 17.008 1.02348C15.8966 0.916237 14.788 1.01621 13.7309 1.30711C13.7045 1.31437 13.6783 1.32243 13.6519 1.32993C13.3104 1.42722 12.9752 1.54295 12.6473 1.6793C12.6402 1.68223 12.6331 1.68473 12.6261 1.68766C11.5992 2.11759 10.6521 2.73661 9.83469 3.52372C9.81607 3.5416 9.7974 3.55912 9.7789 3.57719C9.52292 3.82798 9.27835 4.09226 9.051 4.37589C9.05002 4.37711 9.04886 4.37827 9.04788 4.37949C8.63694 4.89262 8.27115 5.45353 7.97019 6.0684C7.94718 6.11533 7.93112 6.16373 7.90897 6.21085C7.87516 6.28288 7.84671 6.35709 7.81473 6.43009C7.70243 6.68601 7.60135 6.94364 7.51425 7.20408C7.48746 7.2844 7.46268 7.36503 7.43808 7.44621C7.35263 7.72739 7.28153 8.00974 7.22446 8.29374C7.21268 8.35239 7.1987 8.40995 7.18808 8.46891C7.11746 8.86027 7.06858 9.25236 7.04996 9.64433L0.213412 124.731C0.0229827 127.923 1.52738 130.989 4.17435 132.792L90.3748 191.539C91.9173 192.593 93.7074 193.12 95.4973 193.12C97.2874 193.12 99.0774 192.593 100.62 191.539L186.814 132.792C189.461 130.989 190.965 127.923 190.775 124.731L183.945 9.64457ZM95.4973 173.004L18.685 120.655L24.3978 24.4765L73.8308 49.563L45.7385 63.3619C41.2253 65.5772 39.3655 71.0298 41.5808 75.5429C43.1614 78.7612 46.3986 80.6338 49.7565 80.6338C51.1022 80.6338 52.4733 80.3354 53.7619 79.7007L95.0284 59.4306C95.3401 59.4468 95.6509 59.4469 95.9626 59.4309L137.227 79.7007C138.515 80.3354 139.886 80.6338 141.232 80.6338C144.59 80.6338 147.827 78.7612 149.408 75.5429C151.623 71.0298 149.763 65.5772 145.25 63.3619L117.161 49.5639L166.591 24.4765L172.303 120.655L95.4973 173.004Z" fill="white"></path>
                </svg></div>
            </a>
            <div id="w-node-_7728f4df-2d5d-59ec-10e7-ad72bf765fec-bf765fec" class="navbar2_button-wrapper">
              <a href="#" class="button is-navbar2-button w-button">Button</a>
              <div class="navbar2_menu-button w-nav-button">
                <div class="menu-icon2">
                  <div class="menu-icon2_line-top"></div>
                  <div class="menu-icon2_line-middle">
                    <div class="menu-icon2_line-middle-inner"></div>
                  </div>
                  <div class="menu-icon2_line-bottom"></div>
                </div>
              </div>
              <div class="block-text_action"><button type="button" data-button="" data-menu-dialog-toggle="" aria-label="Open Menu" class="button_dialog is-menu-button">
                  <div class="menu-text hide">Menü</div><span class="button_dialog-menu-line-wrap"><span class="button_dialog-menu-line"></span><span class="button_dialog-menu-line"></span><span class="button_dialog-menu-line"></span></span><span class="button_dialog-bg"></span>
                </button>
                <dialog data-menu-dialog="" data-theme="light" class="menu-dialog">
                  <div class="menu-dialog_outer">
                    <div class="menu-dialog_inner">
                      <div class="menu-dialog_header"><span class="menu-dialog_header-text">Menü</span><button type="button" data-menu-dialog-close="" aria-label="Close Dialog" autofocus="" class="menu-dialog_button-close"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 24 24" fill="none" aria-hidden="true" class="icon">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.75827 17.2426L12.0009 12M17.2435 6.75736L12.0009 12M12.0009 12L6.75827 6.75736M12.0009 12L17.2435 17.2426" fill="currentColor" stroke="#000000" stroke-width="1.5" color="#000000"></path>
                          </svg></button></div>
                      <nav class="menu-dialog_nav">
                        <a href="../identitaet.html" class="navbar-icon-link icon-link-mobile w-nav-brand"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 32 32" fill="none" class="icon-embed-small">
                            <path d="M30.5892 1.59116C30.586 1.52648 30.578 1.46178 30.5664 1.39721C30.5646 1.38752 30.5623 1.37807 30.5603 1.36844C30.5509 1.32152 30.5392 1.27488 30.525 1.22844C30.5211 1.2151 30.5169 1.20184 30.5126 1.18864C30.4981 1.14567 30.4815 1.10316 30.463 1.06094C30.4577 1.04889 30.4531 1.03665 30.4475 1.02477C30.4439 1.017 30.4412 1.00902 30.4374 1.00128C30.3883 0.901142 30.3287 0.809597 30.2618 0.725813C30.2585 0.721625 30.2547 0.717739 30.2514 0.713582C30.2175 0.672134 30.1816 0.63303 30.144 0.595901C30.1371 0.588954 30.1298 0.582209 30.1227 0.575403C30.0843 0.538999 30.0444 0.504779 30.0028 0.472894C29.998 0.469229 29.9934 0.465353 29.9886 0.461739C29.8913 0.38896 29.786 0.328625 29.6752 0.281579C29.6681 0.278527 29.6608 0.275951 29.6536 0.273031C29.6059 0.253661 29.5574 0.236858 29.5081 0.222451C29.499 0.219814 29.4901 0.217044 29.4809 0.214588C29.25 0.151837 29.0042 0.144115 28.7635 0.198168C28.7627 0.198339 28.762 0.198479 28.7612 0.198661C28.6482 0.224233 28.5364 0.261795 28.428 0.315476L15.9996 6.42033L3.57166 0.315476C3.46413 0.26239 3.3532 0.225018 3.24124 0.199436C3.23805 0.198712 3.23487 0.198138 3.23169 0.197444C3.1738 0.184617 3.11576 0.175395 3.05755 0.169595C3.0561 0.169445 3.05465 0.169264 3.05318 0.169124C2.86985 0.151434 2.68699 0.167924 2.51263 0.215908C2.50827 0.217105 2.50395 0.218435 2.49959 0.219672C2.44326 0.23572 2.38797 0.254809 2.33389 0.2773C2.33272 0.277783 2.33154 0.278196 2.33039 0.278679C2.161 0.349595 2.00478 0.451702 1.86995 0.581534C1.86688 0.584484 1.8638 0.587373 1.86075 0.590354C1.81853 0.631721 1.77818 0.675314 1.74068 0.722098L1.74017 0.722692C1.67238 0.807332 1.61205 0.899853 1.56241 1.00128C1.55861 1.00902 1.55596 1.017 1.55231 1.02477C1.54673 1.03665 1.54204 1.04889 1.53676 1.06094C1.51824 1.10315 1.50157 1.14564 1.4872 1.1886C1.48278 1.20185 1.47869 1.21515 1.47463 1.22854C1.46054 1.27492 1.44881 1.3215 1.4394 1.36834C1.43746 1.37802 1.43515 1.38751 1.4334 1.39724C1.42175 1.46179 1.41369 1.52646 1.41061 1.59112L0.282937 20.5745C0.251526 21.101 0.499674 21.6067 0.936287 21.9041L15.1549 31.5944C15.4094 31.7682 15.7046 31.8551 15.9999 31.8551C16.2951 31.8551 16.5904 31.7682 16.8448 31.5944L31.0624 21.9041C31.499 21.6067 31.7471 21.101 31.7158 20.5745L30.5892 1.59116ZM15.9999 28.537L3.3298 19.9022L4.27211 4.03766L12.426 8.17564L7.79223 10.4517C7.04778 10.8172 6.74101 11.7166 7.10642 12.461C7.36714 12.9918 7.90111 13.3007 8.45499 13.3007C8.67696 13.3007 8.90312 13.2515 9.11568 13.1468L15.9225 9.80329C15.9739 9.80596 16.0252 9.80598 16.0766 9.80334L22.8831 13.1468C23.0956 13.2515 23.3217 13.3007 23.5437 13.3007C24.0976 13.3007 24.6316 12.9918 24.8924 12.461C25.2577 11.7166 24.9509 10.8172 24.2065 10.4517L19.5733 8.17579L27.7267 4.03766L28.6688 19.9022L15.9999 28.537Z" fill="currentColor"></path>
                          </svg></a>
                        <ul role="list" class="menu-dialog_nav-list w-list-unstyled">
                          <li class="menu-dialog_nav-list-item">
                            <a href="../index.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Start</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item">
                            <a href="../team.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Team</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item">
                            <a href="../leistungen.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Leistungen</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item">
                            <a href="../marken.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Marken</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item">
                            <a href="../projekte.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Projekte</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item">
                            <a href="../identitaet.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Wölfli Identität</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item">
                            <a href="../karriere.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Karriere</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item">
                            <a href="../kontakt.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Kontakt</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item hide">
                            <a href="tel:+41445352222" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer phone-number text-size-regular">+41 44 535 22 22</span></a>
                          </li>
                          <li class="menu-dialog_nav-list-item hide">
                            <a href="mailto:<EMAIL>" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer email"><EMAIL></span></a>
                          </li>
                        </ul>
                      </nav>
                    </div><button type="button" tabindex="-1" data-menu-dialog-backdrop="" class="menu-dialog_backdrop"></button>
                  </div>
                </dialog>
              </div>
            </div>
            <div class="button-row">
              <a data-cursor="Ruf den Chef direkt an" href="#" class="button w-inline-block">
                <div class="header-phone-svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" stroke-width="1.5" viewbox="0 0 24 24" fill="none" color="#000000" class="phone-svg">
                    <path d="M22 5H16M16 5L19 2M16 5L19 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg></div>
                <p class="button-text">+41 44 535 22 22</p>
                <div class="button-bg"></div>
              </a>
            </div>
            <div class="header-phone">
              <div class="header-phone-svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" stroke-width="1.5" viewbox="0 0 24 24" fill="none" color="#000000">
                  <path d="M22 5H16M16 5L19 2M16 5L19 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg></div>
            </div>
            <div class="cursor">
              <p class="cursor-paragraph">Learn more</p>
            </div>
          </div>
        </div>
        <div class="project-header-wrapper">
          <div class="padding-global">
            <div class="container-large">
              <div class="header5_content project-header-content">
                <div class="padding-section-large padding-custom">
                  <div class="project-header-copy">
                    <div id="w-node-_6f077b43-ccf6-21bd-f040-6df950c46be2-9c6bfe73" data-w-id="6f077b43-ccf6-21bd-f040-6df950c46be2" style="-webkit-transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="header114_content-left">
                      <h1 class="heading-style-h1">Aufenthaltsräume zu 6 Zimmern, Integration ins System, Umbau im Betrieb</h1>
                    </div>
                    <div id="w-node-_6f077b43-ccf6-21bd-f040-6df950c46beb-9c6bfe73" data-w-id="6f077b43-ccf6-21bd-f040-6df950c46beb" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="project-header-nav">
                      <div class="button-wrapper back-button-header">
                        <a href="../projekte.html" class="project-back-button is-icon w-inline-block">
                          <div class="icon-embed-xsmall w-embed"><svg width="17" height="14" viewbox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M6.82134 13.6127L7.73794 12.696L2.73794 7.61266L16.8213 7.61266L16.8213 6.2793L2.73794 6.2793L7.73794 1.19596L6.82134 0.279296L0.154638 6.94596L6.82134 13.6127Z" fill="black" fill-opacity="0.6"></path>
                            </svg></div>
                          <div class="text-size-medium">Zurück zur Projektübersicht</div>
                        </a>
                      </div>
                      <a id="w-node-e9e593ac-143a-538f-ed31-abcee1ac226a-9c6bfe73" href="../projekt/patio-areal.html" class="link-block w-inline-block">
                        <div class="next-project-link">
                          <div class="arrow-line">
                            <p class="text-size-medium">Nächstes Projekt</p>
                            <div class="icon-embed-xsmall w-embed"><svg width="20" height="17" viewbox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20.0022 8.48445L12.2951 16.1916L11.588 15.4845L18.088 8.98447H0.795166V7.98447H18.0881L11.588 1.48445L12.2951 0.777344L20.0022 8.48445Z" fill="black"></path>
                              </svg></div>
                          </div>
                          <p class="text-size-large text-weight-medium line-height">Patio Areal – Büroausbau Adecco</p>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="project-header-navigation">
                  <div class="button-wrapper">
                    <a href="#" class="project-back-button is-icon w-inline-block">
                      <div class="icon-embed-xsmall w-embed"><svg width="17" height="14" viewbox="0 0 17 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M6.82134 13.6127L7.73794 12.696L2.73794 7.61266L16.8213 7.61266L16.8213 6.2793L2.73794 6.2793L7.73794 1.19596L6.82134 0.279296L0.154638 6.94596L6.82134 13.6127Z" fill="black" fill-opacity="0.6"></path>
                        </svg></div>
                      <div class="text-size-medium">Zurück zur Projektübersicht</div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="header-gradient project-gradient"></div>
        </div>
        <div class="project-header-image"><img src="../images/ASZ--R.webp" loading="eager" sizes="(max-width: 1900px) 100vw, 1900px" srcset="../images/ASZ--R-p-500.webp 500w, ../images/ASZ--R-p-800.webp 800w, ../images/ASZ--R-p-1080.webp 1080w, ../images/ASZ--R-p-1600.webp 1600w, ../images/ASZ--R.webp 1900w" alt="" class="project-header-img"></div>
      </header>
      <header class="project-intro">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large padding-top">
              <div data-w-id="3e9d2601-8593-bfe4-425c-515d041c7506" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="project-intro-content">
                <div blocks-name="max-width-xlarge" blocks-slot-children="ST265" class="max-width-large header-text-width">
                  <div class="margin-bottom margin-small intro-margin"></div>
                  <div blocks-non-deletable="true" blocks-name="header105_heading-wrapper" class="header-intro-wrapper">
                    <h2 class="heading-style-h2 text-weight-medium line-height">Alterszentrum Rubiswil – Umbau / Erweiterung</h2>
                    <h2 class="heading-style-h2 hyphen">Umbau des Alterszentrum Rubiswil im Auftrag der Gemeinde Schwyz. Räumliche Anpassung und Umnutzung von drei ursprünglich als Aufenthaltsräume genutzten Flächen zu sechs neuen Bewohnerzimmern. </h2>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <section class="project-data">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="project-data-component">
                <div data-w-id="e7646857-a131-c976-9269-62c243ad6065" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="w-layout-grid project-data-content">
                  <div class="project-data-grid">
                    <div class="margin-bottom margin-small">
                      <p class="text-size-medium text-color-black text-weight-medium">Details zum Projekt</p>
                    </div>
                    <div class="project-data-list">
                      <div id="w-node-bb05d3ca-3b70-bc3a-81ee-936b0c73f6a8-9c6bfe73" class="layout66_item">
                        <div class="layout66_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed"><svg width="currentWidth" height="currentHeight" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M20.73 7.12L20.59 6.87C20.4094 6.56769 20.1547 6.31643 19.85 6.14L13.14 2.27C12.8362 2.09375 12.4913 2.00062 12.14 2H11.85C11.4987 2.00062 11.1538 2.09375 10.85 2.27L4.14 6.15C3.83697 6.32526 3.58526 6.57697 3.41 6.88L3.27 7.13C3.09375 7.43384 3.00062 7.77874 3 8.13V15.88C3.00062 16.2313 3.09375 16.5762 3.27 16.88L3.41 17.13C3.58979 17.4295 3.84049 17.6802 4.14 17.86L10.86 21.73C11.1623 21.9099 11.5082 22.0033 11.86 22H12.14C12.4913 21.9994 12.8362 21.9063 13.14 21.73L19.85 17.85C20.156 17.6787 20.4087 17.426 20.58 17.12L20.73 16.87C20.9041 16.5653 20.9971 16.221 21 15.87V8.12C20.9994 7.76874 20.9063 7.42384 20.73 7.12ZM11.85 4H12.14L18 7.38L12 10.84L6 7.38L11.85 4ZM13 19.5L18.85 16.12L19 15.87V9.11L13 12.58V19.5Z" fill="currentColor"></path>
                            </svg></div>
                        </div>
                        <div class="layout66_item-text-wrapper">
                          <p class="text-size-large"><span class="text-weight-medium">Fertigstellung:</span> 2018</p>
                        </div>
                      </div>
                      <div id="w-node-e7646857-a131-c976-9269-62c243ad606e-9c6bfe73" class="layout66_item">
                        <div class="layout66_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed"><svg width="currentWidth" height="currentHeight" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M20.73 7.12L20.59 6.87C20.4094 6.56769 20.1547 6.31643 19.85 6.14L13.14 2.27C12.8362 2.09375 12.4913 2.00062 12.14 2H11.85C11.4987 2.00062 11.1538 2.09375 10.85 2.27L4.14 6.15C3.83697 6.32526 3.58526 6.57697 3.41 6.88L3.27 7.13C3.09375 7.43384 3.00062 7.77874 3 8.13V15.88C3.00062 16.2313 3.09375 16.5762 3.27 16.88L3.41 17.13C3.58979 17.4295 3.84049 17.6802 4.14 17.86L10.86 21.73C11.1623 21.9099 11.5082 22.0033 11.86 22H12.14C12.4913 21.9994 12.8362 21.9063 13.14 21.73L19.85 17.85C20.156 17.6787 20.4087 17.426 20.58 17.12L20.73 16.87C20.9041 16.5653 20.9971 16.221 21 15.87V8.12C20.9994 7.76874 20.9063 7.42384 20.73 7.12ZM11.85 4H12.14L18 7.38L12 10.84L6 7.38L11.85 4ZM13 19.5L18.85 16.12L19 15.87V9.11L13 12.58V19.5Z" fill="currentColor"></path>
                            </svg></div>
                        </div>
                        <div class="layout66_item-text-wrapper">
                          <p class="text-size-large"><span class="text-weight-medium">Ort:</span> Schwyz</p>
                        </div>
                      </div>
                      <div id="w-node-e7646857-a131-c976-9269-62c243ad6074-9c6bfe73" class="layout66_item">
                        <div class="layout66_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed"><svg width="currentWidth" height="currentHeight" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M20.73 7.12L20.59 6.87C20.4094 6.56769 20.1547 6.31643 19.85 6.14L13.14 2.27C12.8362 2.09375 12.4913 2.00062 12.14 2H11.85C11.4987 2.00062 11.1538 2.09375 10.85 2.27L4.14 6.15C3.83697 6.32526 3.58526 6.57697 3.41 6.88L3.27 7.13C3.09375 7.43384 3.00062 7.77874 3 8.13V15.88C3.00062 16.2313 3.09375 16.5762 3.27 16.88L3.41 17.13C3.58979 17.4295 3.84049 17.6802 4.14 17.86L10.86 21.73C11.1623 21.9099 11.5082 22.0033 11.86 22H12.14C12.4913 21.9994 12.8362 21.9063 13.14 21.73L19.85 17.85C20.156 17.6787 20.4087 17.426 20.58 17.12L20.73 16.87C20.9041 16.5653 20.9971 16.221 21 15.87V8.12C20.9994 7.76874 20.9063 7.42384 20.73 7.12ZM11.85 4H12.14L18 7.38L12 10.84L6 7.38L11.85 4ZM13 19.5L18.85 16.12L19 15.87V9.11L13 12.58V19.5Z" fill="currentColor"></path>
                            </svg></div>
                        </div>
                        <div class="layout66_item-text-wrapper">
                          <p class="text-size-large"><span class="text-weight-medium">Kategorie:</span> Umbau </p>
                        </div>
                      </div>
                      <div id="w-node-e7646857-a131-c976-9269-62c243ad607a-9c6bfe73" class="layout66_item">
                        <div class="layout66_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed"><svg width="currentWidth" height="currentHeight" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M20.73 7.12L20.59 6.87C20.4094 6.56769 20.1547 6.31643 19.85 6.14L13.14 2.27C12.8362 2.09375 12.4913 2.00062 12.14 2H11.85C11.4987 2.00062 11.1538 2.09375 10.85 2.27L4.14 6.15C3.83697 6.32526 3.58526 6.57697 3.41 6.88L3.27 7.13C3.09375 7.43384 3.00062 7.77874 3 8.13V15.88C3.00062 16.2313 3.09375 16.5762 3.27 16.88L3.41 17.13C3.58979 17.4295 3.84049 17.6802 4.14 17.86L10.86 21.73C11.1623 21.9099 11.5082 22.0033 11.86 22H12.14C12.4913 21.9994 12.8362 21.9063 13.14 21.73L19.85 17.85C20.156 17.6787 20.4087 17.426 20.58 17.12L20.73 16.87C20.9041 16.5653 20.9971 16.221 21 15.87V8.12C20.9994 7.76874 20.9063 7.42384 20.73 7.12ZM11.85 4H12.14L18 7.38L12 10.84L6 7.38L11.85 4ZM13 19.5L18.85 16.12L19 15.87V9.11L13 12.58V19.5Z" fill="currentColor"></path>
                            </svg></div>
                        </div>
                        <div class="layout66_item-text-wrapper">
                          <p class="text-size-large"><span class="text-weight-medium">Architektur:</span> Wölfli Bauplanung GmbH</p>
                        </div>
                      </div>
                      <div id="w-node-eacf3222-3f28-d1b7-8742-67afbfce8c32-9c6bfe73" class="layout66_item">
                        <div class="layout66_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed"><svg width="currentWidth" height="currentHeight" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M20.73 7.12L20.59 6.87C20.4094 6.56769 20.1547 6.31643 19.85 6.14L13.14 2.27C12.8362 2.09375 12.4913 2.00062 12.14 2H11.85C11.4987 2.00062 11.1538 2.09375 10.85 2.27L4.14 6.15C3.83697 6.32526 3.58526 6.57697 3.41 6.88L3.27 7.13C3.09375 7.43384 3.00062 7.77874 3 8.13V15.88C3.00062 16.2313 3.09375 16.5762 3.27 16.88L3.41 17.13C3.58979 17.4295 3.84049 17.6802 4.14 17.86L10.86 21.73C11.1623 21.9099 11.5082 22.0033 11.86 22H12.14C12.4913 21.9994 12.8362 21.9063 13.14 21.73L19.85 17.85C20.156 17.6787 20.4087 17.426 20.58 17.12L20.73 16.87C20.9041 16.5653 20.9971 16.221 21 15.87V8.12C20.9994 7.76874 20.9063 7.42384 20.73 7.12ZM11.85 4H12.14L18 7.38L12 10.84L6 7.38L11.85 4ZM13 19.5L18.85 16.12L19 15.87V9.11L13 12.58V19.5Z" fill="currentColor"></path>
                            </svg></div>
                        </div>
                        <div class="layout66_item-text-wrapper">
                          <p class="text-size-large"><span class="text-weight-medium">Unsere Leistung:</span> Planung / Baumanagement</p>
                        </div>
                      </div>
                      <div id="w-node-c0d91e30-bc20-4f98-25c0-f7dfa4e0ad12-9c6bfe73" class="layout66_item">
                        <div class="layout66_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed"><svg width="currentWidth" height="currentHeight" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M20.73 7.12L20.59 6.87C20.4094 6.56769 20.1547 6.31643 19.85 6.14L13.14 2.27C12.8362 2.09375 12.4913 2.00062 12.14 2H11.85C11.4987 2.00062 11.1538 2.09375 10.85 2.27L4.14 6.15C3.83697 6.32526 3.58526 6.57697 3.41 6.88L3.27 7.13C3.09375 7.43384 3.00062 7.77874 3 8.13V15.88C3.00062 16.2313 3.09375 16.5762 3.27 16.88L3.41 17.13C3.58979 17.4295 3.84049 17.6802 4.14 17.86L10.86 21.73C11.1623 21.9099 11.5082 22.0033 11.86 22H12.14C12.4913 21.9994 12.8362 21.9063 13.14 21.73L19.85 17.85C20.156 17.6787 20.4087 17.426 20.58 17.12L20.73 16.87C20.9041 16.5653 20.9971 16.221 21 15.87V8.12C20.9994 7.76874 20.9063 7.42384 20.73 7.12ZM11.85 4H12.14L18 7.38L12 10.84L6 7.38L11.85 4ZM13 19.5L18.85 16.12L19 15.87V9.11L13 12.58V19.5Z" fill="currentColor"></path>
                            </svg></div>
                        </div>
                        <div class="layout66_item-text-wrapper">
                          <p class="text-size-large"><span class="text-weight-medium">Bauherrschaft:</span> Gemeinde Schwyz</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <a href="../projekte.html" class="link-block-3 w-inline-block">
                  <div data-w-id="3c7b89fd-d99e-fdf5-1d6b-9e1f64bfb4d1" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="cta-button">
                    <div class="icon-embed-xsmall back-arrow w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                      </svg></div>
                    <div class="heading-style-h4 cta-text">Zurück zur Projektübersicht<br></div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_content7 hide">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-small">
              <div data-w-id="34f9201c-9894-0354-22d4-83299abd8719" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="content7_component">
                <div class="max-width-medium">
                  <div class="content7_content-wrapper">
                    <div class="margin-bottom margin-small">
                      <h2 id="w-node-_34f9201c-9894-0354-22d4-83299abd871d-9c6bfe73" class="heading-style-h2">Raum für Entwicklung</h2>
                    </div>
                    <div class="text-rich-text w-richtext">
                      <p class="text-size-large">Der neugestaltete Aussenbereich ergänzt das Gesamtkonzept. Entstanden ist eine vielseitige Spiel- und Lernlandschaft, die den Bedürfnissen verschiedener Altersgruppen gerecht wird und sowohl motorische als auch soziale Entwicklung fördert. Die naturnahe Gestaltung bietet Platz für bewegungsintensive Aktivitäten sowie Rückzugsorte für ruhigere Beschäftigungen.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="project-img-slider">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-small">
              <div data-flickity-status="not-active" data-flickity-type="cards" data-flickity-count="2" data-w-id="c4014f4e-a7ce-f768-7889-8a2f3cc4e45e" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="flickity-slider-group">
                <div data-flickity-list="" class="flickity-list">
                  <div data-flickity-item="" class="flickity-item">
                    <div class="slider-card">
                      <div class="slider-card-image">
                        <div class="before__125"></div>
                        <h2 class="slider-card-icon-optional">🏀</h2><img src="../images/aussen_LE_upscale_strong_x4.avif" loading="lazy" alt="" class="flickity-slider-img">
                      </div>
                    </div>
                  </div>
                  <div data-flickity-item="" class="flickity-item">
                    <div class="slider-card">
                      <div class="slider-card-image">
                        <div class="before__125"></div>
                        <h2 class="slider-card-icon-optional">🎾</h2><img src="../images/Foto-25.09.18-15-59-53.webp" loading="lazy" sizes="(max-width: 1439px) 83vw, 1125px" srcset="../images/Foto-25.09.18-15-59-53-p-500.webp 500w, ../images/Foto-25.09.18-15-59-53-p-800.webp 800w, ../images/Foto-25.09.18-15-59-53-p-1080.webp 1080w, ../images/Foto-25.09.18-15-59-53.webp 1125w" alt="" class="flickity-slider-img">
                      </div>
                    </div>
                  </div>
                  <div data-flickity-item="" class="flickity-item">
                    <div class="slider-card">
                      <div class="slider-card-image">
                        <div class="before__125"></div>
                        <h2 class="slider-card-icon-optional">⚾</h2><img src="../images/Foto-25.09.18-15-56-14.webp" loading="lazy" sizes="(max-width: 1439px) 83vw, 1125px" srcset="../images/Foto-25.09.18-15-56-14-p-500.webp 500w, ../images/Foto-25.09.18-15-56-14-p-800.webp 800w, ../images/Foto-25.09.18-15-56-14-p-1080.webp 1080w, ../images/Foto-25.09.18-15-56-14.webp 1125w" alt="" class="flickity-slider-img">
                      </div>
                    </div>
                  </div>
                </div>
                <div data-flickity-controls="" class="flickity-controls">
                  <div class="flickity-arrows">
                    <div data-flickity-control="prev" class="flickity-arrow is--flipped"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 24 24" fill="none">
                        <path d="M14 19L21 12L14 5" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></path>
                        <path d="M21 12H2" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></path>
                      </svg></div>
                    <div data-flickity-control="next" class="flickity-arrow"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 24 24" fill="none">
                        <path d="M14 19L21 12L14 5" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></path>
                        <path d="M21 12H2" stroke="currentColor" stroke-miterlimit="10" stroke-width="2"></path>
                      </svg></div>
                  </div>
                  <div class="flickity-dots">
                    <div class="flickity-dots-list">
                      <div data-flickity-dot="active" class="flickity-dot"></div>
                      <div data-flickity-dot="" class="flickity-dot"></div>
                      <div data-flickity-dot="" class="flickity-dot"></div>
                      <div data-flickity-dot="" class="flickity-dot"></div>
                      <div data-flickity-dot="" class="flickity-dot"></div>
                      <div data-flickity-dot="" class="flickity-dot"></div>
                      <div data-flickity-dot="" class="flickity-dot"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content7_component"></div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_content7">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-small padding-bottom">
              <div data-w-id="f47b56f8-ba11-b797-f7e5-ec6f07a3dee9" style="-webkit-transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 50px, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="content7_component">
                <div class="max-width-medium">
                  <div class="content7_content-wrapper">
                    <div class="margin-bottom margin-small">
                      <h2 id="w-node-f47b56f8-ba11-b797-f7e5-ec6f07a3deed-9c6bfe73" class="heading-style-h2 text-weight-medium">Einblick</h2>
                    </div>
                    <div class="text-rich-text w-richtext">
                      <p class="text-size-large hyphen">Anschluss und Integration der neuen Zimmer an die bestehende Haustechnikinfrastruktur und Gebäudesteuerung. Umsetzung der baulichen Massnahmen während laufendem Betrieb.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <header class="project-full-width-img">
        <div class="full-width-img-p">
          <div class="header146_image-wrapper">
            <div data-delay="3500" data-animation="fade" class="slider w-slider" data-autoplay="true" data-easing="ease-out-quart" data-hide-arrows="true" data-disable-swipe="true" data-autoplay-limit="0" data-nav-spacing="3" data-duration="500" data-infinite="true">
              <div class="w-slider-mask">
                <div class="w-slide"><img loading="lazy" src="../images/dd86769741d8f-1.avif" alt="" class="header146_image"></div>
                <div class="w-slide"><img loading="lazy" src="../images/febebab0c7492.avif" alt="" class="header146_image"></div>
                <div class="w-slide"><img loading="lazy" src="../images/ffbc2e748158c.avif" alt="" class="header146_image"></div>
              </div>
              <div class="left-arrow w-slider-arrow-left">
                <div class="w-icon-slider-left"></div>
              </div>
              <div class="right-arrow w-slider-arrow-right">
                <div class="w-icon-slider-right"></div>
              </div>
              <div class="slide-nav w-slider-nav"></div>
            </div>
          </div>
        </div>
      </header>
      <section class="footer">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large footer-padding">
              <div class="layout367_component">
                <div class="w-layout-grid layout367_grid-list">
                  <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b19-398b1b13" class="w-layout-grid layout367_row">
                    <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b1a-398b1b13" class="layout367_card-large">
                      <div class="layout367_card-small-content footer-top-margin">
                        <div class="footer-column-content-top">
                          <div class="layout367_card-small-content-top">
                            <div class="div-block-10">
                              <h3 class="heading-style-h2 text-weight-medium">Engagement für  Bauprojekte.</h3>
                            </div>
                          </div>
                          <div class="layout367_card-small-content-top small-content-contact">
                            <a href="mailto:<EMAIL>" class="footer-email w-inline-block"><img src="../images/footer-mail-icon.svg" loading="lazy" alt="" class="footer-contact-icon hide">
                              <p class="footer-contact-text"><EMAIL></p>
                            </a>
                            <a href="tel:+41445352222" class="footer-phone w-inline-block"><img src="../images/footer-phone-icon.svg" loading="lazy" alt="" class="footer-contact-icon hide">
                              <p class="footer-contact-text">+41 44 535 22 22</p>
                            </a>
                          </div>
                        </div>
                        <div class="footer-logos-desktop">
                          <p class="footer-contact-text">Mitgliedschaften</p>
                          <div class="layout367_card-small-content-top footer-logo-grid">
                            <a aria-label="Maneco Logo" href="https://maneco.pro/" target="_blank" class="footer-logo w-inline-block"><img src="../images/maneco-logo.svg" loading="lazy" alt="Maneco Logo" class="footer-logo-img"></a>
                            <a aria-label="Future Areas Logo" href="https://www.future-areas.swiss/" target="_blank" class="footer-logo w-inline-block"><img src="../images/future-areas-logo.svg" loading="lazy" alt="Future Areas Logo" class="footer-logo-img"></a>
                            <a aria-label="OBS OBD Logo" href="https://www.obs-osd.ch/" target="_blank" class="footer-logo w-inline-block"><img src="../images/logo_obs.svg" loading="lazy" alt="OBS OSD Logo" class="footer-logo-img"></a>
                            <a aria-label="Swiss Leaders Logo" href="https://swissleaders.ch/" target="_blank" class="footer-logo w-inline-block"><img src="../images/swiss-leaders-logo.svg" loading="lazy" alt="Swiss Leaders Logo" class="footer-logo-img"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b2d-398b1b13" class="layout367_card-large">
                      <div class="layout367_card-large-content">
                        <div class="layout367_card-large-content-top">
                          <div class="margin-bottom margin-xxsmall hide">
                            <div class="tag is-text uppercase text-color-black">Menu</div>
                          </div>
                          <div class="footer13_link-list">
                            <a href="../team.html" class="footer_link">Team</a>
                            <a href="../leistungen.html" class="footer_link">Leistungen</a>
                            <a href="../marken.html" class="footer_link">Marken</a>
                            <a href="../projekte.html" class="footer_link">Projekte</a>
                            <a href="../karriere.html" class="footer_link">Karriere</a>
                            <a href="../kontakt.html" class="footer_link">Kontakt</a>
                          </div>
                        </div>
                        <div class="layout367_card-large-content-top">
                          <div class="margin-bottom margin-xxsmall hide">
                            <div class="tag is-text uppercase text-color-black">Socials</div>
                          </div>
                          <div class="footer13_link-list social-link-list">
                            <a href="https://www.instagram.com/woelfli_bauplanung" target="_blank" class="footer-link-icon w-inline-block"><img src="../images/instagram.svg" loading="lazy" alt="" class="footer-icon">
                              <div class="footer_link footer-social-link">Instagram</div>
                            </a>
                            <a href="https://www.linkedin.com/company/woelfli-bauplanung-gmbh/" target="_blank" class="footer-link-icon w-inline-block"><img src="../images/linkedin.svg" loading="lazy" alt="" class="footer-icon">
                              <div class="footer_link footer-social-link">LinkedIn</div>
                            </a>
                          </div>
                        </div>
                      </div>
                      <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b49-398b1b13" class="layout367_card-large-footer desktop-large-footer">
                        <div class="wolf-column-mobile">
                          <a href="../index.html" class="footer-logo-link w-inline-block">
                            <div class="logo-wolf-column"><img src="../images/woelfli-logo-sw.svg" loading="lazy" alt="Wöfli Logo "></div>
                          </a>
                          <div class="wolf-symbol-footer wolf-mobile-2">
                            <a href="../identitaet.html" class="footer-icon-link w-inline-block"><img src="../images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                          </div>
                        </div>
                        <div class="footer-column">
                          <div class="policy-column policy-mobile">
                            <a href="../impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="../datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                          </div>
                          <div class="policy-column policy-desktop">
                            <a href="../impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="../datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                          </div>
                          <div class="copyright-column copyright-desktop">
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                        </div>
                        <div class="wolf-symbol-footer wolf-desktop">
                          <a href="../identitaet.html" class="footer-icon-link w-inline-block"><img src="../images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                        </div>
                        <div class="copyright-column copyright-mobile">
                          <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                        </div>
                      </div>
                      <div class="layout367_card-large-footer mobile-large-footer">
                        <div class="woelfli-logo-column">
                          <div class="wolf-column-mobile">
                            <a href="../index.html" class="footer-logo-link w-inline-block">
                              <div class="logo-wolf-column"><img src="../images/woelfli-logo-sw.svg" loading="lazy" alt="Wöfli Logo "></div>
                            </a>
                            <div class="wolf-symbol-footer wolf-mobile-2">
                              <a href="../identitaet.html" class="footer-icon-link w-inline-block"><img src="../images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                            </div>
                          </div>
                          <div class="wolf-symbol-footer wolf-desktop">
                            <a href="../identitaet.html" class="footer-icon-link w-inline-block"><img src="../images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                          </div>
                        </div>
                        <div class="footer-column footer-column-mobile">
                          <div class="policy-column policy-mobile">
                            <a href="../impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="../datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                          <div class="copyright-column copyright-mobile"></div>
                          <div class="policy-column policy-desktop">
                            <a href="../impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="../datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                          </div>
                          <div class="copyright-column copyright-desktop">
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layout367_card-large card-mobile">
                      <div class="layout367_card-small-content footer-top-margin">
                        <div class="footer-logos-mobile">
                          <p class="footer-contact-text">Mitgliedschaften</p>
                          <div class="layout367_card-small-content-top footer-logo-grid">
                            <a href="https://maneco.pro/" target="_blank" class="footer-logo w-inline-block"><img src="../images/maneco-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="https://www.future-areas.swiss/" target="_blank" class="footer-logo w-inline-block"><img src="../images/future-areas-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="#" target="_blank" class="footer-logo w-inline-block"><img src="../images/logo_obs.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="https://swissleaders.ch/" target="_blank" class="footer-logo w-inline-block"><img src="../images/swiss-leaders-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
    <div class="cw-cookies">
      <div id="cw-cookie-banner" class="cw-cookie_banner">
        <div class="cw-cookie_content">
          <h3 class="heading-style-h3">Cookie Einstellungen</h3>
          <p class="text-size-regular">Wir verwenden Cookies, um Ihnen die bestmögliche Nutzung unserer Website zu ermöglichen. Diese helfen uns dabei, die Website zu analysieren und zu verbessern sowie Ihnen ein optimales Nutzungserlebnis zu bieten.</p>
          <a href="../datenschutzerklaerung.html" class="text-size-medium text-color-black">Datenschutzerklärung ansehen</a>
          <div class="cw-cookie_buttons"><button id="cw-btn-reject-all" class="cw-button_secondary">
              <div class="text-size-regular">Alle ablehnen</div>
            </button><button data-w-id="b7421a8e-479b-8458-8a2b-6c4cb088a137" id="cw-btn-options" class="cw-button_secondary">
              <div class="text-size-regular">Auswählen</div>
            </button><button id="cw-btn-accept-all" class="cw-button_primary">
              <div class="text-size-regular">Alle akzeptieren</div>
            </button></div>
          <div id="cw-cookie-options" class="cw-cookie_selection">
            <div class="w-form">
              <form id="email-form" name="email-form" data-name="Email Form" method="get" class="cw-cookie_options" data-wf-page-id="68595fd408a505cd9c6bfe73" data-wf-element-id="b7421a8e-479b-8458-8a2b-6c4cb088a13f"><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Notwendige-Cookies-2" id="consent-necessary" data-name="Notwendige Cookies 2" style="opacity:0;position:absolute;z-index:-1" checked=""><span id="consent-necessary" class="form_checkbox-label text-size-regular w-form-label" for="Notwendige-Cookies-2">Notwendige Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Analyse-Cookies" id="consent-analytics" data-name="Analyse Cookies" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Analyse-Cookies">Analyse Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Marketing" id="consent-ad-marketing" data-name="Marketing" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Marketing">Marketing-Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Nutzerdaten" id="consent-ad-user" data-name="Nutzerdaten" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Nutzerdaten">Nutzerdaten für Werbung</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Personalisierte-Werbung" id="consent-ad-personalization" data-name="Personalisierte Werbung" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Personalisierte-Werbung">Personalisierte Werbung</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Speichern-von-Informationen" id="consent-personalization" data-name="Speichern von Informationen" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="text-size-regular w-form-label" for="Speichern-von-Informationen">Speichern der Informationen für Zugriff oder auf Endgeräten</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Sicherheits-Cookies" id="consent-security" data-name="Sicherheits-Cookies" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Sicherheits-Cookies">Sicherheits-Cookies</span>
                </label></form>
              <div class="w-form-done">
                <div>Thank you! Your submission has been received!</div>
              </div>
              <div class="w-form-fail">
                <div>Oops! Something went wrong while submitting the form.</div>
              </div>
            </div><button id="cw-btn-accept-some" class="cw-button_secondary">
              <div class="text-size-regular">Auswahl übernehmen</div>
            </button>
          </div>
        </div>
      </div>
      <div class="cw-cookie_script w-embed w-script">
        <script>
// Immediate execution - prevent layout shift
(function() {
    const consentModeString = localStorage.getItem('consentMode');
    if (consentModeString) {
        document.write('<style>#cw-cookie-banner { display: none; } #cw-cookie-icon { display: flex; }</style>');
        // Apply consent to scripts immediately
        try {
            const consentMode = JSON.parse(consentModeString);
            setTimeout(function() {
                document.querySelectorAll('script[type="text/plain"][data-consent]').forEach(script => {
                    const consentCategory = script.getAttribute('data-consent');
                    const consentStatus = consentMode[consentCategory] || 'denied';
                    if (consentStatus === 'granted') {
                        const newScript = document.createElement('script');
                        Array.from(script.attributes).forEach(attr => {
                            if (attr.name !== 'data-consent' && attr.name !== 'type') {
                                newScript.setAttribute(attr.name, attr.value);
                            }
                        });
                        newScript.type = 'text/javascript';
                        if (script.innerHTML) newScript.innerHTML = script.innerHTML;
                        script.parentNode.insertBefore(newScript, script.nextSibling);
                    }
                });
            }, 100);
        } catch (e) {
            console.error("Error applying consent:", e);
        }
    }
})();
// FIXED: Find ALL cookie links, not just the first one with the ID
function setupAllCookieLinks() {
    // Find all links that contain "Cookies" text (case insensitive)
    const allCookieLinks = Array.from(document.querySelectorAll('a')).filter(link => 
        link.textContent.toLowerCase().includes('cookie') && 
        link.href && 
        link.href.includes('#')
    );
    console.log(`Found ${allCookieLinks.length} cookie links`);
    allCookieLinks.forEach((link, index) => {
        if (!link.hasAttribute('data-cookie-listener-added')) {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log(`Cookie link ${index + 1} clicked`);
                // Force show banner
                const banner = document.getElementById('cw-cookie-banner');
                if (banner) {
                    banner.style.display = 'block';
                    console.log('Banner shown');
                }
                // Force hide all cookie icons
                document.querySelectorAll('#cw-cookie-icon').forEach(icon => {
                    icon.style.display = 'none';
                });
                // Reset checkboxes and options
                setTimeout(function() {
                    if (window.setConsentCheckboxes) {
                        window.setConsentCheckboxes();
                    }
                    if (window.hideOptions) {
                        window.hideOptions();
                    }
                }, 10);
                return false;
            });
            link.setAttribute('data-cookie-listener-added', 'true');
            console.log(`Attached listener to cookie link ${index + 1}: "${link.textContent.trim()}"`);
        }
    });
}
// Try multiple times to catch all cookie links
setupAllCookieLinks();
setTimeout(setupAllCookieLinks, 100);
setTimeout(setupAllCookieLinks, 500);
setTimeout(setupAllCookieLinks, 1000);
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupAllCookieLinks);
} else {
    setupAllCookieLinks();
}
// Show banner if no consent
if (localStorage.getItem('consentMode') === null) {
    setTimeout(function() {
        const banner = document.getElementById('cw-cookie-banner');
        if (banner) banner.style.display = 'block';
    }, 10);
}
// Main script logic
document.addEventListener('DOMContentLoaded', function() {
    const banner = document.getElementById('cw-cookie-banner');
    if (!banner) return;
    const consentMapping = {
        'functionality_storage': 'consent-necessary',
        'ad_storage': 'consent-ad-marketing', 
        'analytics_storage': 'consent-analytics',
        'ad_user_data': 'consent-ad-user',
        'ad_personalization': 'consent-ad-personalization',
        'personalization_storage': 'consent-personalization',
        'security_storage': 'consent-security',
    };
    function uncheckAllConsentCheckboxes() {
        ['consent-analytics', 'consent-ad-personalization', 'consent-ad-marketing', 'consent-ad-user', 'consent-personalization', 'consent-security'].forEach(checkboxId => {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = false;
                const checkboxDiv = checkbox.previousElementSibling;
                if (checkboxDiv && checkboxDiv.classList.contains('w--redirected-checked')) {
                    checkboxDiv.classList.remove('w--redirected-checked');
                }
            }
        });
    }
    function setConsentCheckboxes() {
        uncheckAllConsentCheckboxes();
        const consentModeString = localStorage.getItem('consentMode');
        if (consentModeString) {
            const consentMode = JSON.parse(consentModeString);
            Object.entries(consentMapping).forEach(([storageKey, checkboxId]) => {
                const checkbox = document.getElementById(checkboxId);
                if (checkbox) {
                    const isChecked = consentMode[storageKey] === 'granted';
                    checkbox.checked = isChecked;
                    const checkboxDiv = checkbox.previousElementSibling;
                    if (checkboxDiv) {
                        if (isChecked) {
                            checkboxDiv.classList.add('w--redirected-checked');
                        } else {
                            checkboxDiv.classList.remove('w--redirected-checked');
                        }
                    }
                }
            });
        }
    }
    function hideOptions() {
        const options = document.getElementById('cw-cookie-options');
        if (options) options.style.height = '0px';
    }
    function hideBanner() {
        banner.style.display = 'none';
        // Show all cookie icons
        document.querySelectorAll('#cw-cookie-icon').forEach(icon => {
            icon.style.display = 'flex';
        });
    }
    function setConsent(consent) {
        const consentMode = {
            'functionality_storage': consent.necessary ? 'granted' : 'denied',
            'ad_user_data': consent.aduser ? 'granted' : 'denied',
            'ad_storage': consent.admarketing ? 'granted' : 'denied',
            'analytics_storage': consent.analytics ? 'granted' : 'denied',
            'ad_personalization': consent.adpersonalized ? 'granted' : 'denied',
            'personalization_storage': consent.personalized ? 'granted' : 'denied',
            'security_storage': consent.security ? 'granted' : 'denied',
        };
        localStorage.setItem('consentMode', JSON.stringify(consentMode));
        hideBanner();
    }
    // Make functions global for the cookie link handlers
    window.setConsentCheckboxes = setConsentCheckboxes;
    window.hideOptions = hideOptions;
    // Button event listeners
    const acceptAll = document.getElementById('cw-btn-accept-all');
    if (acceptAll) {
        acceptAll.addEventListener('click', function() {
            setConsent({
                necessary: true, analytics: true, adpersonalized: true, admarketing: true,
                aduser: true, personalized: true, security: true,
            });
        });
    }
    const rejectAll = document.getElementById('cw-btn-reject-all');
    if (rejectAll) {
        rejectAll.addEventListener('click', function() {
            setConsent({
                necessary: true, analytics: false, adpersonalized: false, admarketing: false,
                aduser: false, personalized: false, security: false
            });
        });
    }
    const acceptSome = document.getElementById('cw-btn-accept-some');
    if (acceptSome) {
        acceptSome.addEventListener('click', function() {
            setConsent({
                necessary: true,
                analytics: document.getElementById('consent-analytics')?.checked || false,
                adpersonalized: document.getElementById('consent-ad-personalization')?.checked || false,
                admarketing: document.getElementById('consent-ad-marketing')?.checked || false,
                aduser: document.getElementById('consent-ad-user')?.checked || false,
                personalized: document.getElementById('consent-personalization')?.checked || false,
                security: document.getElementById('consent-security')?.checked || false,
            });
        });
    }
    const optionsBtn = document.getElementById('cw-btn-options');
    if (optionsBtn) {
        optionsBtn.addEventListener('click', function() {
            const options = document.getElementById('cw-cookie-options');
            if (options) {
                if (options.style.height === '0px' || options.style.height === '') {
                    options.style.height = options.scrollHeight + 'px';
                } else {
                    options.style.height = '0px';
                }
            }
        });
    }
    // Initialize
    setConsentCheckboxes();
    // Set up cookie links one more time
    setTimeout(setupAllCookieLinks, 100);
});
</script>
      </div>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=68257cde3c60ae59e717c715" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="../js/webflow.js" type="text/javascript"></script>
  <script src="https://instant.page/5.2.0" type="module" integrity="sha384-jnZyxPjiipYXnSU0ygqeac2q7CVYMbh84q0uHVRRxEtvFPiQYbXWUorga2aqZJ0z"></script>
  <script>
//Navbar Scroll Script
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar2_component');
    const navLinks = document.querySelectorAll('.navbar2_link');
    const navButton = document.querySelector('.button.is-navbar2-button');
    const logoSvgPaths = document.querySelectorAll('.navbar2_component .w-embed svg path');
    const menuLines = document.querySelectorAll('.button_dialog-menu-line');
    let scrollThreshold = 50; // Adjust this value to change when the animation triggers
    function updateNavbar() {
        if (window.scrollY > scrollThreshold) {
            // Scrolled state
            navbar.style.position = 'fixed';
            navbar.style.top = '0px';
            navbar.style.left = '0px';
            navbar.style.width = '100%';
            navbar.style.zIndex = '1000'; // Ensure it's on top
            navbar.style.backgroundColor = 'white';
            // Update nav links color
            navLinks.forEach(link => {
                link.style.color = '#000000'; // Dark grey color
            });
            // Update button background
            if (navButton) {
                navButton.style.backgroundColor = '#f5f5f5'; // Light grey background
            }
            // Update SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = '#000000';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = '#000000';
                }
            });
            // Update hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = '#000000';
            });
        } else {
            // Reset to original state
            navbar.style.position = ''; // Revert to original position
            navbar.style.top = '';
            navbar.style.left = '';
            navbar.style.width = '';
            navbar.style.zIndex = '';
            navbar.style.backgroundColor = 'transparent';
            // Reset nav links color
            navLinks.forEach(link => {
                link.style.color = ''; // Reset to original color
            });
            // Reset button background
            if (navButton) {
                navButton.style.backgroundColor = ''; // Reset to original background
            }
            // Reset SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = 'white';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = 'white';
                }
            });
            // Reset hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = 'white';
            });
        }
    }
    // Add smooth transition styles
    navbar.style.transition = 'all 0.3s ease';
    navLinks.forEach(link => {
        link.style.transition = 'color 0.3s ease';
    });
    if (navButton) {
        navButton.style.transition = 'background-color 0.3s ease';
    }
    logoSvgPaths.forEach(path => {
        path.style.transition = 'fill 0.3s ease, stroke 0.3s ease';
    });
    menuLines.forEach(line => {
        line.style.transition = 'color 0.3s ease';
    });
    // Initial check
    updateNavbar();
    // Add scroll event listener
    window.addEventListener('scroll', updateNavbar);
});
</script>
  <script>
//Navbar Scroll Script
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar2_component');
    const navLinks = document.querySelectorAll('.navbar2_link');
    const navButton = document.querySelector('.button.is-navbar2-button');
    const logoSvgPaths = document.querySelectorAll('.navbar2_component .w-embed svg path');
    let scrollThreshold = 50; // Adjust this value to change when the animation triggers
    function updateNavbar() {
        if (window.scrollY > scrollThreshold) {
            // Scrolled state
            navbar.style.backgroundColor = 'white';
            // Update nav links color
            navLinks.forEach(link => {
                link.style.color = '#333333'; // Dark grey color
            });
            // Update button background
            if (navButton) {
                navButton.style.backgroundColor = '#f5f5f5'; // Light grey background
            }
            // Update SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = '#333333';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = '#333333';
                }
            });
        } else {
            // Reset to original state
            navbar.style.backgroundColor = 'transparent';
            // Reset nav links color
            navLinks.forEach(link => {
                link.style.color = ''; // Reset to original color
            });
            // Reset button background
            if (navButton) {
                navButton.style.backgroundColor = ''; // Reset to original background
            }
            // Reset SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = 'white';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = 'white';
                }
            });
        }
    }
    // Add smooth transition styles
    navbar.style.transition = 'all 0.3s ease';
    navLinks.forEach(link => {
        link.style.transition = 'color 0.3s ease';
    });
    if (navButton) {
        navButton.style.transition = 'background-color 0.3s ease';
    }
    logoSvgPaths.forEach(path => {
        path.style.transition = 'fill 0.3s ease, stroke 0.3s ease';
    });
    // Initial check
    updateNavbar();
    // Add scroll event listener
    window.addEventListener('scroll', updateNavbar);
});
</script>
  <script>
   function initFlickitySlider() {
  // Select all slider groups with the specified data attribute
  const sliderCards = document.querySelectorAll('[data-flickity-type="cards"]');
  sliderCards.forEach((slider, index) => {
    // Give each slider a unique ID
    const sliderIndexID = 'flickity-type-cards-id-' + index;
    slider.id = sliderIndexID; 
    // Count slides
    let slidesCount = slider.querySelectorAll('[data-flickity-item]').length;
    slider.setAttribute('data-flickity-count', slidesCount);
    // Set Active status
    slider.setAttribute('data-flickity-status', 'active');
    // Select the element containing the slide list
    const sliderEl = document.querySelector('#' + sliderIndexID + ' [data-flickity-list]');
    if (!sliderEl) return;
    try {
      // Initialize Flickity on the slider element
      const flickitySlider = new Flickity(sliderEl, {
        watchCSS: true,
        contain: true,
        wrapAround: false,
        dragThreshold: 10,
        prevNextButtons: false,
        pageDots: false,
        cellAlign: 'left',
        selectedAttraction: 0.015,
        friction: 0.25,
        percentPosition: true,
        freeScroll: false,
        on: {
          dragStart: () => {
            // Disable pointer events during drag
            sliderEl.style.pointerEvents = "none";
          },
          dragEnd: () => {
            // Re-enable pointer events after drag
            sliderEl.style.pointerEvents = "auto";
          },
          change: function () {
            updateArrows();
            updateDots();
          }
        }
      });
      // Get Flickity instance data
      const flickity = Flickity.data(sliderEl);
      if (!flickity) {
        console.error('Flickity instance not found');
        return;
      }
      // Set up previous click functionality
      const prevButton = slider.querySelector('[data-flickity-control="prev"]');
      if (prevButton) {
        prevButton.setAttribute('disabled', '');
        prevButton.addEventListener('click', function () {
          flickity.previous();
        });
      }
      // Set up next click functionality
      const nextButton = slider.querySelector('[data-flickity-control="next"]');
      if (nextButton) {
        nextButton.addEventListener('click', function () {
          flickity.next();
        });
      }
      // Update arrows using CSS var(--flick-col) count
      function updateArrows() {
        if (!flickity) return; // Ensure Flickity instance exists
        // Enable/disable previous button
        if (prevButton) {
            if (flickity.isFirst) { // True when the carousel is at the very first slide position
                prevButton.setAttribute('disabled', 'disabled');
            } else {
                prevButton.removeAttribute('disabled');
            }
        }
        // Enable/disable next button
        if (nextButton) {
            if (flickity.isLast) { // True when the carousel is at the very last slide position
                nextButton.setAttribute('disabled', 'disabled');
            } else {
                nextButton.removeAttribute('disabled');
            }
        }
    }
      // Set up dots click functionality
      const dots = slider.querySelectorAll('[data-flickity-dot]');
      if (dots.length) {
        dots.forEach((dot, index) => {
          dot.addEventListener('click', function () {
            if (!flickity || !flickity.cells) return;
            // Get computed style with fallback
            let inviewColumns = 3; // Default fallback
            try {
              const computedStyle = window.getComputedStyle(sliderEl);
              const flickColValue = computedStyle.getPropertyValue('--flick-col').trim();
              if (flickColValue) {
                inviewColumns = parseFloat(flickColValue);
              }
            } catch (e) {
              console.warn('Error getting CSS variable:', e);
            }
            const maxIndex = flickity.cells.length - inviewColumns;
            let targetIndex = index;
            if (targetIndex > maxIndex) {
              targetIndex = maxIndex;
            }
            flickity.select(targetIndex);
          });
        });
      }
      // Update dots using CSS var(--flick-col) count
      function updateDots() {
        if (!flickity || !flickity.cells) return;
        // Get computed style with fallback
        let inviewColumns = 3; // Default fallback
        try {
          const computedStyle = window.getComputedStyle(sliderEl);
          const flickColValue = computedStyle.getPropertyValue('--flick-col').trim();
          if (flickColValue) {
            inviewColumns = parseFloat(flickColValue);
          }
        } catch (e) {
          console.warn('Error getting CSS variable:', e);
        }
        const maxIndex = flickity.cells.length - inviewColumns;
        const activeIndex = flickity.selectedIndex < maxIndex ? flickity.selectedIndex : maxIndex;
        const dots = slider.querySelectorAll('[data-flickity-dot]');
        dots.forEach((dot, index) => {
          dot.setAttribute('data-flickity-dot', index === activeIndex ? 'active' : '');
        });
      }
      // Initial update
      updateArrows();
      updateDots();
    } catch (error) {
      console.error('Error initializing Flickity:', error);
    }
  });
}
// Initialize Flickity Slider
document.addEventListener('DOMContentLoaded', function() {
  initFlickitySlider();
});
  </script>
</body>
</html>