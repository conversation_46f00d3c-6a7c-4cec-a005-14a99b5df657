<!DOCTYPE html><!--  Last Published: Mon Aug 11 2025 09:01:09 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="6863cffb7ce5d82a182d6528" data-wf-site="68257cde3c60ae59e717c715" lang="de">
<head>
  <meta charset="utf-8">
  <title>Karriere bei Wölfli Bauplanung | Jetzt Teil des Teams werden</title>
  <meta content="Werde Teil eines engagierten Teams und plane mit uns die Bauprojekte der Zukunft. Bei Wölfli erwarten dich Verantwortung, Entwicklung und Zusammenhalt." name="description">
  <meta content="Karriere bei Wölfli Bauplanung | Jetzt Teil des Teams werden" property="og:title">
  <meta content="Werde Teil eines engagierten Teams und plane mit uns die Bauprojekte der Zukunft. Bei Wölfli erwarten dich Verantwortung, Entwicklung und Zusammenhalt." property="og:description">
  <meta content="Karriere bei Wölfli Bauplanung | Jetzt Teil des Teams werden" property="twitter:title">
  <meta content="Werde Teil eines engagierten Teams und plane mit uns die Bauprojekte der Zukunft. Bei Wölfli erwarten dich Verantwortung, Entwicklung und Zusammenhalt." property="twitter:description">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/woelfli-staging-de009659c94824f4bd1e533.webflow.css" rel="stylesheet" type="text/css">
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js" as="script">
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/animation/beautified_scroll_animations_noheader.js?=v7" defer=""></script>
  <script defer="" src="https://cloud.umami.is/script.js" data-website-id="634ce2ab-8a79-4bd9-b693-703b47938caf"></script>
  <style>
body {
  overscroll-behavior-y: contain;
}
  .image-3 {
  content-visibility: auto;
  contain: layout style paint;
}
.text-size-regular {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
  .text-size-regular {
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
  -o-hyphens: auto;
  hyphens: auto;
}
  .no-hyphen {
  word-break: normal;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  -o-hyphens: none;
  hyphens: none;
}
  .text-size-medium{
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto ;
  -ms-hyphens: auto ;
  -o-hyphens: auto ;
  hyphens: auto ;
}
/*.heading-style-h1 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h2 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h3 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}*/
.heading-style-h4 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h5 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
</style>
  <style>
/* Custom styles for Mapbox project cards */
/* Project Type Tag */
.project-type-tag {
  display: inline-block;
  background-color: #f0f0f0;
  color: #333;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
  font-weight: 500;
}
/* Project Type in Popup */
.project-type {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}
/* Activity Title */
.card_activity-title {
  font-weight: 600;
  font-size: 16px;
  margin-top: 12px;
  margin-bottom: 8px;
}
/* Activity Content */
.card_activity {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}
.header_background-video {
  background-attachment: scroll, scroll; !important;
}
/*style map popups*/
.mapboxgl-popup-content {
	pointer-events: auto;
  border-radius: 4px;
  box-shadow: none;
  padding: 12px 16px;
  color: #161616;
  background-color: #fff;
}
/*popup bottom arrow color*/
.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
    border-top-color: #fefae0;
}
body:has( [data-cursor]:hover ) .cursor{ opacity: 1; }
.button:hover .button-bg{
	transform: scale(0.95);
}
.reveal-type {
  will-change: opacity;
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
}
</style>
  <style>
      .mapboxgl-popup {
        z-index: 1;
      }
      .mapboxgl-popup-content {
        z-index: 1;
      }
      .mapboxgl-popup-close-button {
        z-index: 12;
        padding: 8px !important; /* Larger touch target */
        font-size: 20px !important; /* Larger X */
        right: 0 !important;
        top: 0 !important;
        color: #000 !important;
        background: rgba(255, 255, 255, 0.8) !important;
        border-radius: 0 3px 0 3px !important;
      }
      @media (max-width: 767px) {
        .mapboxgl-popup-close-button {
          padding: 12px !important;
          font-size: 24px !important;
        }
.mapboxgl-popup { 
max-width:5rem;
}
      }
  .handwriting-svg path {
    stroke-dasharray: 0;
    stroke-dashoffset: 0;
  }
    </style>
  <style>
:root {
  --vh: 1vh;
}
</style>
  <script>
function setVh() {
  let vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}
setVh();
window.addEventListener('resize', setVh);
</script>
</head>
<body class="body">
  <div class="menu-main-css u-embed w-embed">
    <style>
:root {
	--font-family-serif: "Overused Grotesk", 
  iowan old style,
  apple garamond,
  baskerville,
  times new roman,
  droid serif,
  times,
  source serif pro,
  serif,
  apple color emoji,
  segoe ui emoji,
  segoe ui symbol;
  --font-family-normal: "Overused Grotesk",
  -apple-system,
  blinkmacsystemfont,
  avenir next,
  avenir,
  segoe ui,
  helvetica neue,
  helvetica,
  cantarell,
  ubuntu,
  roboto,
  noto,
  arial,
  sans-serif;
  --font-weight-regular: 400;
	--font-size-h1: clamp(3rem, 0.578rem + 6.92vw, 7.5rem);
	--font-size-h2: clamp(2.25rem, 0.7695rem + 4.23vw, 5rem);
	--font-size-h3: clamp(1.25rem, 0.8475rem + 1.15vw, 2rem);
	--size-48: clamp(2rem, 1.461rem + 1.54vw, 3rem);
	--size-64: clamp(2.5rem, 1.6915rem + 2.31vw, 4rem);
	--size-80: clamp(3rem, 1.922rem + 3.08vw, 5rem);
	--size-96: clamp(4rem, 2.922rem + 3.08vw, 6rem);
	--size-120: clamp(5rem, 3.6525rem + 3.85vw, 7.5rem);
	--size-144: clamp(6rem, 4.383rem + 4.62vw, 9rem);
  --z-below: -1;
  --z-above: 1;
  --z-dialog: 300;
  --grid-margin: clamp(1rem, 0.1915rem + 2.31vw, 2.5rem);
  --grid-gutter: 1rem;
  --grid-columns: 12;
  --grid-column-width:  calc(((var(--vw, 1vw) * 100) - (2 * var(--grid-margin))) / var(--grid-columns) - (var(--grid-gutter) * (var(--grid-columns) - 1) / var(--grid-columns)));
}
html {
	-webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
  text-size-adjust: none;
  touch-action: manipulation;
  scrollbar-width: thin;
  scrollbar-color: color-mix(in hsl, var(--color), transparent 50%) var(--color-background);
}
::selection {
  background-color: var(--selection-background);
  color: var(--selection-foreground);
  text-shadow: none;
}
*:focus-visible:not(input, textarea, select) {
  outline: var(--color) auto 6px;
  outline-offset: 4px;
}
/* theme */
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
[data-theme="light"] .link {
	color: var(--color);
}
/* webflow */
.wf-design-mode .wf-empty {
  padding: 0;
}
.wf-editor-mode .wf-empty {
  padding: 0;
}
/* utilities */
.u-screen-reader-text {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  margin: 0;
  padding: 0;
  width: 1px;
  height: 1px;
  border: 0;
}
.u-embed {
	position: fixed;
  inset: 0 auto auto 0;
}
.u-embed::before {
	content: none !important;
}
/* icons */
.icon.is-small {
	--icon-width: .75rem;
  --icon-height: .75rem;
}
/* header */
.header_breadcrumb-item:nth-child(1),
.header_breadcrumb-item:nth-child(2) {
	opacity: 60%;
}
.header_breadcrumb-item:nth-child(1)::after {
	content: "//";
  margin-left: var(--size-4);
}
.header_breadcrumb-item:nth-child(2)::after {
	content: "/";
  margin-left: var(--size-4);
}
/* buttons */
.button_cloneable::after,
.button_info::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.25rem;
}
.button_dialog {
	-webkit-tap-highlight-color: transparent;
  transition: transform .15s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog[data-active] {
	transform: translate3d(0, .125rem, 0);
}
.button_dialog[data-active] .button_dialog-bg {
	background-color: color-mix(in hsl, var(--color-accent), transparent 25%);
}
.button_dialog[data-active] .button_dialog-bg::after {
	background-color: color-mix(in hsl, var(--color), transparent 25%);
}
.button_cloneable-bg {
	transition: box-shadow .3s cubic-bezier(.62, .08, 0, 1);
}
.button_info .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-bg {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1), background-color .15s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-menu-line-wrap {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-bg::before {
  content: "";
  display: block;
  position: absolute;
  background-color: var(--color-accent-orange);
  inset: 0;
  width: 100%;
  height: 100%;
  clip-path: inset(100% 0 0 0 round 0 0 .5rem .5rem);
  transition: clip-path .3s cubic-bezier(.36, .08, 0, 1);
  transition-delay: 0.15s;
}
@media screen and (prefers-reduced-motion) {
	.button_dialog-bg::before {
  	content: none;
  }
}
.button_dialog-bg::after {
  content: "";
  display: block;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  clip-path: inset(100% 0 0 0 round 0 0 .5rem .5rem);
  transition: clip-path .45s cubic-bezier(.62, .08, 0, 1), background-color .15s cubic-bezier(.62, .08, 0, 1);
}
@media screen and (prefers-reduced-motion) {
	.button_dialog-bg::after {
  	clip-path: inset(0 0 0 0 round .5rem);
    transition: opacity .3s cubic-bezier(.62, .08, 0, 1);
    opacity: 0;
  }
}
@media (hover: hover) and (pointer: fine) {
  .button_cloneable:hover .button_cloneable-bg,
  .button_cloneable:focus-visible .button_cloneable-bg {
    box-shadow: 0 0 0 .1875rem var(--color);
  }
  .button_info:hover .icon,
  .button_info:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
  .button_dialog:hover .button_dialog-bg,
  .button_dialog:focus-visible .button_dialog-bg {
    transform: scale(1.05) translateZ(0);
  }
  .button_dialog:hover .button_dialog-bg::before,
  .button_dialog:focus-visible .button_dialog-bg::before,
  .button_dialog:hover .button_dialog-bg::after,
  .button_dialog:focus-visible .button_dialog-bg::after {
    clip-path: inset(0 0 0 0 round .5rem);
  }
  .button_dialog:hover .button_dialog-bg::before,
  .button_dialog:focus-visible .button_dialog-bg::before {
    transition-delay: 0s;
  }
  .button_dialog:hover .button_dialog-menu-line-wrap,
  .button_dialog:focus-visible .button_dialog-menu-line-wrap {
  	transform: scaleX(.9) translateZ(0);
  }
  .button_dialog:focus-visible {
    outline-offset: .5rem;
  }
}
@media screen and (prefers-reduced-motion) and (hover: hover) and (pointer: fine) {
	.button_dialog:hover .button_dialog-bg,
  .button_dialog:focus-visible .button_dialog-bg {
    transform: scale(1) translateZ(0);
  }
	.button_dialog:hover .button_dialog-bg::after,
  .button_dialog:focus-visible .button_dialog-bg::after {
    opacity: 1;
  }
}
/* External Links */
.link[target="_blank"]::after {
	content: "";
  display: inline-block;
  background-color: currentColor;
  mask: url("https://cdn.prod.website-files.com/66d5c7d781a3c8330fefd851/66eade6255eed3286f87a03c_icon-external.svg") no-repeat 50% 50%;
	height: .625rem;
  width: .625rem;
  margin-left: .25rem;
}
/* header info dialog */
.header_dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --content-speed: .45s;
  --content-ease: cubic-bezier(.215, .61, .355, 1);
  --content-stagger: .05s;
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .header_dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.header_dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.header_dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.header_dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.header_dialog:not([open]) .header_dialog-inner {
	transform: translate3d(100%, 0, 0);
}
.header_dialog-inner {
	transform: translate3d(0, 0, 0);
	contain: layout style paint;
	transition: transform var(--modal-speed) var(--modal-ease);  
}
.header_dialog-inner:focus-visible {
	outline-offset: -.25rem;
}
/* move wrapper */
@media screen and (prefers-reduced-motion: no-preference) {
  .hero,
  .block-text,
  .footer {
    transition: transform .75s cubic-bezier(.62, .08, 0, 1);  
  }
  .wrapper:has(.header_dialog[open]) .hero,
  .wrapper:has(.header_dialog[open]) .block-text,
  .wrapper:has(.header_dialog[open]) .footer {
    transform: translate3d(-10rem, 0 , 0);
  }
}
@media screen and (prefers-reduced-motion) {
  .header_dialog:not([open]) .header_dialog-inner {
  	transform: translate3d(0, 0, 0);
    opacity: 0;
  }
  .header_dialog-inner {
    transition: opacity var(--modal-speed) var(--modal-ease);  
  }
}
/* initial state of close button */
.header_dialog:not([open]) .header_dialog-button-close {
	transform: translate3d(.75rem, 0, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.header_dialog-button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.header_dialog-button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.header_dialog-button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .header_dialog-button-close:hover .icon,
  .header_dialog-button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.header_dialog:not([open]) .header_dialog-backdrop {
	opacity: 0;
}
.header_dialog-backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
</style>
  </div>
  <div class="css-dialog-styles u-embed w-embed">
    <style>
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
.css-dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --content-speed: .45s;
  --content-ease: cubic-bezier(.215, .61, .355, 1);
  --content-stagger: .05s;
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .css-dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.css-dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.css-dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.css-dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.css-dialog:not([open]) .css-dialog_inner {
	transform: translate3d(0, 100%, 0);
}
.css-dialog_inner {
	contain: content;
	transition: transform var(--modal-speed) var(--modal-ease);  
}
.css-dialog_inner:focus-visible {
	outline-offset: -.25rem;
}
@media screen and (prefers-reduced-motion) {
  .css-dialog:not([open]) .css-dialog_inner {
  	transform: translate3d(0, 0, 0);
    opacity: 0;
  }
  .css-dialog_inner {
    transition: opacity var(--modal-speed) var(--modal-ease);  
  }
}
/* preload content for better performance */
.css-dialog_title,
.css-dialog_paragraph,
.css-dialog_visual-number,
.css-dialog_visual-author,
.css-dialog_visual-img {
  content-visibility: auto;
  contain: layout style paint;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
/* initial state of close button */
.css-dialog:not([open]) .css-dialog_button-close {
	transform: translate3d(0, -.75rem, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.css-dialog_button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.css-dialog_button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.css-dialog_button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .css-dialog_button-close:hover .icon,
  .css-dialog_button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.css-dialog:not([open]) .css-dialog_backdrop {
	opacity: 0;
}
.css-dialog_backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
/* initial state of content when modal is not opened (only when reduce motion is not activated) */
@media screen and (prefers-reduced-motion: no-preference) {
  .css-dialog:not([open]) .css-dialog_title,
  .css-dialog:not([open]) .css-dialog_paragraph.u-h3,
  .css-dialog:not([open]) .css-dialog_visual-img {
    transform: translate3d(0, 2.5rem, 0);
    opacity: 0;
  }
  .css-dialog:not([open]) .css-dialog_paragraph,
  .css-dialog:not([open]) .css-dialog_visual-number,
  .css-dialog:not([open]) .css-dialog_visual-author {
    transform: translate3d(0, 1.25rem, 0);
    opacity: 0;
  }
  .css-dialog_title,
  .css-dialog_paragraph,
  .css-dialog_visual-number,
  .css-dialog_visual-author,
  .css-dialog_visual-img {
    transition: transform var(--content-speed) var(--content-ease), opacity var(--content-speed) var(--content-ease);
    transition-delay: 0s, 0s;
    opacity: 1;
  }
  .css-dialog[open] .css-dialog_title,
  .css-dialog[open] .css-dialog_paragraph,
  .css-dialog[open] .css-dialog_visual-number,
  .css-dialog[open] .css-dialog_visual-author,
  .css-dialog[open] .css-dialog_visual-img {
    transition-delay: calc((var(--dialog-animation-speed) - .2s) + var(--content-stagger) * (var(--index) - 1)), calc((var(--dialog-animation-speed) - .2s) + var(--content-stagger) * (var(--index) - 1));
  }
}
</style>
  </div>
  <div class="css-dialog-javascript u-embed w-embed w-script">
    <script defer="">
// handles scrollbar jump when opening modal and disabling scrolling behind the modal
// normaly scrollbar-gutter: stable; would handle this, but safari doesn't support it at this time
class CssScrollbarHandler {
  constructor(options = {}) {
    this.html = document.documentElement;
    this.scrollbarThreshold = options.scrollbarThreshold || 5;
    this.scrollbarWidth = 0;
    this.calculateScrollbarWidth();
    this.handleScrollbar();
  }
  calculateScrollbarWidth() {
    // calculate scrollbar width
    const documentWidth = this.html.clientWidth;
    this.scrollbarWidth = window.innerWidth - documentWidth;
  }
  handleScrollbar() {
    if (this.scrollbarWidth > this.scrollbarThreshold) {   
      this.html.style.paddingRight = `${this.scrollbarWidth}px`;
    }
  }
  destroy() {
    this.html.style.paddingRight = '';
  }
}
// JavaScript for Dialog open/close
class CssDialog {
	constructor(element, options = {}) {
  	this.toggler = element;
		this.dialog = this.toggler.parentNode.querySelector("[data-css-dialog]");
    this.dialogClose = this.dialog.querySelector("[data-css-dialog-close]");
		this.dialogBackdrop = this.dialog.querySelector("[data-css-dialog-backdrop]");
    this.activeClass = "is-active";
    // get transition-duration from CSS variable --dialog-animation-speed
    this.dialogStyles = getComputedStyle(this.dialog);
    this.transitionDuration = parseFloat(this.dialogStyles.getPropertyValue("--dialog-animation-speed")) * 1000; // convert to miliseconds
    // ScrollbarHandler configuration
    this.scrollbarOptions = {
      scrollbarThreshold: 5,
      ...options  // add to options
    };
    // bind methods
    this.onKeyDown = this.onKeyDown.bind(this);
    this.removeStyles = this.removeStyles.bind(this);
    this.init();
  }
	init() {
  	this.toggler.addEventListener("click", () => this.toggle());
    this.dialogClose.addEventListener("click", () => this.hide());
    this.dialogBackdrop.addEventListener("click", () => this.hide());
    return this;
  }
  // add ESC trigger so this.activeClass will also be removed
  onKeyDown(e) {
    if ("Escape" === e.key) {
      e.preventDefault();
      this.dialogClose.click();
    }
  }
  // toggle between show & hide
  toggle() {
    if (this.dialog.classList.contains(this.activeClass)) {
      this.hide();
    } else {
      this.show();
    }
  }
  // show dialog, add event listener for ESC key, add overflow hidden on body to disable scroll
  show() {
  	document.documentElement.style.overflow = "hidden";
    window.addEventListener("keydown", this.onKeyDown);
    this.scrollbarHandler = new CssScrollbarHandler(this.scrollbarOptions);
    this.dialog.classList.add(this.activeClass);
    this.dialog.showModal();
  }
  // hide dialog, remove event listener for ESC key, remove overflow hidden on body to enable scroll
  hide() {
    window.removeEventListener("keydown", this.onKeyDown);
    this.dialog.classList.remove(this.activeClass);
    this.dialog.close();
    setTimeout(() => {
      this.removeStyles();
      // cleanup ScrollbarHandler
      if (this.scrollbarHandler) {
        this.scrollbarHandler.destroy();
        this.scrollbarHandler = null;
      }
    }, this.transitionDuration);
  }
  // remove styles from html
  removeStyles() {
  	document.documentElement.style.overflow = "";
  }
}
// add to webflow
window.Webflow ||= [];
window.Webflow.push(async () => {	
  const cssDialogElements = document.querySelectorAll("[data-css-dialog-toggle]");
  if (cssDialogElements.length > 0) {
  	cssDialogElements.forEach(element => {
    	new CssDialog(element);
  	});
  }
});
</script>
  </div>
  <div class="menu-dialog-styles u-embed w-embed">
    <style>
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
.menu-dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-close-ease: cubic-bezier(.36, 0, .087, 1);
  --modal-opacity-speed: .45s;
  --modal-opacity-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --nav-speed: .45s;
  --nav-ease: cubic-bezier(.215, .61, .355, 1);
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .menu-dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --modal-opacity-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.menu-dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.menu-dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.menu-dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.menu-dialog:not([open]) .menu-dialog_inner {
  opacity: 0;
  transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
}
.menu-dialog:not([open]) .menu-dialog_footer {
  opacity: 0;
  transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
}
.menu-dialog_inner,
.menu-dialog_footer {
	transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);  
}
.menu-dialog_inner:focus-visible {
	outline-offset: -.25rem;
}
@media screen and (prefers-reduced-motion: no-preference) {
  .menu-dialog:not([open]) .menu-dialog_inner {
    transform: translate3d(0, 8rem, 0) rotate(-3deg);
    transition: transform var(--modal-speed) var(--modal-close-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
  }
  .menu-dialog:not([open]) .menu-dialog_footer {
    transform: translate3d(0, 14rem, 0) rotate(3deg);
    transition: transform var(--modal-speed) var(--modal-close-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
  }
  .menu-dialog_inner,
  .menu-dialog_footer {
    transition: transform var(--modal-speed) var(--modal-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);  
  }
}
/* initial state of close button */
.menu-dialog:not([open]) .menu-dialog_button-close {
	transform: translate3d(0, -.75rem, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.menu-dialog_button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.menu-dialog_button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.menu-dialog_button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .menu-dialog_button-close:hover .icon,
  .menu-dialog_button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.menu-dialog:not([open]) .menu-dialog_backdrop {
	opacity: 0;
}
.menu-dialog_backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
/* hide scrollbar on transform animation */
.menu-dialog[open] .menu-dialog_outer {
	animation: hide-scroll var(--dialog-animation-speed) backwards;
}
@keyframes hide-scroll {
  from, to { overflow: hidden; } 
}
/* menu navigation */
.menu-dialog_nav-link {
	transition: opacity calc(var(--nav-speed) * .5) var(--nav-ease);
}
.menu-dialog_nav-list:has(.menu-dialog_nav-link:hover) .menu-dialog_nav-link:not(:hover) {
	opacity: .5;
}
@media screen and (prefers-reduced-motion: no-preference) {
  .menu-dialog_nav-link {
    transition: padding-left var(--nav-speed) var(--nav-ease), opacity calc(var(--nav-speed) * .5) var(--nav-ease);
  }
  .menu-dialog_nav-link::before {
    content: "";
    display: block;
    position: absolute;
    background-color: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E %3Cpath fill='%23000' fill-rule='evenodd' d='M9 5h2v2h2v2h2v2h-2v2h-2v2H9v-2H7v-2H5V9h2V7h2V5Z' clip-rule='evenodd'/%3E %3C/svg%3E") no-repeat 50% 50%;
    left: calc((var(--icon-height) + var(--size-8)) * -1);
    height: var(--icon-height);
    width: var(--icon-width);
    pointer-events: none;
    transform: translate3d(-.5rem, 0, 0);
    transition: transform calc(var(--nav-speed) * .75) var(--nav-ease), opacity calc(var(--nav-speed) * .5) var(--nav-ease);
    opacity: 0;
  }
  .menu-dialog_nav-link:hover,
  .menu-dialog_nav-link:focus-visible {
    padding-left: var(--size-4);
  }
  .menu-dialog_nav-link:hover::before,
  .menu-dialog_nav-link:focus-visible::before {
    transform: translate3d(.5rem, 0, 0);
    opacity: 1;
  }
}
.button_dialog-bg::after {
background-color:none!important;
position:relative!important;
}
</style>
  </div>
  <div class="menu-dialog-javascript u-embed w-embed w-script">
    <script defer="">
// handles scrollbar jump when opening modal and disabling scrolling behind the modal
// normaly scrollbar-gutter: stable; would handle this, but safari doesn't support it at this time
class MenuScrollbarHandler {
  constructor(options = {}) {
    this.html = document.documentElement;
    this.scrollbarThreshold = options.scrollbarThreshold || 5;
    this.scrollbarWidth = 0;
    this.calculateScrollbarWidth();
    this.handleScrollbar();
  }
  calculateScrollbarWidth() {
    // Berechne die tatsächliche Scrollbar-Breite
    const documentWidth = this.html.clientWidth;
    this.scrollbarWidth = window.innerWidth - documentWidth;
  }
  handleScrollbar() {
    if (this.scrollbarWidth > this.scrollbarThreshold) {   
      this.html.style.paddingRight = `${this.scrollbarWidth}px`;
    }
  }
  destroy() {
    this.html.style.paddingRight = '';
  }
}
// JavaScript for Dialog open/close
class MenuDialog {
	constructor(element, options = {}) {
  	this.toggler = element;
		this.dialog = this.toggler.parentNode.querySelector("[data-menu-dialog]");
    this.dialogClose = this.dialog.querySelector("[data-menu-dialog-close]");
		this.dialogBackdrop = this.dialog.querySelector("[data-menu-dialog-backdrop]");
    this.activeClass = "is-active";
    // Hole die transition-duration aus der CSS Variable
    this.dialogStyles = getComputedStyle(this.dialog);
    this.transitionDuration = parseFloat(this.dialogStyles.getPropertyValue("--dialog-animation-speed")) * 1000; // convert to miliseconds
    // ScrollbarHandler Konfiguration
    this.scrollbarOptions = {
      scrollbarThreshold: 5,
      ...options  // Hier fügen wir die übergebenen Optionen hinzu
    };
    // Bind methods
    this.onKeyDown = this.onKeyDown.bind(this);
    this.removeStyles = this.removeStyles.bind(this);
    this.init();
  }
	init() {
  	this.toggler.addEventListener("click", () => this.toggle());
    this.dialogClose.addEventListener("click", () => this.hide());
    this.dialogBackdrop.addEventListener("click", () => this.hide());
    return this;
  }
  // add ESC trigger so this.activeClass will also be removed
  onKeyDown(e) {
    if ("Escape" === e.key) {
      e.preventDefault();
      this.dialogClose.click();
    }
  }
  // toggle between show & hide
  toggle() {
    if (this.dialog.classList.contains(this.activeClass)) {
      this.hide();
    } else {
      this.show();
    }
  }
  // show dialog, add event listener for ESC key, add overflow hidden on body to disable scroll
  show() {
  	document.documentElement.style.overflow = "hidden";
    console.log("test")
    window.addEventListener("keydown", this.onKeyDown);
    this.scrollbarHandler = new MenuScrollbarHandler(this.scrollbarOptions);
    this.dialog.classList.add(this.activeClass);
    this.dialog.showModal();
  }
  // hide dialog, remove event listener for ESC key, remove overflow hidden on body to enable scroll
  hide() {
    window.removeEventListener("keydown", this.onKeyDown);
    this.dialog.classList.remove(this.activeClass);
    this.dialog.close();
    setTimeout(() => {
      this.removeStyles();
      // Cleanup ScrollbarHandler
      if (this.scrollbarHandler) {
        this.scrollbarHandler.destroy();
        this.scrollbarHandler = null;
      }
    }, this.transitionDuration);
  }
  // remove styles from html
  removeStyles() {
  	document.documentElement.style.overflow = "";
  }
}
window.Webflow ||= [];
window.Webflow.push(async () => {	
  const menuDialogElements = document.querySelectorAll("[data-menu-dialog-toggle]");
  if (menuDialogElements.length > 0) {
  	menuDialogElements.forEach(element => {
    	new MenuDialog(element);
  	});
  }
});
</script>
  </div>
  <div class="page-wrapper">
    <div class="animations-css w-embed">
      <style>
  .hero-header .hero-heading {
    -webkit-animation: fadeIn 1000ms backwards;
    animation: fadeIn 1000ms backwards;
  }
  .hero-header .subheading {
    -webkit-animation: fadeIn 1200ms backwards;
    animation: fadeIn 1200ms backwards;
    -webkit-animation-delay: 400ms;
    animation-delay: 400ms;
  }
  .hero-header .cta-button {
    -webkit-animation: fadeIn 1200ms backwards;
    animation: fadeIn 1200ms backwards;
    -webkit-animation-delay: 800ms;
    animation-delay: 800ms;
  }
  @-webkit-keyframes fadeIn {
    from {
      opacity: 0;
      -webkit-transform: translateY(-10px);
      -ms-transform: translateY(-10px);
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  @keyframes fadeIn {
    from {
      opacity: 0;
      -webkit-transform: translateY(-10px);
      -ms-transform: translateY(-10px);
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  .hero-img-target.visible {
    -webkit-animation: heroRotate 1400ms forwards;
    animation: heroRotate 1400ms forwards;
    -webkit-animation-delay: 400ms;
    animation-delay: 400ms;
  }
  @-webkit-keyframes heroRotate {
    0% {
      -webkit-transform: rotateX(25deg);
      -ms-transform: rotateX(25deg);
      transform: rotateX(25deg);
    }
    25% {
      -webkit-transform: rotateX(25deg) scale(0.9);
      -ms-transform: rotateX(25deg) scale(0.9);
      transform: rotateX(25deg) scale(0.9);
    }
    60%,
    to {
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  @keyframes heroRotate {
    0% {
      -webkit-transform: rotateX(25deg);
      -ms-transform: rotateX(25deg);
      transform: rotateX(25deg);
    }
    25% {
      -webkit-transform: rotateX(25deg) scale(0.9);
      -ms-transform: rotateX(25deg) scale(0.9);
      transform: rotateX(25deg) scale(0.9);
    }
    60%,
    to {
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  .visible .hero-img-blur {
    -webkit-animation: heroBlur 4.1s ease-out forwards;
    animation: heroBlur 4.1s ease-out forwards;
    -webkit-animation-delay: 600ms;
    animation-delay: 600ms;
  }
  @-webkit-keyframes heroBlur {
    0% {
      opacity: 0;
      -webkit-animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
      animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
    }
    10% {
      opacity: 1;
      -webkit-animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
      animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
    }
    100% {
      opacity: 0.2;
    }
  }
  @keyframes heroBlur {
    0% {
      opacity: 0;
      -webkit-animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
      animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
    }
    10% {
      opacity: 1;
      -webkit-animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
      animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
    }
    100% {
      opacity: 0.2;
    }
  }
  .hero-lines path {
    stroke-dasharray: 1;
    stroke-dashoffset: 1;
    stroke: white;
    stroke-opacity: 0.2;
  }
  .visible .hero-lines path {
    -webkit-animation: heroLines 1200ms ease-out forwards;
    animation: heroLines 1200ms ease-out forwards;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
  }
  @-webkit-keyframes heroLines {
    from {
      stroke-dashoffset: 1;
    }
    50% {
      stroke-dashoffset: 0;
    }
    99% {
      stroke-dashoffset: 0;
    }
    100% {
      visibility: hidden;
    }
  } /*!sc*/
  @keyframes heroLines {
    from {
      stroke-dashoffset: 1;
    }
    50% {
      stroke-dashoffset: 0;
    }
    99% {
      stroke-dashoffset: 0;
    }
    100% {
      visibility: hidden;
    }
  }
  .visible .hero-img {
    -webkit-animation: heroImg 400ms forwards;
    animation: heroImg 400ms forwards;
    -webkit-animation-delay: 680ms;
    animation-delay: 680ms;
  }
  @-webkit-keyframes heroImg {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  @keyframes heroImg {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
@media only screen and (min-width: 992px){
  .lightning svg g:last-of-type path {
    fill: transparent;
    -webkit-animation: lightning 2250ms linear infinite;
    animation: lightning 2250ms linear infinite;
    -webkit-animation-delay: calc(var(--index) * 20ms);
    animation-delay: calc(var(--index) * 20ms);
  }
  }
  @-webkit-keyframes lightning {
    0%,
    9%,
    11%,
    100% {
      fill: transparent;
    }
    10% {
      fill: rgba(255, 255, 255, 0.2);
      fill: #fff;
    }
  }
  @keyframes lightning {
    0%,
    9%,
    11%,
    100% {
      fill: transparent;
    }
    10% {
      fill: rgba(255, 255, 255, 0.2);
      fill: #fff;
    }
  }
  .visible .integrations-bg-circle {
    opacity: 1;
    -webkit-animation: integrationsBg 3400ms infinite backwards;
    animation: integrationsBg 3400ms infinite backwards;
    -webkit-animation-delay: calc(500ms + var(--delay, 0s));
    animation-delay: calc(500ms + var(--delay, 0s));
  }
  @-webkit-keyframes integrationsBg {
    from {
      opacity: 0;
      -webkit-transform: translate(-50%, -50%) scale(0.9);
      -ms-transform: translate(-50%, -50%) scale(0.9);
      transform: translate(-50%, -50%) scale(0.9);
    }
    40%,
    50% {
      opacity: var(--opacity);
      -webkit-transform: translate(-50%, -50%) scale(1);
      -ms-transform: translate(-50%, -50%) scale(1);
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes integrationsBg {
    from {
      opacity: 0;
      -webkit-transform: translate(-50%, -50%) scale(0.9);
      -ms-transform: translate(-50%, -50%) scale(0.9);
      transform: translate(-50%, -50%) scale(0.9);
    }
    40%,
    50% {
      opacity: var(--opacity);
      -webkit-transform: translate(-50%, -50%) scale(1);
      -ms-transform: translate(-50%, -50%) scale(1);
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0;
    }
  }</div><div class="global-styles w-embed"><style>
* {
    -webkit-font-smoothing: antialiased; -moz-font-smoothing: antialiased; -o-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
}
.heading-style-h2 {
  font-smooth: always; /* or auto, high, medium, small - but support is inconsistent */
}
:root {
  /* ... existing code ... */
  --selection-background: var(--wölfli-black, #262725);
  --selection-color: var(--wölfli-beige, #f7f3eb);
}
/* Global selection styles */
::selection {
  background-color: var(--selection-background) !important;
  color: var(--selection-color) !important;
  text-shadow: none;
}
::-moz-selection {
  background-color: var(--selection-background) !important;
  color: var(--selection-color) !important;
  text-shadow: none;
}
.text-size-regular {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h3 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h4 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h5 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
/* Set color style to inherit */
.inherit-color * {
    color: inherit;
}
/* Focus state style for keyboard navigation for the focusable elements */
*[tabindex]:focus-visible,
  input[type="file"]:focus-visible {
   outline: 0.125rem solid #4d65ff;
   outline-offset: 0.125rem;
}
/* Get rid of top margin on first element in any rich text element */
.w-richtext > :not(div):first-child, .w-richtext > div:first-child > :first-child {
  margin-top: 0 !important;
}
/* Get rid of bottom margin on last element in any rich text element */
.w-richtext>:last-child, .w-richtext ol li:last-child, .w-richtext ul li:last-child {
	margin-bottom: 0 !important;
}
/* Prevent all click and hover interaction with an element */
.pointer-events-off {
	pointer-events: none;
}
/* Enables all click and hover interaction with an element */
.pointer-events-on {
  pointer-events: auto;
}
/* Create a class of .div-square which maintains a 1:1 dimension of a div */
.div-square::after {
	content: "";
	display: block;
	padding-bottom: 100%;
}
/* Make sure containers never lose their center alignment */
.container-medium,.container-small, .container-large {
	margin-right: auto !important;
  margin-left: auto !important;
}
/* 
Make the following elements inherit typography styles from the parent and not have hardcoded values. 
Important: You will not be able to style for example "All Links" in Designer with this CSS applied.
Uncomment this CSS to use it in the project. Leave this message for future hand-off.
*/
/*
a,
.w-input,
.w-select,
.w-tab-link,
.w-nav-link,
.w-dropdown-btn,
.w-dropdown-toggle,
.w-dropdown-link {
  color: inherit;
  text-decoration: inherit;
  font-size: inherit;
}
*/
/* Apply "..." after 3 lines of text */
.text-style-3lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}
/* Apply "..." after 2 lines of text */
.text-style-2lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
/* Adds inline flex display */
.display-inlineflex {
  display: inline-flex;
}
/* These classes are never overwritten */
.hide {
  display: none !important;
}
@media screen and (max-width: 991px) {
    .hide, .hide-tablet {
        display: none !important;
    }
}
  @media screen and (max-width: 767px) {
    .hide-mobile-landscape{
      display: none !important;
    }
}
  @media screen and (max-width: 479px) {
    .hide-mobile{
      display: none !important;
    }
}
.margin-0 {
  margin: 0rem !important;
}
.padding-0 {
  padding: 0rem !important;
}
.spacing-clean {
padding: 0rem !important;
margin: 0rem !important;
}
.margin-top {
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-top {
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-right {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-right {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-bottom {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-bottom {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
.margin-left {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-left {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-horizontal {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-horizontal {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-vertical {
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-vertical {
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
/* Apply "..." at 100% width */
.truncate-width { 
		width: 100%; 
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 
}
/* Removes native scrollbar */
.no-scrollbar {
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none; 
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
.marken-grid {
  display: grid;
  gap: 2rem;
  width: 100%;
}
/* Apply single column layout only for mobile */
@media screen and (max-width: 479px) {
  .marken-grid-row {
    display: grid;
    grid-template-columns: 1fr !important; /* Force single column */
    gap: 1rem;
    width: 100%;
  }
  .marken-grid-card {
    width: 100%;
    max-width: 100%;
  }
}
/* Optional: Add intermediate breakpoint for tablets */
@media screen and (max-width: 767px) and (min-width: 480px) {
  .marken-grid-row {
    gap: 1.5rem;
  }
}
.heading-style-subtitle {
  font-size: var(--font-size-h5);
  line-height: 1.4;
}
/* Utility classes for heading styles */
.heading-style-regular {
  font-weight: var(--font-weight-regular);
}
.heading-style-medium {
  font-weight: 500;
}
.heading-style-bold {
  font-weight: 700;
}
/* Optional: Add responsive margin adjustments 
@media screen and (max-width: 767px) {
  .heading-style-h1,
  .heading-style-h2,
  .heading-style-h3 {
    margin-top: calc(var(--size-24) * 0.75);
    margin-bottom: calc(var(--size-16) * 0.75);
  }
}*/
.hyphen
 {
  word-break: break-word !important;
  -webkit-hyphens: auto !important;
  -moz-hyphens: auto !important;
  -ms-hyphens: auto !important;
  -o-hyphens: auto !important;
  hyphens: auto !important;
}
.cdn-video::-webkit-media-controls-start-playback-button {
    display: none;
}
  .handwriting-svg path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation-fill-mode: forwards;
    animation-timing-function: ease-in-out;
  }
  .handwriting-svg.animate path {
    animation-name: draw;
    animation-duration: 2s;
  }
  @keyframes draw {
    to {
      stroke-dashoffset: 0;
    }
  }
  /* Add a small delay for each path */
  .handwriting-svg path:nth-child(1) { animation-delay: 0s; }
  .handwriting-svg path:nth-child(2) { animation-delay: 0.1s; }
  .handwriting-svg path:nth-child(3) { animation-delay: 0.2s; }
  /* Continue for all paths */
</style>
      <style>
/* Fluid Typography System for Woelfli Bauplanung */
:root {
  /* Base responsive variables */
  --fluid-min-width: 320;
  --fluid-max-width: 1200;
  --fluid-screen: 100vw;
  --fluid-bp: calc((var(--fluid-screen) - var(--fluid-min-width) / 16 * 1rem) / (var(--fluid-max-width) - var(--fluid-min-width)));
}
/* Heading Styles */
/* H1 - Main Headings */
/* Heading Styles - Adjusted to maintain hierarchy */
/* H1 - Main Headings */
h1, .heading-style-h1 {
  font-size: clamp(26px, calc(26px + 26 * var(--fluid-bp)), 52px);
  line-height: clamp(31px, calc(31px + 26.72 * var(--fluid-bp)), 56.72px);
}
/* H2 - Section Headings */
h2, .heading-style-h2 {
  font-size: clamp(18px, calc(18px + 10 * var(--fluid-bp)), 28px);
  line-height: clamp(25px, calc(25px + 16.4 * var(--fluid-bp)), 48.4px);
}
/* H3 - Sub-Section Headings */
h3, .heading-style-h3 {
  font-size: clamp(20px, calc(20px + 6 * var(--fluid-bp)), 24px);
  line-height: clamp(26px, calc(26px + 5.6 * var(--fluid-bp)), 30.6px);
}
/* H4 - Minor Headings */
h4, .heading-style-h4 {
  font-size: clamp(18px, calc(18px + 4 * var(--fluid-bp)), 22px);
  line-height: clamp(20px, calc(20px + 2 * var(--fluid-bp)), 22px);
}
/* H5 */
.heading-style-h5 {
  font-size: clamp(20px, calc(20px + 4 * var(--fluid-bp)), 24px);
  line-height: clamp(28px, calc(28px + 5.6 * var(--fluid-bp)), 33.6px);
}
/* H6 */
.heading-style-h6 {
  font-size: clamp(18px, calc(18px + 2 * var(--fluid-bp)), 20px);
  line-height: clamp(24px, calc(24px + 4 * var(--fluid-bp)), 28px);
}
/* Text Size Styles */
/* Large Text */
.text-size-large, .portfolio-expose {
  font-size: clamp(18px, calc(18px + 4 * var(--fluid-bp)), 22px);
  line-height: clamp(28px, calc(28px + 5.88 * var(--fluid-bp)), 33.88px);
}
/* Medium Text */
.text-size-medium {
  font-size: clamp(17px, calc(17px + 2 * var(--fluid-bp)), 18px);
  line-height: clamp(21px, calc(21px + 3 * var(--fluid-bp)), 25px);
}
/* Regular Text */
.text-size-regular, p {
  font-size: clamp(16px, calc(16px + 1 * var(--fluid-bp)), 16px);
  line-height: clamp(22px, calc(22px + 2 * var(--fluid-bp)), 24px);
}
/* Small Text */
.text-size-small {
  font-size: clamp(13px, calc(13px + 1 * var(--fluid-bp)), 14px);
  line-height: clamp(19px, calc(19px + 2 * var(--fluid-bp)), 21px);
}
/* Tiny Text */
.text-size-tiny {
  font-size: clamp(11px, calc(11px + 1 * var(--fluid-bp)), 12px);
  line-height: clamp(16px, calc(16px + 2 * var(--fluid-bp)), 18px);
}
/* Special Elements */
/* Buttons */
.button, .btn {
  font-size: clamp(15px, calc(15px + 1 * var(--fluid-bp)), 16px);
  line-height: clamp(22px, calc(22px + 2 * var(--fluid-bp)), 24px);
}
/* Navigation Links */
.nav-link, .menu-link {
  font-size: clamp(14px, calc(14px + 2 * var(--fluid-bp)), 16px);
  line-height: clamp(20px, calc(20px + 4 * var(--fluid-bp)), 24px);
}
.marken-cta:hover .text-color-white {
    color: #4b4b4b;
}
.button.job-button {
    background-color: #f5f5f5;
    transition: background-color 0.3s ease; /* Smooth transition for better UX */
}
/* Hover state - when hovering over the career item, change button background */
.career19_item:hover .button.job-button {
    background-color: #fff;
}
/* Optional: Override any inline styles that might conflict */
.career19_item:hover .button.job-button[style] {
    background-color: #fff !important;
} 
</style>
    </div>
    <main class="main-wrapper">
      <div class="page-header">
        <header class="section-header-2">
          <div data-collapse="medium" data-animation="default" data-duration="400" fs-scrolldisable-element="smart-nav" data-easing="ease" data-easing2="ease" role="banner" class="navbar2_component w-nav">
            <div class="navbar2_container">
              <a href="index.html" class="navbar2_logo-link w-nav-brand">
                <div class="w-embed"><svg width="81" height="34" viewbox="0 0 81 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.330078 11.272V10.568C1.01609 10.4199 2.03156 10.3631 3.37649 10.3974C3.86031 10.4082 4.14644 10.6384 4.2349 11.0879C4.52916 12.5682 5.3966 16.9451 6.83722 24.2186C6.86791 24.3757 6.91575 24.484 6.98074 24.5436C7.0005 24.5617 7.02458 24.5744 7.05067 24.5806C7.07676 24.5868 7.10399 24.5862 7.12978 24.5788C7.15557 24.5715 7.17905 24.5577 7.19799 24.5387C7.21693 24.5198 7.2307 24.4962 7.23799 24.4704L10.9262 10.8171C10.9582 10.6997 11.0278 10.596 11.1242 10.5221C11.2206 10.4482 11.3385 10.4082 11.4596 10.4082H13.1034C13.2349 10.4083 13.3627 10.4517 13.467 10.5315C13.5713 10.6113 13.6462 10.7231 13.6801 10.8496C15.6786 18.2422 16.9053 22.7536 17.3602 24.3838C17.418 24.5932 17.5209 24.6275 17.6689 24.4867C17.6899 24.4673 17.7042 24.4427 17.7095 24.4163L20.4039 10.9227C20.4334 10.7725 20.5143 10.6373 20.6328 10.54C20.7514 10.4427 20.9002 10.3894 21.0538 10.3893H23.8592C23.9259 10.3891 23.9918 10.4038 24.0521 10.4323C24.1124 10.4608 24.1656 10.5023 24.2078 10.5539C24.2501 10.6055 24.2803 10.6659 24.2964 10.7306C24.3124 10.7953 24.3139 10.8628 24.3006 10.9281L19.8569 33.1873C19.8269 33.3347 19.747 33.4672 19.6305 33.5625C19.5141 33.6577 19.3683 33.7098 19.2178 33.7099H17.0434C16.6733 33.7099 16.4404 33.5321 16.3447 33.1764L12.4724 18.8001C12.4651 18.7731 12.4506 18.7487 12.4305 18.7296C12.4105 18.7105 12.3856 18.6974 12.3587 18.6917C12.2197 18.6611 12.1258 18.7414 12.077 18.9327C11.1961 22.3086 9.92064 27.0782 8.25075 33.2414C8.2134 33.3776 8.13275 33.4977 8.0212 33.5833C7.90966 33.6689 7.77339 33.7153 7.63335 33.7153H5.42368C5.2609 33.7153 5.10314 33.659 4.97724 33.5558C4.85134 33.4526 4.76509 33.309 4.73316 33.1493L0.330078 11.272Z" fill="white"></path>
                    <path d="M48.9372 29.0686C49.5952 29.8268 50.4428 29.8024 51.3337 30.0082C51.4495 30.0342 51.5532 30.0985 51.6283 30.1908C51.7034 30.2831 51.7456 30.398 51.748 30.5173C51.7679 31.2647 51.7778 31.9895 51.7778 32.6918C51.776 33.2424 51.4998 33.5195 50.9492 33.5231C49.2648 33.5393 47.5128 33.2442 46.2401 32.0716C44.6289 30.5823 44.3066 27.7823 44.3012 25.5076C44.2868 18.3623 44.2877 10.605 44.3039 2.23575C44.3039 1.45858 44.2118 0.857422 45.2517 0.857422C45.9558 0.857422 46.7203 0.864643 47.5453 0.879085C47.7304 0.882632 47.9067 0.958657 48.0363 1.09082C48.166 1.22298 48.2386 1.40073 48.2385 1.58585C48.2476 6.83381 48.2448 14.7436 48.2304 25.3154C48.2277 26.515 48.2196 28.2399 48.9372 29.0686Z" fill="white"></path>
                    <path d="M59.1849 14.1371C59.0351 14.1371 58.9602 14.2121 58.9602 14.3619V32.7487C58.9602 33.2614 58.7038 33.5177 58.1911 33.5177H55.7757C55.2774 33.5177 55.0283 33.2677 55.0283 32.7676V14.3727C55.0283 14.2157 54.9488 14.1371 54.79 14.1371H54.2376C54.0803 14.1371 53.9294 14.0747 53.8182 13.9634C53.707 13.8522 53.6445 13.7014 53.6445 13.5441V11.218C53.6445 11.0794 53.6996 10.9465 53.7976 10.8484C53.8956 10.7504 54.0285 10.6954 54.1672 10.6954H54.7575C54.8257 10.6954 54.8911 10.6683 54.9394 10.62C54.9876 10.5718 55.0147 10.5063 55.0147 10.4381C55.0147 9.51381 55.0319 8.62471 55.0662 7.77081C55.1203 6.4349 55.3803 5.17842 55.8461 4.00138C56.0717 3.42549 56.4445 2.87578 56.9644 2.35225C58.183 1.12827 60.3385 0.800613 62.0499 0.879143C62.5698 0.903514 62.7567 1.19055 62.7756 1.68881C62.8063 2.55354 62.8009 3.2928 62.7594 3.9066C62.7341 4.29654 62.5265 4.50415 62.1366 4.52942C60.4739 4.63503 59.2851 5.13871 59.0766 6.96926C58.9557 8.02896 58.9232 9.17351 58.9791 10.4029C58.9882 10.5979 59.0902 10.6954 59.2851 10.6954H61.4406C61.8956 10.6954 62.123 10.9228 62.123 11.3778V13.4547C62.123 13.5444 62.1054 13.6331 62.0711 13.7159C62.0368 13.7987 61.9865 13.8739 61.9232 13.9373C61.8598 14.0006 61.7846 14.0509 61.7018 14.0852C61.619 14.1195 61.5303 14.1371 61.4406 14.1371H59.1849Z" fill="white"></path>
                    <path d="M70.2085 29.0634C70.8666 29.8216 71.7141 29.7972 72.6051 30.003C72.7204 30.0289 72.8238 30.0928 72.8989 30.1846C72.9739 30.2763 73.0163 30.3906 73.0194 30.5094C73.0392 31.2568 73.0501 31.9816 73.0519 32.6839C73.0501 33.2345 72.7738 33.5116 72.2232 33.5152C70.5389 33.5341 68.7869 33.239 67.5141 32.0665C65.9029 30.5798 65.578 27.7798 65.5726 25.5052C65.5545 18.3617 65.5518 10.6062 65.5644 2.23868C65.5644 1.46151 65.4724 0.860352 66.5122 0.860352C67.2163 0.860352 67.9808 0.86667 68.8058 0.879307C68.9909 0.882853 69.1672 0.958879 69.2969 1.09104C69.4265 1.2232 69.4991 1.40095 69.4991 1.58607C69.5099 6.83403 69.5108 14.7421 69.5018 25.3102C69.4991 26.5098 69.4909 28.2347 70.2085 29.0634Z" fill="white"></path>
                    <path d="M30.3661 8.10338C31.6179 8.10338 32.6327 7.08862 32.6327 5.83684C32.6327 4.58507 31.6179 3.57031 30.3661 3.57031C29.1144 3.57031 28.0996 4.58507 28.0996 5.83684C28.0996 7.08862 29.1144 8.10338 30.3661 8.10338Z" fill="white"></path>
                    <path d="M36.3356 8.10684C37.5889 8.10684 38.6049 7.09087 38.6049 5.8376C38.6049 4.58433 37.5889 3.56836 36.3356 3.56836C35.0824 3.56836 34.0664 4.58433 34.0664 5.8376C34.0664 7.09087 35.0824 8.10684 36.3356 8.10684Z" fill="white"></path>
                    <path d="M78.2829 8.10684C79.5362 8.10684 80.5521 7.09087 80.5521 5.8376C80.5521 4.58433 79.5362 3.56836 78.2829 3.56836C77.0296 3.56836 76.0137 4.58433 76.0137 5.8376C76.0137 7.09087 77.0296 8.10684 78.2829 8.10684Z" fill="white"></path>
                    <path d="M33.3314 10.0234C36.1693 10.0234 38.4331 11.4965 39.5353 14.1314C40.5724 16.6037 40.3341 19.5445 40.3341 21.8679C40.3341 24.1913 40.5751 27.1321 39.538 29.6044C38.4331 32.2392 36.172 33.7151 33.3341 33.7151C30.4962 33.7151 28.2351 32.2392 27.1303 29.6071C26.0931 27.1321 26.3341 24.1913 26.3341 21.8679C26.3314 19.5472 26.0931 16.6064 27.1276 14.1314C28.2324 11.4993 30.4935 10.0234 33.3314 10.0234ZM30.2633 21.8706C30.2633 24.9197 30.2715 26.5607 30.2877 26.7936C30.4177 28.5538 31.3763 30.081 33.3368 30.081C35.3001 30.0837 36.2587 28.5538 36.3887 26.7936C36.4049 26.5607 36.413 24.9197 36.413 21.8706C36.413 18.8197 36.4049 17.1778 36.3887 16.9449C36.2587 15.1847 35.3001 13.6575 33.3395 13.6575C31.3763 13.6575 30.4177 15.1847 30.2877 16.9449C30.2715 17.1778 30.2633 18.8197 30.2633 21.8706Z" fill="white"></path>
                    <path d="M79.5267 10.1943H77.0029C76.614 10.1943 76.2988 10.5096 76.2988 10.8984V32.8163C76.2988 33.2052 76.614 33.5204 77.0029 33.5204H79.5267C79.9155 33.5204 80.2307 33.2052 80.2307 32.8163V10.8984C80.2307 10.5096 79.9155 10.1943 79.5267 10.1943Z" fill="white"></path>
                  </svg></div>
              </a>
              <nav role="navigation" id="w-node-d392c3b2-4450-74b5-1131-dbdbdf68d69c-df68d698" class="navbar2_menu is-page-height-tablet w-nav-menu">
                <a href="team.html" class="navbar2_link text-size-medium w-nav-link">Team</a>
                <a href="leistungen.html" class="navbar2_link text-size-medium w-nav-link">Leistungen</a>
                <a href="marken.html" class="navbar2_link text-size-medium w-nav-link">Marken</a>
                <a href="projekte.html" class="navbar2_link text-size-medium w-nav-link">Projekte</a>
                <a href="karriere.html" aria-current="page" class="navbar2_link text-size-medium w-nav-link w--current">Karriere</a>
                <a href="kontakt.html" class="navbar2_link text-size-medium w-nav-link">Kontakt</a>
              </nav>
              <a href="identitaet.html" class="navbar-icon-link w-nav-brand">
                <div class="icon-embed-small w-embed"><svg width="191" height="194" viewbox="0 0 191 194" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M183.945 9.64457C183.926 9.25248 183.877 8.86021 183.807 8.46873C183.796 8.41001 183.782 8.3527 183.77 8.29435C183.713 8.00986 183.642 7.72715 183.556 7.44559C183.532 7.36472 183.507 7.28434 183.481 7.20432C183.393 6.94382 183.292 6.68607 183.18 6.43009C183.148 6.35709 183.12 6.28288 183.086 6.21085C183.064 6.16373 183.048 6.11533 183.025 6.0684C182.727 5.46134 182.366 4.90635 181.96 4.39841C181.94 4.37302 181.917 4.34946 181.897 4.32426C181.692 4.07298 181.474 3.83591 181.246 3.61082C181.204 3.5687 181.16 3.52781 181.117 3.48655C180.884 3.26585 180.642 3.05839 180.39 2.86509C180.361 2.84287 180.333 2.81937 180.304 2.79746C179.714 2.35624 179.076 1.99046 178.404 1.70524C178.361 1.68674 178.317 1.67112 178.273 1.65342C177.984 1.53599 177.69 1.43412 177.391 1.34678C177.336 1.33079 177.282 1.314 177.226 1.29911C175.826 0.918678 174.336 0.871864 172.877 1.19956C172.872 1.2006 172.868 1.20145 172.863 1.20255C172.178 1.35758 171.5 1.5853 170.843 1.91074L95.4955 38.9214L20.1513 1.91074C19.4994 1.58891 18.8269 1.36234 18.1481 1.20725C18.1288 1.20286 18.1095 1.19938 18.0902 1.19517C17.7393 1.11741 17.3874 1.0615 17.0345 1.02634C17.0257 1.02543 17.0169 1.02433 17.008 1.02348C15.8966 0.916237 14.788 1.01621 13.7309 1.30711C13.7045 1.31437 13.6783 1.32243 13.6519 1.32993C13.3104 1.42722 12.9752 1.54295 12.6473 1.6793C12.6402 1.68223 12.6331 1.68473 12.6261 1.68766C11.5992 2.11759 10.6521 2.73661 9.83469 3.52372C9.81607 3.5416 9.7974 3.55912 9.7789 3.57719C9.52292 3.82798 9.27835 4.09226 9.051 4.37589C9.05002 4.37711 9.04886 4.37827 9.04788 4.37949C8.63694 4.89262 8.27115 5.45353 7.97019 6.0684C7.94718 6.11533 7.93112 6.16373 7.90897 6.21085C7.87516 6.28288 7.84671 6.35709 7.81473 6.43009C7.70243 6.68601 7.60135 6.94364 7.51425 7.20408C7.48746 7.2844 7.46268 7.36503 7.43808 7.44621C7.35263 7.72739 7.28153 8.00974 7.22446 8.29374C7.21268 8.35239 7.1987 8.40995 7.18808 8.46891C7.11746 8.86027 7.06858 9.25236 7.04996 9.64433L0.213412 124.731C0.0229827 127.923 1.52738 130.989 4.17435 132.792L90.3748 191.539C91.9173 192.593 93.7074 193.12 95.4973 193.12C97.2874 193.12 99.0774 192.593 100.62 191.539L186.814 132.792C189.461 130.989 190.965 127.923 190.775 124.731L183.945 9.64457ZM95.4973 173.004L18.685 120.655L24.3978 24.4765L73.8308 49.563L45.7385 63.3619C41.2253 65.5772 39.3655 71.0298 41.5808 75.5429C43.1614 78.7612 46.3986 80.6338 49.7565 80.6338C51.1022 80.6338 52.4733 80.3354 53.7619 79.7007L95.0284 59.4306C95.3401 59.4468 95.6509 59.4469 95.9626 59.4309L137.227 79.7007C138.515 80.3354 139.886 80.6338 141.232 80.6338C144.59 80.6338 147.827 78.7612 149.408 75.5429C151.623 71.0298 149.763 65.5772 145.25 63.3619L117.161 49.5639L166.591 24.4765L172.303 120.655L95.4973 173.004Z" fill="white"></path>
                  </svg></div>
              </a>
              <div id="w-node-_7728f4df-2d5d-59ec-10e7-ad72bf765fec-bf765fec" class="navbar2_button-wrapper">
                <a href="#" class="button is-navbar2-button w-button">Button</a>
                <div class="navbar2_menu-button w-nav-button">
                  <div class="menu-icon2">
                    <div class="menu-icon2_line-top"></div>
                    <div class="menu-icon2_line-middle">
                      <div class="menu-icon2_line-middle-inner"></div>
                    </div>
                    <div class="menu-icon2_line-bottom"></div>
                  </div>
                </div>
                <div class="block-text_action"><button type="button" data-button="" data-menu-dialog-toggle="" aria-label="Open Menu" class="button_dialog is-menu-button">
                    <div class="menu-text hide">Menü</div><span class="button_dialog-menu-line-wrap"><span class="button_dialog-menu-line"></span><span class="button_dialog-menu-line"></span><span class="button_dialog-menu-line"></span></span><span class="button_dialog-bg"></span>
                  </button>
                  <dialog data-menu-dialog="" data-theme="light" class="menu-dialog">
                    <div class="menu-dialog_outer">
                      <div class="menu-dialog_inner">
                        <div class="menu-dialog_header"><span class="menu-dialog_header-text">Menü</span><button type="button" data-menu-dialog-close="" aria-label="Close Dialog" autofocus="" class="menu-dialog_button-close"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 24 24" fill="none" aria-hidden="true" class="icon">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M6.75827 17.2426L12.0009 12M17.2435 6.75736L12.0009 12M12.0009 12L6.75827 6.75736M12.0009 12L17.2435 17.2426" fill="currentColor" stroke="#000000" stroke-width="1.5" color="#000000"></path>
                            </svg></button></div>
                        <nav class="menu-dialog_nav">
                          <a href="identitaet.html" class="navbar-icon-link icon-link-mobile w-nav-brand"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 32 32" fill="none" class="icon-embed-small">
                              <path d="M30.5892 1.59116C30.586 1.52648 30.578 1.46178 30.5664 1.39721C30.5646 1.38752 30.5623 1.37807 30.5603 1.36844C30.5509 1.32152 30.5392 1.27488 30.525 1.22844C30.5211 1.2151 30.5169 1.20184 30.5126 1.18864C30.4981 1.14567 30.4815 1.10316 30.463 1.06094C30.4577 1.04889 30.4531 1.03665 30.4475 1.02477C30.4439 1.017 30.4412 1.00902 30.4374 1.00128C30.3883 0.901142 30.3287 0.809597 30.2618 0.725813C30.2585 0.721625 30.2547 0.717739 30.2514 0.713582C30.2175 0.672134 30.1816 0.63303 30.144 0.595901C30.1371 0.588954 30.1298 0.582209 30.1227 0.575403C30.0843 0.538999 30.0444 0.504779 30.0028 0.472894C29.998 0.469229 29.9934 0.465353 29.9886 0.461739C29.8913 0.38896 29.786 0.328625 29.6752 0.281579C29.6681 0.278527 29.6608 0.275951 29.6536 0.273031C29.6059 0.253661 29.5574 0.236858 29.5081 0.222451C29.499 0.219814 29.4901 0.217044 29.4809 0.214588C29.25 0.151837 29.0042 0.144115 28.7635 0.198168C28.7627 0.198339 28.762 0.198479 28.7612 0.198661C28.6482 0.224233 28.5364 0.261795 28.428 0.315476L15.9996 6.42033L3.57166 0.315476C3.46413 0.26239 3.3532 0.225018 3.24124 0.199436C3.23805 0.198712 3.23487 0.198138 3.23169 0.197444C3.1738 0.184617 3.11576 0.175395 3.05755 0.169595C3.0561 0.169445 3.05465 0.169264 3.05318 0.169124C2.86985 0.151434 2.68699 0.167924 2.51263 0.215908C2.50827 0.217105 2.50395 0.218435 2.49959 0.219672C2.44326 0.23572 2.38797 0.254809 2.33389 0.2773C2.33272 0.277783 2.33154 0.278196 2.33039 0.278679C2.161 0.349595 2.00478 0.451702 1.86995 0.581534C1.86688 0.584484 1.8638 0.587373 1.86075 0.590354C1.81853 0.631721 1.77818 0.675314 1.74068 0.722098L1.74017 0.722692C1.67238 0.807332 1.61205 0.899853 1.56241 1.00128C1.55861 1.00902 1.55596 1.017 1.55231 1.02477C1.54673 1.03665 1.54204 1.04889 1.53676 1.06094C1.51824 1.10315 1.50157 1.14564 1.4872 1.1886C1.48278 1.20185 1.47869 1.21515 1.47463 1.22854C1.46054 1.27492 1.44881 1.3215 1.4394 1.36834C1.43746 1.37802 1.43515 1.38751 1.4334 1.39724C1.42175 1.46179 1.41369 1.52646 1.41061 1.59112L0.282937 20.5745C0.251526 21.101 0.499674 21.6067 0.936287 21.9041L15.1549 31.5944C15.4094 31.7682 15.7046 31.8551 15.9999 31.8551C16.2951 31.8551 16.5904 31.7682 16.8448 31.5944L31.0624 21.9041C31.499 21.6067 31.7471 21.101 31.7158 20.5745L30.5892 1.59116ZM15.9999 28.537L3.3298 19.9022L4.27211 4.03766L12.426 8.17564L7.79223 10.4517C7.04778 10.8172 6.74101 11.7166 7.10642 12.461C7.36714 12.9918 7.90111 13.3007 8.45499 13.3007C8.67696 13.3007 8.90312 13.2515 9.11568 13.1468L15.9225 9.80329C15.9739 9.80596 16.0252 9.80598 16.0766 9.80334L22.8831 13.1468C23.0956 13.2515 23.3217 13.3007 23.5437 13.3007C24.0976 13.3007 24.6316 12.9918 24.8924 12.461C25.2577 11.7166 24.9509 10.8172 24.2065 10.4517L19.5733 8.17579L27.7267 4.03766L28.6688 19.9022L15.9999 28.537Z" fill="currentColor"></path>
                            </svg></a>
                          <ul role="list" class="menu-dialog_nav-list w-list-unstyled">
                            <li class="menu-dialog_nav-list-item">
                              <a href="index.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Start</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="team.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Team</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="leistungen.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Leistungen</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="marken.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Marken</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="projekte.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Projekte</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="identitaet.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Wölfli Identität</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="karriere.html" aria-current="page" class="menu-dialog_nav-link u-h2 w-inline-block w--current"><span class="menu-dialog_nav-link-outer">Karriere</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="kontakt.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Kontakt</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item hide">
                              <a href="tel:+41445352222" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer phone-number text-size-regular">+41 44 535 22 22</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item hide">
                              <a href="mailto:<EMAIL>" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer email"><EMAIL></span></a>
                            </li>
                          </ul>
                        </nav>
                      </div><button type="button" tabindex="-1" data-menu-dialog-backdrop="" class="menu-dialog_backdrop"></button>
                    </div>
                  </dialog>
                </div>
              </div>
              <div class="button-row">
                <a data-cursor="Ruf den Chef direkt an" href="#" class="button w-inline-block">
                  <div class="header-phone-svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" stroke-width="1.5" viewbox="0 0 24 24" fill="none" color="#000000" class="phone-svg">
                      <path d="M22 5H16M16 5L19 2M16 5L19 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg></div>
                  <p class="button-text">+41 44 535 22 22</p>
                  <div class="button-bg"></div>
                </a>
              </div>
              <div class="header-phone">
                <div class="header-phone-svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" stroke-width="1.5" viewbox="0 0 24 24" fill="none" color="#000000">
                    <path d="M22 5H16M16 5L19 2M16 5L19 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg></div>
              </div>
              <div class="cursor">
                <p class="cursor-paragraph">Learn more</p>
              </div>
            </div>
          </div>
          <div class="header_component-2">
            <div class="header_card-2 text-color-alternate-2">
              <div class="header-content">
                <div class="padding-global padding-custom1">
                  <div class="container-large">
                    <div class="padding-section-large">
                      <div class="header_content">
                        <div class="max-width-large">
                          <div>
                            <h1 class="heading-style-h1">Engagement für Bauprojekte - <span class="header-h1-span">und für Menschen</span></h1>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="header-gradient landing-gradient"></div>
              <div class="header_background-video">
                <div class="vimeo-wrapper"><video loading="eager" preload="metadata" title="Wölfli Header Video" loop="" playsinline="" muted="" autoplay="" defaultmuted="" class="cdn-video">
                    <source src="https://trace-creative.fra1.cdn.digitaloceanspaces.com/Videos/Woelfli/karriere-seite-header_1080p.mp4" type="video/mp4" data-resolution="1080p" data-format="mp4">
                    <source src="https://trace-creative.fra1.cdn.digitaloceanspaces.com/Videos/Woelfli/Karriere-Seite-Header_720p.mp4" type="video/mp4" data-resolution="720p" data-format="mp4">
                    <source src="https://trace-creative.fra1.cdn.digitaloceanspaces.com/Videos/Woelfli/Karriere-Seite-Header_576p.mp4" type="video/mp4" data-resolution="576p" data-format="mp4">
                  </video></div>
              </div>
            </div>
          </div>
        </header>
        <section class="intro-section">
          <div class="padding-global">
            <div class="container-large">
              <div class="padding-section-large">
                <div class="header-intro">
                  <div blocks-name="max-width-xlarge" blocks-slot-children="ST265" class="max-width-large header-text-width">
                    <div class="margin-bottom margin-small intro-margin">
                      <div blocks-non-deletable="true" blocks-name="header105_heading-wrapper" class="header-intro-wrapper">
                        <h1 class="heading-style-h2 text-weight-medium line-height">Karriere bei Wölfli</h1>
                        <h1 class="heading-style-h2 hyphen">Wir glauben an eine Unternehmenskultur, in der Fachwissen, Interesse und Persönlichkeit zählen. Wir führen Bauprojekte mit Weitsicht, Präzision und Teamgeist – von der ersten Idee bis zur erfolgreichen Umsetzung. Wer bei uns arbeitet, übernimmt Verantwortung, denkt mit und entwickelt sich kontinuierlich weiter. In unseren Jobs entwickelst du dich weiter, denkst mit und kannst Verantwortung übernehmen.<br><br>Ob Berufserfahrene oder Berufseinsteiger:innen – wir freuen uns auf Talente, die etwas bewegen wollen.</h1>
                      </div>
                      <a role="button" href="team.html" class="link-block-3 w-inline-block">
                        <div data-w-id="17c584b9-154c-576b-2fa0-5cf5cffb15e5" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="cta-button">
                          <div class="heading-style-h4 cta-text">Das Team<br></div>
                          <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                            </svg></div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <section id="stellen-header" class="stellen-sektion">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large padding-top">
              <div class="team-section-heading">
                <div class="margin-bottom">
                  <div class="max-width-full">
                    <div class="headline-tag-group">
                      <div blocks-name="tagline" class="text-style-tagline">Kernkompetenzen</div>
                      <div class="after-header-headline">
                        <h1 class="heading-style-h1 text-color-black">Offene Stellen</h1>
                        <div class="intro-headline-slogan"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 681 63" fill="none" class="svg-3 handwriting-svg">
                            <path d="M22.8 8C17.1 18 13.7 29.3 12.8 40.8C12.6 43.2 12.7 46 14.4 47.7C16 49.2 18.6 49.3 20.7 48.4C22.8 47.5 24.3 45.8 25.8 44.1C34.2 34.2 39.8 22 41.8 9.2" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M44.9999 26.8999C45.6999 27.4999 45.8999 28.3999 45.7999 29.2999C45.6999 30.1999 45.2999 30.9999 44.9999 31.7999L40.9999 40.7999C40.8999 40.8999 41.0999 41.0999 41.1999 40.9999C44.5999 38.2999 48.0999 35.7999 51.6999 33.3999C52.7999 32.6999 54.0999 31.8999 55.2999 32.2999C56.6999 32.6999 57.3999 34.3999 57.3999 35.8999C57.3999 37.3999 56.9999 38.8999 57.1999 40.2999C57.3999 41.7999 58.2999 43.3999 59.7999 43.3999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M90 11.8C85.1 22.3 80.1 33.0001 78.8 44.4001C79.2 40.3001 79.6 36.1 80.1 32C80.2 31.3 80.2 30.6 79.9 30C79.3 28.9 77.8 28.8 76.6 29.1C70.9 30.1 66.2 35.3 65.8 41.1C65.7 42.5 65.9 44.0001 67.1 44.7001C67.9 45.2001 68.9 45.1001 69.8 44.7001C70.6 44.3001 71.3 43.7 72 43.1C74.5 40.8 76.9 38.6 79.4 36.3" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M116 10.6001C111.9 20.7001 108.1 31.0001 104.6 41.4001C104.5 41.8001 105 42.1001 105.3 41.8001C108.7 37.9001 112.7 34.5001 117.2 31.9001C117.8 31.5001 118.5 31.2001 119.1 31.3001C120.3 31.6001 120.6 33.1001 120.7 34.3001C120.9 37.7001 121.1 41.1001 121.3 44.4001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M134 30.8999C133.8 33.2999 132.7 35.4999 131.8 37.6999C130.9 39.8999 130.1 42.2999 130.6 44.6999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M137.4 17.3C136.8 17.4 136.3 18.1001 136.5 18.7001C136.7 19.3001 137.4 19.7 138 19.4C138.6 19.1 138.8 18.3 138.5 17.8C138.4 17.7 138.4 17.7 138.3 17.6" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M143.2 32.4001C144.8 36.6001 147.7 36.8001 149.8 36.0001C151.9 35.2001 153.3 33.2001 153.8 31.0001C154.1 29.4001 153.9 27.5001 152.7 26.4001C151.6 25.4001 149.8 25.3001 148.4 25.9001C147 26.5001 145.9 27.6001 145 28.8001C142.5 32.0001 141.1 35.9001 140.9 39.9001C140.8 41.9001 141.1 44.2001 142.7 45.5001C144.1 46.7001 146.2 46.7001 147.9 46.1001C149.6 45.5001 151 44.3001 152.4 43.1001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M166 25.3C162.6 31.6 160.3 38.5 159.5 45.6C159.6 41.9 161.8 38.5001 164.3 35.9001C168 32.1001 172.6 29.2 177.6 27.5" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M207.7 25.3C204.6 25.2 201.5 26.2001 199 28.1001C196.5 30.0001 194.8 32.8 194.1 35.8C193.8 37.3 193.9 39.2001 195.1 40.0001C196.3 40.8001 197.8 40.2001 199.1 39.7001C200.9 39.0001 202.6 38.2001 204.4 37.5001C206 36.8001 207.8 36.1 208.7 34.6C209.6 33.3 209.7 31.7001 209.5 30.2001C209.3 28.7001 207.6 33.2001 206.4 36.7001C204.9 40.8001 203.9 45.0001 203.1 49.2001C202.7 51.0001 202.4 52.9 201.6 54.6C200.7 56.3001 199.2 57.7001 197.4 57.9001C195.3 58.2001 193.1 56.7001 192.6 54.7001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M226.7 41.7001C225.4 43.5001 223.9 45.5001 221.7 45.9001C219.7 46.2001 217.6 45.1001 216.6 43.3001C215.6 41.6001 215.4 39.4001 215.9 37.4001C216.3 35.4001 217.3 33.6001 218.3 31.9001C219.5 30.0001 220.8 28.0001 222.8 27.0001C224.8 26.0001 227.7 26.3001 228.8 28.3001C229.7 29.8001 229.2 31.8001 228.1 33.1001C226.9 34.4001 225.2 35.0001 223.5 35.2001C221.8 35.3001 220.1 35.1001 218.4 34.8001C216.7 34.5001 214.9 33.9001 213.7 32.6001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M246.7 11C243.5 18.9 240.2 26.9 237 34.8C236.2 36.8 235.4 38.7 234.9 40.8C234.7 41.5 235.6 42 236.1 41.5L245.2 32.3C246.1 31.4 246.9 30.5 248.1 30.1C249.2 29.6 250.7 29.7 251.5 30.6C252.3 31.4 252.3 32.7 252.3 33.8C252.3 36.1 252.2 38.4 252.2 40.7C252.2 41.7 252.2 42.7 252.5 43.7C252.8 44.7 253.5 45.6 254.4 45.9" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M264.1 34C266.1 34.5 268.3 35 270.3 34.6C272.4 34.3 274.4 33.1 275.2 31.1C276 29.1 275.1 26.5 273 25.9C271 25.3 269 26.8 267.6 28.2C266 29.9 264.5 31.8 263.3 33.8C262.4 35.4 261.5 37.1 261.2 38.9C260.9 40.7 261.2 42.7 262.2 44.2C263.2 45.7 265.1 46.8 266.9 46.5C269.1 46.2 270.6 44.3 271.9 42.5" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M285.1 26.6001C286.2 28.1001 285.6 30.3001 284.9 32.0001C283.5 35.6001 282 39.2001 280.6 42.7001C284 38.5001 288.2 35.1001 293 32.6001C294.2 32.0001 295.8 31.4001 296.9 32.3001C297.6 32.8001 297.8 33.8001 297.8 34.6001C297.8 35.4001 297.5 36.3001 297.4 37.1001C297.1 39.5001 298.1 42.1001 299.9 43.7001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M327.1 25.1001C325.7 28.2001 325.1 30.5001 324 33.1001C322.9 35.7001 321.8 38.7001 322.4 41.5001C322.7 42.8001 323.6 44.2001 325 44.1001C326.1 44.0001 326.8 43.0001 327.3 42.1001C329.8 38.1001 332.3 34.1001 334.8 30.1001C334.1 33.6001 333.4 37.2001 332.7 40.7001C332.5 41.7001 332.3 42.8001 332.5 43.8001C332.7 44.8001 333.6 45.8001 334.6 45.8001C335.7 45.8001 336.6 44.9001 337.3 44.0001C341.1 39.2001 344.4 33.9001 347.2 28.4001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M354.7 29.8999C353.7 31.9999 353.5 33.8999 352.8 35.8999C351.8 38.4999 351.5 41.3999 350.3 44.0999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M358.6 16.8C357.6 17 357 18.3001 357.5 19.2001C358 19.3001 358.6 19.1001 358.9 18.7001C359.2 18.3001 359.2 17.6001 358.9 17.2001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M367.7 26.3999C364.6 32.3999 362.5 38.8999 361.6 45.5999C362.5 41.5999 364.9 37.9999 367.9 35.1999C370.9 32.3999 374.6 30.3999 378.5 28.8999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M418.3 11.3C414.5 22.8 411.6 34.6 409.6 46.6C410.1 41.2 410.7 35.8 411.2 30.4C411.3 33.4 410.4 36.5001 408.5 38.9001C406.7 41.3001 403.9 43 401 43.6C400.2 43.8 399.2 43.9 398.5 43.5C397 42.8 396.6 40.9 396.7 39.3C397 34.8 399.9 30.5 404 28.5C405.6 27.8 407.2 28 409 28.1C409.6 28.1 410.3 28.2 410.8 28.6C411.3 29 411.6 29.8 411.2 30.3" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M423.7 29.6001C423.3 34.2001 421.5 38.7001 418.6 42.4001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M427.5 17.1C426.6 17.4 426.1 18.4 426.4 19.2C426.7 20.1 427.7 20.6 428.5 20.3C428.8 19.2 428.6 18 428.1 17" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M433.9 33.7999C435.6 34.2999 437.3 34.4999 439 34.1999C440.7 33.8999 442.3 32.7999 443.1 31.1999C443.9 29.5999 443.6 27.5999 442.4 26.3999C441.3 25.3999 439.7 25.1999 438.2 25.5999C436.7 25.9999 435.6 26.9999 434.6 27.9999C431.6 31.0999 429.8 35.3999 429.8 39.6999C429.8 41.1999 430.1 42.8999 431.1 43.9999C432.3 45.1999 434.3 45.3999 435.9 44.9999C437.6 44.4999 439 43.4999 440.4 42.3999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M491.5 8.8999C488.1 9.2999 484.7 9.6999 481.3 10.1999C479.8 10.3999 478.3 10.5999 476.9 11.2999C475.3 12.0999 474.2 13.5999 473.1 14.9999C469.1 20.5999 465.9 26.6999 463.5 33.0999C462.7 35.2999 462 37.5999 462.1 39.8999C462.2 42.1999 463.1 44.6999 465 45.9999C466.5 47.0999 468.4 47.3999 470.3 47.4999C473.4 47.7999 476.6 47.6999 479.6 46.6999C482.6 45.6999 485.3 43.6999 486.7 40.9999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M464.6 28.6C471.4 29.7 478.5 28.7 484.8 25.8" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M497.7 26.8C499.6 32.8 501.5 38.8 503.5 44.8C504 46.3 504.5 47.8001 505.6 48.9001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M510 26.6001C506.9 30.1001 503.6 33.4001 500.3 36.7001C497.5 39.5001 494.7 42.3001 491.8 45.2001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M530.1 11.8999C527.9 16.7999 525.7 21.7999 523.4 26.6999C520.3 33.6999 517 40.9999 517.3 48.6999C517.8 48.1999 518.3 47.6999 518.8 47.1999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M515.5 24.7C522 23.8 528.5 22.9 535 22" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M539.4 25.1001C538.6 32.1001 533.6 38.2001 533.2 45.3001C535.1 37.1001 542.2 30.3001 550.5 28.9001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M573.3 43.1C572.5 43.9 571.1 43.8 570.2 43C569.3 42.2 569 41.1 568.8 39.9C568.5 38 568.8 36.2 569.2 34.4C569.6 32.9 570.1 31.4 569.7 29.8C569.2 28.1 567.5 26.6 565.8 26.4C564.1 26.3 562.3 28 561.1 29.1C558.1 31.6 555.8 34.9 554.3 38.5C553.9 39.4 553.6 40.4 553.7 41.4C553.8 42.4 554.3 43.4 555.3 43.8C556.5 44.3 557.8 43.6 558.8 42.9C560.6 41.7 562.5 40.5 564.3 39.2C565.9 38.1 567.4 36.9 568.6 35.3C568.9 34.9 569.1 34.5 569.3 34.1" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M580.7 26.9C581 25.8 582.4 25.3 583.5 25.8C584.5 26.3 585.1 27.4 585.2 28.5C585.3 29.6 585 30.7 584.7 31.8C583.4 36.1 581.5 40.1 579 43.8C582.4 39.5 586.2 35.4 590.1 31.5C590.8 30.8 591.8 30 592.8 30.3C593.6 30.5 594.1 31.3 594.2 32.1C594.3 32.9 594.1 33.7 593.9 34.5C593.1 37.4 592.4 40.3 591.6 43.2C592.8 38.1 596.7 33.7 601.7 31.8C601.9 34.4 602.1 37 602.3 39.6C602.4 40.6 602.5 41.7 603.2 42.3C604 43.1 605.4 43 606.4 42.5C607.4 41.9 608.1 41 608.8 40.1" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M613.3 33.5C616.9 33.9 623.9 35.2 626.8 33.1C627.6 32.5 628.4 31.7 628.7 30.7C629 29.7 629 28.6 628.4 27.7C627.4 26.3 625.2 26.1 623.5 26.7C620.4 27.7 618 30.5 616.8 33.5C615.6 36.5 615.4 39.9 615.5 43.2C615.5 44 615.6 44.9 616.1 45.5C616.8 46.5 618.2 46.8 619.4 46.7C622.6 46.5 625.6 44.6 627.2 41.9" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M637.7 30.6001C637 32.3001 636.3 34.0001 635.6 35.7001C634.5 38.5001 633.4 41.2001 632.7 44.1001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M641.6 16.3999C640.8 16.3999 640 16.7999 639.5 17.4999C639 18.1999 638.9 18.9999 639.2 19.7999C640 19.7999 640.9 19.3999 641.4 18.7999C642 18.1999 642.2 17.2999 642.1 16.4999C641.8 16.4999 641.5 16.4999 641.2 16.4999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M655.2 12.7C647.8 22.9 643.2 35 641.8 47.5" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M656.7 33.5999C657.2 34.8999 658.6 35.7999 660 35.9999C661.4 36.1999 662.8 35.8999 664.1 35.2999C665.6 34.5999 666.9 33.4999 667.7 32.0999C668.5 30.6999 668.6 28.8999 667.9 27.3999C667 25.5999 664.8 24.5999 662.7 24.7999C660.6 24.9999 658.8 26.2999 657.5 27.8999C656.2 29.4999 655.4 31.4999 654.8 33.3999C654.1 35.6999 653.7 37.9999 653.4 40.2999C653.3 41.4999 653.2 42.7999 653.9 43.7999C654.9 45.4999 657.2 45.8999 659.1 45.1999C660.9 44.5999 662.4 43.0999 663.7 41.6999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div id="stellen-sektion" data-current="Tab 1" data-easing="ease" data-duration-in="300" data-duration-out="100" class="career19_tabs w-tabs">
                <div class="category-filter-menu is-center no-scrollbar w-tab-menu">
                  <a data-w-tab="Tab 1" class="category-filter-link w-inline-block w-tab-link w--current">
                    <div>Alle ansehen</div>
                  </a>
                  <a data-w-tab="Tab 2" class="category-filter-link w-inline-block w-tab-link">
                    <div>Bauleitung</div>
                  </a>
                  <a data-w-tab="Tab 3" class="category-filter-link w-inline-block w-tab-link">
                    <div>Projektleitung</div>
                  </a>
                </div>
                <div class="w-tab-content">
                  <div data-w-tab="Tab 1" class="career19_tab-pane w-tab-pane w--tab-active">
                    <div class="career19_list-wrapper">
                      <div class="career19_list">
                        <div id="w-node-e70b951f-8c83-b8d2-a6d1-c44bbf3b3252-182d6528" class="career19_item">
                          <div class="margin-bottom margin-medium">
                            <div class="career19_top-wrapper">
                              <div class="career19_title-wrapper">
                                <div class="heading-style-h5">Bauleiter:in (80-100%)</div>
                              </div>
                              <div class="button-group">
                                <a data-w-id="e70b951f-8c83-b8d2-a6d1-c44bbf3b325c" href="jobs/bauleiter-in-80-100.html" class="button job-button w-inline-block">
                                  <div>Jetzt bewerben</div>
                                </a>
                              </div>
                            </div>
                          </div>
                          <p class="text-size-regular">Projektarbeit über alle Phasen von der Kostenermittlung, über die Ausschreibung und Angebotsverhandlung bis hin zur Übergabe von mängelfreien Bauwerken. </p>
                          <div class="margin-top margin-small">
                            <div class="career19_job-details-wrapper">
                              <div class="career19_detail-wrapper">
                                <div class="career19_icon-wrapper">
                                  <div class="icon-embed-xsmall w-embed">
                                    <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                                      <path d="M20 10C20 14.4183 12 22 12 22C12 22 4 14.4183 4 10C4 5.58172 7.58172 2 12 2C16.4183 2 20 5.58172 20 10Z" stroke="#000000" stroke-width="1.5"></path>
                                      <path d="M12 11C12.5523 11 13 10.5523 13 10C13 9.44772 12.5523 9 12 9C11.4477 9 11 9.44772 11 10C11 10.5523 11.4477 11 12 11Z" fill="#000000" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                  </div>
                                </div>
                                <div class="text-size-medium">Zürich</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div id="w-node-e70b951f-8c83-b8d2-a6d1-c44bbf3b326e-182d6528" class="career19_item">
                          <div class="margin-bottom margin-medium">
                            <div class="career19_top-wrapper">
                              <div class="career19_title-wrapper">
                                <div class="heading-style-h5">Bau-/Projektleiter:in (80-100%)</div>
                              </div>
                              <div class="button-group">
                                <a href="jobs/bau--projektleiter-in-80-100.html" class="button job-button w-inline-block">
                                  <div>Jetzt bewerben</div>
                                </a>
                              </div>
                            </div>
                          </div>
                          <p class="text-size-regular">Adäquate Betreuung von Bauprojekten ab Kostenermittlung bis zur Fertigstellung. Du bist die Schlüsselstelle im Projekt und koordinierst zwischen Ausführung und Planung. </p>
                          <div class="margin-top margin-small">
                            <div class="career19_job-details-wrapper">
                              <div class="career19_detail-wrapper">
                                <div class="career19_icon-wrapper">
                                  <div class="icon-embed-xsmall w-embed">
                                    <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                                      <path d="M20 10C20 14.4183 12 22 12 22C12 22 4 14.4183 4 10C4 5.58172 7.58172 2 12 2C16.4183 2 20 5.58172 20 10Z" stroke="#000000" stroke-width="1.5"></path>
                                      <path d="M12 11C12.5523 11 13 10.5523 13 10C13 9.44772 12.5523 9 12 9C11.4477 9 11 9.44772 11 10C11 10.5523 11.4477 11 12 11Z" fill="#000000" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                  </div>
                                </div>
                                <div class="text-size-medium">Zürich</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="card-row4_card initiativ-card">
                          <div class="initiaitv-card-content">
                            <div class="card-top initiativ-top">
                              <div class="max-width-medium">
                                <p class="heading-style-h5">Interesse nach mehr? Jetzt Initiativbewerbung senden</p>
                                <a href="mailto:<EMAIL>" class="footer-email w-inline-block">
                                  <div class="layout128_item-icon-wrapper">
                                    <div class="icon-embed-medium w-embed"><svg width="50" height="50" viewbox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.583 25L24.9997 32.2917L35.4163 25" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M4.16699 41.6669V19.026C4.16699 17.5624 4.93491 16.2062 6.18993 15.4531L22.8566 5.45314C24.1762 4.66145 25.8245 4.66145 27.1441 5.45316L43.8107 15.4532C45.0657 16.2062 45.8337 17.5624 45.8337 19.026V41.6669C45.8337 43.9682 43.9682 45.8336 41.667 45.8336H8.33366C6.03247 45.8336 4.16699 43.9682 4.16699 41.6669Z" stroke="black" stroke-width="1.5"></path>
                                      </svg></div>
                                  </div>
                                  <p class="footer-contact-text"><EMAIL></p>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div data-w-tab="Tab 2" class="career19_tab-pane w-tab-pane">
                    <div class="career19_list-wrapper">
                      <div class="career19_list">
                        <div id="w-node-_3c6d7a87-b462-26fd-19f7-86847e11baea-182d6528" data-w-id="3c6d7a87-b462-26fd-19f7-86847e11baea" class="career19_item">
                          <div class="margin-bottom margin-medium">
                            <div class="career19_top-wrapper">
                              <div class="career19_title-wrapper">
                                <div class="heading-style-h5">Bauleiter:in (80-100%)</div>
                              </div>
                              <div class="button-group">
                                <a href="jobs/bauleiter-in-80-100.html" class="button job-button w-inline-block">
                                  <div>Jetzt bewerben</div>
                                </a>
                              </div>
                            </div>
                          </div>
                          <p class="text-size-regular">Projektarbeit über alle Phasen von der Kostenermittlung, über die Ausschreibung und Angebotsverhandlung bis hin zur Übergabe von mängelfreien Bauwerken. </p>
                          <div class="margin-top margin-small">
                            <div class="career19_job-details-wrapper">
                              <div class="career19_detail-wrapper">
                                <div class="career19_icon-wrapper">
                                  <div class="icon-embed-xsmall w-embed">
                                    <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                                      <path d="M20 10C20 14.4183 12 22 12 22C12 22 4 14.4183 4 10C4 5.58172 7.58172 2 12 2C16.4183 2 20 5.58172 20 10Z" stroke="#000000" stroke-width="1.5"></path>
                                      <path d="M12 11C12.5523 11 13 10.5523 13 10C13 9.44772 12.5523 9 12 9C11.4477 9 11 9.44772 11 10C11 10.5523 11.4477 11 12 11Z" fill="#000000" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                  </div>
                                </div>
                                <div class="text-size-medium">Zürich</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="card-row4_card initiativ-card">
                          <div class="initiaitv-card-content">
                            <div class="card-top initiativ-top">
                              <div class="max-width-medium">
                                <p class="heading-style-h5">Interesse nach mehr? Jetzt Initiativbewerbung senden</p>
                                <a href="mailto:<EMAIL>" class="footer-email w-inline-block">
                                  <div class="layout128_item-icon-wrapper">
                                    <div class="icon-embed-medium w-embed"><svg width="50" height="50" viewbox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.583 25L24.9997 32.2917L35.4163 25" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M4.16699 41.6669V19.026C4.16699 17.5624 4.93491 16.2062 6.18993 15.4531L22.8566 5.45314C24.1762 4.66145 25.8245 4.66145 27.1441 5.45316L43.8107 15.4532C45.0657 16.2062 45.8337 17.5624 45.8337 19.026V41.6669C45.8337 43.9682 43.9682 45.8336 41.667 45.8336H8.33366C6.03247 45.8336 4.16699 43.9682 4.16699 41.6669Z" stroke="black" stroke-width="1.5"></path>
                                      </svg></div>
                                  </div>
                                  <p class="footer-contact-text"><EMAIL></p>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div data-w-tab="Tab 3" class="career19_tab-pane w-tab-pane">
                    <div class="career19_list-wrapper">
                      <div class="career19_list">
                        <div id="w-node-dac3e1a6-8a42-3bde-1edd-d1715a74de91-182d6528" class="career19_item">
                          <div class="margin-bottom margin-medium">
                            <div class="career19_top-wrapper">
                              <div class="career19_title-wrapper">
                                <div class="heading-style-h5">Bau-/Projektleiter:in (80-100%)</div>
                              </div>
                              <div class="button-group">
                                <a href="jobs/bau--projektleiter-in-80-100.html" class="button job-button w-inline-block">
                                  <div>Jetzt bewerben</div>
                                </a>
                              </div>
                            </div>
                          </div>
                          <p class="text-size-regular">Adäquate Betreuung von Bauprojekten ab Kostenermittlung bis zur Fertigstellung. Du bist die Schlüsselstelle im Projekt und koordinierst zwischen Ausführung und Planung. </p>
                          <div class="margin-top margin-small">
                            <div class="career19_job-details-wrapper">
                              <div class="career19_detail-wrapper">
                                <div class="career19_icon-wrapper">
                                  <div class="icon-embed-xsmall w-embed">
                                    <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                                      <path d="M20 10C20 14.4183 12 22 12 22C12 22 4 14.4183 4 10C4 5.58172 7.58172 2 12 2C16.4183 2 20 5.58172 20 10Z" stroke="#000000" stroke-width="1.5"></path>
                                      <path d="M12 11C12.5523 11 13 10.5523 13 10C13 9.44772 12.5523 9 12 9C11.4477 9 11 9.44772 11 10C11 10.5523 11.4477 11 12 11Z" fill="#000000" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                  </div>
                                </div>
                                <div class="text-size-medium">Zürich</div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div class="card-row4_card initiativ-card">
                          <div class="initiaitv-card-content">
                            <div class="card-top initiativ-top">
                              <div class="max-width-medium">
                                <p class="heading-style-h5">Interesse nach mehr? Jetzt Initiativbewerbung senden</p>
                                <a href="mailto:<EMAIL>" class="footer-email w-inline-block">
                                  <div class="layout128_item-icon-wrapper">
                                    <div class="icon-embed-medium w-embed"><svg width="50" height="50" viewbox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.583 25L24.9997 32.2917L35.4163 25" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                        <path d="M4.16699 41.6669V19.026C4.16699 17.5624 4.93491 16.2062 6.18993 15.4531L22.8566 5.45314C24.1762 4.66145 25.8245 4.66145 27.1441 5.45316L43.8107 15.4532C45.0657 16.2062 45.8337 17.5624 45.8337 19.026V41.6669C45.8337 43.9682 43.9682 45.8336 41.667 45.8336H8.33366C6.03247 45.8336 4.16699 43.9682 4.16699 41.6669Z" stroke="black" stroke-width="1.5"></path>
                                      </svg></div>
                                  </div>
                                  <p class="footer-contact-text"><EMAIL></p>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_layout121">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="layout121_component">
                <div class="w-layout-grid layout121_content">
                  <div class="layout121_content-left">
                    <div class="sticke-column">
                      <h1 class="heading-style-h1 text-color-black bewerben-fixed">Bewerben einfach gemacht</h1>
                    </div>
                  </div>
                  <div class="layout121_content-right">
                    <div data-w-id="7b7abd60-00db-c416-db1c-a47ff2ee95b0" class="layout121_timeline-wrapper">
                      <div class="layout121_progress-bar-wrapper">
                        <div class="layout121_progress-bar"></div>
                      </div>
                      <div class="layout121_timeline-step-wrapper">
                        <div data-w-id="7b7abd60-00db-c416-db1c-a47ff2ee95b4" class="w-layout-grid layout121_timeline-step">
                          <div class="layout121_timeline-left">
                            <div class="layout121_timeline-icon-wrapper">
                              <div class="icon-1x1-medium step-icon">
                                <h3 class="heading-style-h4 text-color-white">1.</h3>
                              </div>
                            </div>
                          </div>
                          <div class="layout121_timeline-right">
                            <div class="margin-bottom">
                              <h3 class="heading-style-h4">Bewerbung online einreichen </h3>
                            </div>
                            <p class="text-size-regular no-hyphen">Lebenslauf &amp; ein paar Zeilen zu deiner Motivation</p>
                          </div>
                        </div>
                        <div data-w-id="7b7abd60-00db-c416-db1c-a47ff2ee95be" class="w-layout-grid layout121_timeline-step">
                          <div class="layout121_timeline-left">
                            <div class="layout121_timeline-icon-wrapper">
                              <div class="icon-1x1-medium step-icon">
                                <h3 class="heading-style-h4 text-color-white">2.</h3>
                              </div>
                            </div>
                          </div>
                          <div class="layout121_timeline-right">
                            <div class="margin-bottom">
                              <h3 class="heading-style-h4">Rückmeldung</h3>
                            </div>
                            <p class="text-size-regular no-hyphen">Wir melden uns subito</p>
                          </div>
                        </div>
                        <div data-w-id="7b7abd60-00db-c416-db1c-a47ff2ee95c8" class="w-layout-grid layout121_timeline-step">
                          <div class="layout121_timeline-left">
                            <div class="layout121_timeline-icon-wrapper">
                              <div class="icon-1x1-medium step-icon">
                                <h3 class="heading-style-h4 text-color-white">3.</h3>
                              </div>
                            </div>
                          </div>
                          <div class="layout121_timeline-right">
                            <div class="margin-bottom">
                              <h3 class="heading-style-h4">Erstgespräch</h3>
                            </div>
                            <p class="text-size-regular no-hyphen">Wir treffen uns zum unkomplizierten Gespräch </p>
                          </div>
                        </div>
                        <div data-w-id="7b7abd60-00db-c416-db1c-a47ff2ee95d2" class="w-layout-grid layout121_timeline-step">
                          <div class="layout121_timeline-left">
                            <div class="layout121_timeline-icon-wrapper">
                              <div class="icon-1x1-medium step-icon">
                                <h3 class="heading-style-h4 text-color-white">4.</h3>
                              </div>
                            </div>
                          </div>
                          <div class="layout121_timeline-right">
                            <div class="margin-bottom">
                              <h3 class="heading-style-h4">Meet the Team </h3>
                            </div>
                            <p class="text-size-regular no-hyphen">Du kommst zum Zweitgespräch und triffst andere vom Team </p>
                          </div>
                        </div>
                        <div data-w-id="f39eb8bb-6a6d-2060-a293-8f6136b4e5f4" class="w-layout-grid layout121_timeline-step">
                          <div class="layout121_timeline-left">
                            <div class="layout121_timeline-icon-wrapper">
                              <div class="icon-1x1-medium step-icon">
                                <h3 class="heading-style-h4 text-color-white">5.</h3>
                              </div>
                            </div>
                          </div>
                          <div class="layout121_timeline-right">
                            <div class="margin-bottom">
                              <h3 class="heading-style-h4">Entscheidung &amp; Einstieg </h3>
                            </div>
                            <p class="text-size-regular no-hyphen">Klarer Fahrplan, individueller Start</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section data-animate-on-load="true" class="section_layout408">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large padding-top">
              <div class="margin-bottom">
                <div class="div-block-12">
                  <div class="max-width-large">
                    <div class="margin-bottom margin-small">
                      <h1 class="heading-style-h1 text-color-black bewerben-fixed">Bewerben einfach gemacht</h1>
                    </div>
                  </div>
                </div>
              </div>
              <div data-w-id="07826999-f368-156f-2c7f-2d75d350807a" class="w-layout-grid layout408_component">
                <div class="layout408_card card-slide-1">
                  <div id="w-node-b2a8dd2e-498b-3f37-4044-17bca4926e5a-182d6528" class="layout408_image-wrapper"><img loading="lazy" src="images/Wölfli-53.avif" alt="" class="layout408_image"></div>
                  <div class="layout408_card-content">
                    <div class="layout408_card-content-top">
                      <div class="margin-bottom">
                        <h3 class="heading-style-h3">1. Bewerbung online einreichen</h3>
                      </div>
                      <p class="text-size-regular">Lebenslauf &amp; ein paar Zeilen zu deiner Motivation</p>
                    </div>
                  </div>
                </div>
                <div class="layout408_card card-slide-2">
                  <div id="w-node-_07826999-f368-156f-2c7f-2d75d3508091-182d6528" class="layout408_image-wrapper"><img loading="lazy" src="images/Wölfli-21.avif" alt="" class="layout408_image"></div>
                  <div class="layout408_card-content">
                    <div class="layout408_card-content-top">
                      <div class="margin-bottom">
                        <h3 class="heading-style-h3">2. Rückmeldung</h3>
                      </div>
                      <p class="text-size-regular">Wir melden uns subito</p>
                    </div>
                  </div>
                </div>
                <div class="layout408_card card-slide-3">
                  <div class="layout408_card-content">
                    <div class="layout408_card-content-top">
                      <div class="margin-bottom">
                        <h3 class="heading-style-h3">3. Erstgespräch</h3>
                      </div>
                      <p class="text-size-regular">Wir treffen uns zum unkomplizierten Gespräch </p>
                    </div>
                  </div>
                  <div class="layout408_image-wrapper"><img sizes="(max-width: 1800px) 100vw, 1800px" srcset="images/Wölfli-37-p-500.webp 500w, images/Wölfli-37-p-800.webp 800w, images/Wölfli-37-p-1080.webp 1080w, images/Wölfli-37-p-1600.webp 1600w, images/Wölfli-37.webp 1800w" alt="" src="images/Wölfli-37.webp" loading="lazy" class="layout408_image"></div>
                </div>
                <div class="layout408_card card-slide-4">
                  <div class="layout408_image-wrapper"><img sizes="(max-width: 2000px) 100vw, 2000px" srcset="images/Wölfli-32-p-500.webp 500w, images/Wölfli-32-p-800.webp 800w, images/Wölfli-32-p-1080.webp 1080w, images/Wölfli-32-p-1600.webp 1600w, images/Wölfli-32.webp 2000w" alt="" src="images/Wölfli-32.webp" loading="lazy" class="layout408_image"></div>
                  <div class="layout408_card-content">
                    <div class="layout408_card-content-top">
                      <div class="margin-bottom">
                        <h3 class="heading-style-h3">4. Meet the Team </h3>
                      </div>
                      <p class="text-size-regular">Du kommst zum Zweitgespräch und triffst andere vom Team </p>
                    </div>
                  </div>
                </div>
                <div class="layout408_card card-slide-5">
                  <div class="layout408_card-content">
                    <div class="layout408_card-content-top">
                      <div class="margin-bottom">
                        <h3 class="heading-style-h3">5. Entscheidung &amp; Einstieg  </h3>
                      </div>
                      <p class="text-size-regular">Klarer Fahrplan, individueller Start</p>
                    </div>
                  </div>
                  <div class="layout408_image-wrapper"><img loading="lazy" src="images/IMG_4127.avif" alt="" class="layout408_image"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="bieten-sektion">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large padding-bottom">
              <div class="bieten-section-heading">
                <div class="margin-bottom">
                  <div class="max-width-full">
                    <div class="headline-tag-group">
                      <div blocks-name="tagline" class="text-style-tagline">Kernkompetenzen</div>
                      <div class="after-header-headline">
                        <h1 class="heading-style-h1 text-color-black">Was wir bieten</h1>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="w-layout-grid layout242_list">
                  <div class="layout242_item">
                    <div class="margin-bottom margin-small">
                      <h4 class="heading-style-h2">Entwicklungsmöglichkeiten</h4>
                      <h6 class="text-size-medium">Fachliche Weiterbildung</h6>
                    </div>
                    <div class="bieten-list-content">
                      <div id="w-node-_51d9177a-c709-775a-61a2-3b32f2031b2c-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Persönliche Förderung</p>
                        </div>
                      </div>
                      <div id="w-node-_51d9177a-c709-775a-61a2-3b32f2031b32-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Projektvielfalt im Alltag</p>
                        </div>
                      </div>
                      <div id="w-node-_51d9177a-c709-775a-61a2-3b32f2031b38-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">QS-Prozesse / Struktur</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="layout242_item">
                    <div class="margin-bottom margin-small bieten-top">
                      <h4 class="heading-style-h2">Arbeitsweise</h4>
                      <h6 class="text-size-medium">Eigenverantwortung &amp; Vertrauen</h6>
                    </div>
                    <div class="bieten-list-content">
                      <div id="w-node-_72266d54-5df6-7a64-9088-344c8bc23bb5-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Flexible Arbeitszeiten</p>
                        </div>
                      </div>
                      <div id="w-node-_72266d54-5df6-7a64-9088-344c8bc23bbb-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Homeoffice und Teilzeit möglich<br></p>
                        </div>
                      </div>
                      <div id="w-node-_72266d54-5df6-7a64-9088-344c8bc23bc1-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Klare Zuständigkeiten</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="layout242_item">
                    <div class="margin-bottom margin-small">
                      <h4 class="heading-style-h2">Miteinander</h4>
                      <h6 class="text-size-medium">Flache Hierarchien und kurze Wege</h6>
                    </div>
                    <div class="bieten-list-content">
                      <div id="w-node-aee9266a-f48c-373f-7028-4c86f0a5533d-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Offene, ehrliche Kommunikation</p>
                        </div>
                      </div>
                      <div id="w-node-aee9266a-f48c-373f-7028-4c86f0a55343-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Team-Lunches &amp; gemeinsame Anlässe</p>
                        </div>
                      </div>
                      <div id="w-node-aee9266a-f48c-373f-7028-4c86f0a55349-182d6528" class="list1_item">
                        <div class="layout65_item-icon-wrapper">
                          <div class="icon-embed-xsmall w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000" stroke-width="1.5">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M12 1.25C6.06294 1.25 1.25 6.06294 1.25 12C1.25 17.9371 6.06294 22.75 12 22.75C17.9371 22.75 22.75 17.9371 22.75 12C22.75 6.06294 17.9371 1.25 12 1.25ZM7.53044 11.9697C7.23755 11.6768 6.76268 11.6768 6.46978 11.9697C6.17689 12.2626 6.17689 12.7374 6.46978 13.0303L9.46978 16.0303C9.76268 16.3232 10.2376 16.3232 10.5304 16.0303L17.5304 9.03033C17.8233 8.73744 17.8233 8.26256 17.5304 7.96967C17.2375 7.67678 16.7627 7.67678 16.4698 7.96967L10.0001 14.4393L7.53044 11.9697Z" fill="#000000"></path>
                            </svg>
                          </div>
                        </div>
                        <div class="layout65_item-text-wrapper">
                          <p class="text-size-medium">Wertschätzendes Arbeitsumfeld</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="entwicklung-sektion">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large padding-bottom">
              <div class="card-row1_component">
                <div class="card-row1_card-content">
                  <div class="card-row1_card-content-top">
                    <div class="margin-bottom margin-small">
                      <h2 class="heading-style-h1">Weiterkommen mit Perspektive</h2>
                    </div>
                    <p class="heading-style-h4 text-color-white">Wir fördern gezielt die Weiterentwicklung unserer Mitarbeitenden – fachlich wie persönlich. <br><br>Vom Trainee zur Projektleiter:in? Kein Problem.<br><br>Du willst dich auf Bauökonomie spezialisieren oder vertieft in BIM einsteigen? Wir unterstützen dich!<br><br>Dein Weg ist uns wichtig – und wir gehen ihn mit dir gemeinsam.</p>
                  </div>
                </div>
                <div class="card-row1_image-wrapper"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 741 475" fill="none">
                    <path d="M738.193 225.121C738.292 226.378 738.271 227.641 738.13 228.894L737.562 233.966C737.505 234.475 737.408 234.98 737.272 235.475V235.475C737.014 236.411 736.619 237.303 736.099 238.123L735.917 238.41L734.105 240.6C732.974 241.966 732.425 243.722 732.576 245.489V245.489C732.622 246.025 732.732 246.555 732.904 247.065L733.587 249.101C733.739 249.553 733.845 250.019 733.903 250.492L733.942 250.806C734.123 252.272 733.778 253.756 732.967 254.992L732.902 255.09C732.465 255.757 731.893 256.324 731.222 256.755L730.76 257.052C729.921 257.592 729.301 258.413 729.012 259.369V259.369C728.871 259.838 728.812 260.328 728.84 260.817L728.903 261.95C728.935 262.518 728.922 263.088 728.862 263.654L728.79 264.344C728.661 265.566 728.287 266.75 727.688 267.823L727.478 268.199C727.174 268.743 726.79 269.24 726.339 269.67L725.989 270.004C725.496 270.474 725.124 271.056 724.903 271.699V271.699C724.615 272.54 724.597 273.45 724.853 274.302L725.376 276.043C725.677 277.045 726.184 277.973 726.865 278.767V278.767C728.107 280.214 729.854 281.133 731.75 281.338L733.584 281.535C735.017 281.69 736.371 282.269 737.473 283.198V283.198C738.601 284.15 739.412 285.425 739.795 286.851L740.058 287.828C740.314 288.78 740.434 289.762 740.415 290.747L740.369 293.136C740.339 294.657 740.159 296.172 739.831 297.657L739.439 299.434C739.161 300.693 738.681 301.898 738.016 303.003V303.003L737.76 303.43C737.104 304.52 736.017 305.28 734.769 305.522V305.522C734.108 305.649 733.426 305.626 732.775 305.455L725.232 303.465C723.106 302.904 720.897 302.723 718.708 302.93L717.336 303.061C716.651 303.126 715.961 303.102 715.282 302.991L715.036 302.95C714.483 302.86 713.944 302.694 713.436 302.458V302.458C712.721 302.127 712.077 301.662 711.537 301.089L711.32 300.86L710.813 300.155C710.575 299.824 710.302 299.521 709.998 299.25V299.25C709.45 298.763 708.81 298.388 708.117 298.15L704.167 296.789C703.062 296.408 702.215 295.511 701.898 294.386V294.386C701.751 293.863 701.725 293.314 701.823 292.779L702.439 289.419C702.527 288.935 702.543 288.44 702.484 287.951L702.461 287.755C702.326 286.624 701.759 285.589 700.88 284.865V284.865C700.078 284.207 699.071 283.851 698.034 283.86L696.575 283.872C695.807 283.879 695.045 283.991 694.307 284.204L680.306 288.249C680.03 288.328 679.765 288.438 679.514 288.577V288.577C678.738 289.005 678.133 289.687 677.802 290.509L677.602 291.003L676.001 297.181C675.726 298.239 675.25 299.235 674.598 300.112V300.112C674.352 300.442 674.084 300.753 673.793 301.044L671.224 303.618C670.866 303.976 670.545 304.369 670.266 304.79L670.234 304.838C669.606 305.783 669.204 306.861 669.059 307.988V307.988L669.033 312.113C669.028 312.849 668.925 313.581 668.726 314.289V314.289C668.453 315.26 668.361 316.272 668.453 317.276L668.516 317.962L668.763 319.054C668.858 319.476 669.019 319.88 669.241 320.251L669.535 320.743C669.646 320.929 669.796 321.089 669.975 321.211V321.211C670.624 321.654 670.818 322.523 670.421 323.2L669.458 324.839C668.718 326.099 668.831 327.686 669.745 328.827L670.083 329.251C670.556 329.842 671.177 330.299 671.882 330.574L674.404 331.559C675.212 331.876 676.048 332.118 676.901 332.283L682.377 333.347C682.764 333.423 683.143 333.535 683.509 333.683L684.343 334.019C685.272 334.394 686.022 335.112 686.438 336.024V336.024C686.798 336.816 686.885 337.706 686.683 338.553L686.068 341.13C685.863 341.99 685.486 342.798 684.959 343.507V343.507C684.701 343.854 684.408 344.175 684.086 344.465L682.284 346.086C681.52 346.773 680.881 347.588 680.396 348.494V348.494C680.061 349.118 679.803 349.78 679.626 350.466L679.256 351.896C679.127 352.398 679.049 352.916 679.025 353.434V353.434C678.982 354.347 679.106 355.268 679.393 356.136V356.136C679.544 356.594 679.739 357.036 679.976 357.456L680.253 357.948C680.823 358.962 681.525 359.896 682.339 360.728L687.064 365.552C687.602 366.102 688.051 366.732 688.393 367.421L688.579 367.795C689.463 369.573 689.481 371.659 688.63 373.452V373.452C688.104 374.559 687.276 375.494 686.24 376.148L684.424 377.296C683.642 377.79 682.813 378.204 681.949 378.533L677.19 380.346C676.623 380.562 676.018 380.666 675.412 380.654V380.654C672.901 380.603 670.858 378.586 670.794 376.076V376.076C670.78 375.502 670.66 374.937 670.442 374.406L670.033 373.414C669.427 371.941 668.493 370.625 667.303 369.566L663.611 366.28C662.963 365.703 662.377 365.061 661.862 364.363V364.363C660.786 362.905 660.041 361.23 659.676 359.456L659.482 358.508C659.269 357.47 659.154 356.414 659.138 355.354L659.108 353.285C659.098 352.596 658.999 351.91 658.813 351.246V351.246C658.294 349.397 657.125 347.797 655.521 346.74L655.165 346.506C654.332 345.957 653.413 345.548 652.447 345.297L651.722 345.108C650.931 344.902 650.124 344.768 649.309 344.706L649.191 344.697C647.966 344.605 646.734 344.664 645.523 344.873L644.155 345.109C641.529 345.563 638.988 346.414 636.619 347.635L630.007 351.041C629.521 351.292 629.015 351.503 628.495 351.672L627.133 352.117C626.305 352.386 625.421 352.432 624.57 352.248V352.248C624.039 352.133 623.53 351.931 623.064 351.649L620.315 349.987C619.956 349.77 619.547 349.65 619.127 349.64V349.64C618.416 349.623 617.732 349.921 617.262 350.455L616.937 350.823C616.473 351.35 616.18 352.006 616.097 352.703L615.984 353.655C615.93 354.114 615.924 354.578 615.968 355.038L616.156 357.04V357.04C616.112 358.26 615.708 359.439 614.994 360.43L614.725 360.803C613.977 361.843 612.949 362.65 611.762 363.131V363.131C610.925 363.471 610.029 363.64 609.126 363.629L594.052 363.452C592.492 363.434 590.947 363.15 589.483 362.613V362.613C588.419 362.224 587.408 361.704 586.472 361.066L586.087 360.804C584.975 360.046 584.003 359.101 583.215 358.011V358.011C582.622 357.19 582.138 356.295 581.777 355.349L579.55 349.517C579.222 348.658 578.676 347.897 577.967 347.311V347.311C577.717 347.105 577.449 346.922 577.166 346.765L574.43 345.242C574.12 345.069 573.835 344.855 573.582 344.607L573.414 344.442C572.997 344.032 572.704 343.513 572.566 342.945V342.945C572.492 342.638 572.465 342.321 572.485 342.006L572.691 338.857L571.718 316.254C571.712 316.104 571.683 315.955 571.633 315.813L571.536 315.538C571.334 314.964 570.735 314.632 570.142 314.765V314.765C569.979 314.802 569.825 314.872 569.691 314.971L567.999 316.223C567.503 316.591 567.064 317.03 566.698 317.528V317.528C566.322 318.038 566.027 318.603 565.823 319.202L565.663 319.674C565.591 319.884 565.501 320.087 565.393 320.281L565.186 320.651C564.909 321.147 564.471 321.533 563.943 321.744L563.859 321.778C563.296 322.004 562.665 321.994 562.109 321.751V321.751C561.866 321.645 561.643 321.497 561.452 321.314L558.31 318.312C557.825 317.849 557.539 317.216 557.512 316.546V316.546C557.467 315.414 556.689 314.444 555.594 314.152L554.859 313.955C554.411 313.836 553.944 313.804 553.483 313.861L548.917 314.424C547.149 314.642 545.494 315.413 544.189 316.627V316.627C543.443 317.322 542.828 318.145 542.375 319.058L540.901 322.026C540.443 322.947 540.1 323.92 539.879 324.924L539.368 327.237C539.081 328.536 539.243 329.894 539.829 331.089L540.066 331.573C540.351 332.154 540.78 332.653 541.312 333.023V333.023C542.188 333.631 542.769 334.58 542.911 335.637L542.932 335.793C542.983 336.171 542.982 336.554 542.929 336.932L542.732 338.326C542.634 339.016 542.587 339.711 542.591 340.407L542.592 340.606C542.599 342.035 542.785 343.458 543.147 344.841L544.489 349.974C544.7 350.783 544.837 351.609 544.897 352.442V352.442C544.983 353.64 544.91 354.844 544.681 356.022L544.507 356.917C544.177 358.615 543.632 360.265 542.888 361.826L540.567 366.692C539.665 368.582 538.922 370.543 538.343 372.556L537.172 376.633C536.906 377.557 536.514 378.44 536.007 379.256L535.909 379.414C535.491 380.086 534.987 380.7 534.409 381.241L534.353 381.292C533.709 381.896 532.974 382.395 532.176 382.773L531.926 382.891C531.586 383.052 531.262 383.247 530.96 383.472V383.472C530.094 384.115 529.427 384.99 529.035 385.995L528.719 386.807L528.65 387.057C528.189 388.706 527.554 390.301 526.757 391.817V391.817V391.817C526.699 391.928 526.614 392.024 526.509 392.094L517.047 398.506C515.531 399.533 514.23 400.846 513.216 402.37L512.723 403.111C512.059 404.11 511.59 405.227 511.343 406.401V406.401C511.164 407.249 511.103 408.118 511.16 408.983L511.349 411.805C511.402 412.604 511.357 413.407 511.215 414.195L511.09 414.884C510.967 415.567 510.723 416.223 510.37 416.82L510.239 417.043C509.819 417.754 509.225 418.347 508.512 418.765V418.765C508.197 418.95 507.863 419.099 507.515 419.209L506.198 419.625C505.038 419.992 503.952 420.561 502.991 421.306L501.387 422.55C500.643 423.127 500.11 423.932 499.868 424.842V424.842C499.647 425.678 499.683 426.562 499.972 427.377L500.46 428.753C501.664 432.15 500.731 435.937 498.086 438.384L497.274 439.136C497.128 439.271 497.001 439.426 496.897 439.596L496.851 439.672C496.749 439.839 496.678 440.023 496.641 440.215V440.215C496.6 440.426 496.6 440.643 496.642 440.854V440.854C496.679 441.038 496.747 441.215 496.843 441.377L496.907 441.484C497.004 441.647 497.121 441.798 497.254 441.933L497.642 442.328C498.414 443.113 498.989 444.069 499.321 445.118L499.865 446.838C500.069 447.484 500.364 448.097 500.742 448.659L501.061 449.132C501.458 449.723 501.985 450.214 502.602 450.568V450.568C503.036 450.817 503.507 450.994 503.998 451.092L506.123 451.519C506.593 451.614 507.042 451.795 507.446 452.054V452.054C508.055 452.443 508.542 452.995 508.854 453.648L509.273 454.526C509.904 455.851 510.121 457.335 509.894 458.785L509.829 459.198C509.688 460.104 509.402 460.982 508.984 461.798L506.097 467.431C505.72 468.167 505.268 468.861 504.746 469.503L502.935 471.732C501.306 473.736 498.712 474.688 496.174 474.213V474.213C495.633 474.112 495.105 473.947 494.603 473.723L491.15 472.184C490.399 471.849 489.552 471.797 488.766 472.037L486.832 472.628C485.298 473.096 483.806 471.787 484.073 470.205V470.205C484.093 470.084 484.124 469.964 484.165 469.848L485.086 467.223L485.237 466.558C485.575 465.062 485.696 463.525 485.594 461.995V461.995C485.516 460.818 485.307 459.654 484.971 458.523L484.561 457.144L483.188 454.063L480.34 448.982L479.041 445.941C478.834 445.459 478.577 445 478.272 444.573L478.17 444.429C477.833 443.957 477.431 443.534 476.974 443.176V443.176C476.699 442.959 476.405 442.767 476.097 442.601L467.985 438.238C467.431 437.94 466.855 437.685 466.261 437.474L464.881 436.985C464.723 436.929 464.559 436.895 464.392 436.884L462.517 436.762C462.181 436.74 461.968 436.393 462.1 436.083V436.083L462.727 434.949L464.128 431.723C465.111 429.46 466.403 427.344 467.968 425.436L469.571 423.481C470.058 422.887 470.46 422.23 470.768 421.527L470.9 421.223C472.029 418.64 471.677 415.649 469.978 413.399L469.543 412.823C468.901 411.972 468.022 411.33 467.016 410.978V410.978C466.399 410.761 465.749 410.659 465.095 410.674L461.734 410.756C461.278 410.767 460.821 410.741 460.369 410.678L459.162 410.509C458.675 410.441 458.21 410.261 457.804 409.984V409.984C457.386 409.698 457.041 409.317 456.799 408.873L456.23 407.826C456.059 407.511 455.779 407.269 455.443 407.145V407.145L455.391 407.125C454.89 406.94 454.336 406.963 453.853 407.19V407.19C453.68 407.271 453.519 407.377 453.375 407.505L451.319 409.334C451.139 409.494 450.943 409.634 450.733 409.752V409.752C450.175 410.067 449.539 410.217 448.899 410.185L448.313 410.155C447.448 410.111 446.606 409.859 445.859 409.422L441.543 406.896C440.086 406.043 438.548 405.335 436.952 404.781L436.181 404.514V404.514C435.136 403.976 434.244 403.184 433.586 402.21L425.529 390.274C424.666 388.995 423.691 387.795 422.616 386.688V386.688C420.599 384.611 418.251 382.884 415.668 381.578L415.043 381.262L412.249 380.137C411.259 379.738 410.336 379.188 409.513 378.506V378.506C408.344 377.538 407.402 376.323 406.754 374.95L406.709 374.854C406.2 373.775 405.873 372.62 405.741 371.434L405.54 369.629C405.378 368.172 405.364 366.702 405.497 365.242L405.855 361.33L408.516 349.025L409.075 342.177L408.99 335L408.495 331.077C408.375 330.124 408.028 329.214 407.483 328.423V328.423C406.571 327.1 405.166 326.198 403.583 325.919L402.849 325.789C401.856 325.614 400.839 325.634 399.854 325.847L399.172 325.995L394.569 326.989C393.003 327.328 391.495 327.889 390.089 328.657L389.947 328.735C388.492 329.53 387.172 330.55 386.036 331.758L384.794 333.079C384.439 333.456 384.149 333.889 383.935 334.36L383.252 335.861C383.023 336.364 383.061 336.949 383.353 337.418V337.418C383.458 337.588 383.594 337.737 383.752 337.858L383.813 337.904C384.309 338.284 384.608 338.867 384.625 339.492L384.643 340.128C384.658 340.644 384.555 341.157 384.342 341.628L384.046 342.283C383.805 342.817 383.493 343.316 383.118 343.766L382.794 344.158C382.303 344.748 381.748 345.282 381.138 345.75L381.023 345.838C380.458 346.271 379.854 346.652 379.218 346.974L378.846 347.164C376.663 348.272 374.761 349.863 373.286 351.816L368.569 358.057C367.834 359.031 366.95 359.882 365.95 360.581V360.581C364.708 361.448 363.309 362.065 361.831 362.396L358.365 363.172C357.424 363.383 356.508 363.694 355.634 364.1V364.1C354.161 364.783 352.823 365.727 351.686 366.885L350.918 367.667C350.442 368.152 350.005 368.674 349.611 369.229L348.838 370.318C348.653 370.58 348.505 370.866 348.4 371.169L348.136 371.927C347.964 372.422 348.025 372.969 348.304 373.413V373.413C348.394 373.557 348.504 373.686 348.632 373.797L350.113 375.091C351.538 376.336 352.716 377.839 353.585 379.52L355.264 382.769C356.486 385.135 357.191 387.734 357.331 390.393L357.337 390.506C357.494 393.49 356.919 396.466 355.661 399.177V399.177C354.577 401.512 353.012 403.593 351.069 405.283L347.761 408.16C347.157 408.685 346.456 409.087 345.697 409.342V409.342C345.278 409.483 344.844 409.579 344.404 409.626L342.801 409.8C342.066 409.879 341.367 410.161 340.782 410.613V410.613C340.289 410.994 339.892 411.485 339.622 412.046L339.335 412.642C339.022 413.293 338.814 413.989 338.72 414.706L338.506 416.337L338.489 418.988L337.679 425.291C337.643 425.571 337.546 425.839 337.394 426.076V426.076C337.344 426.153 337.3 426.233 337.263 426.317L335.795 429.556C335.749 429.656 335.714 429.76 335.689 429.868V429.868C335.635 430.099 335.532 430.317 335.387 430.506L333.887 432.461C333.667 432.747 333.416 433.008 333.139 433.239V433.239C332.642 433.654 332.068 433.966 331.45 434.157L330.809 434.356L320.739 436.637C319.714 436.87 318.755 437.334 317.936 437.994V437.994C317.196 438.59 316.588 439.332 316.148 440.175L315.413 441.586C315.162 442.068 315.005 442.594 314.951 443.135L314.788 444.78C314.673 445.94 313.724 446.84 312.559 446.894L311.562 446.939C311.345 446.949 311.149 447.074 311.049 447.266V447.266C311.023 447.316 311.004 447.369 310.992 447.424L310.591 449.338C310.532 449.622 310.518 449.913 310.549 450.202L310.591 450.577C310.621 450.852 310.572 451.129 310.45 451.377V451.377C310.318 451.646 310.107 451.866 309.844 452.01L308.776 452.593C308.397 452.801 307.968 452.899 307.536 452.876V452.876C307.344 452.866 307.154 452.833 306.971 452.777L305.967 452.468C304.872 452.132 303.731 451.971 302.586 451.993L302.238 452C300.63 452.03 299.031 451.762 297.521 451.209L292.818 449.485C292.23 449.27 291.599 449.194 290.977 449.265V449.265C290.538 449.315 290.109 449.437 289.71 449.626L289.411 449.767C289.189 449.872 288.956 449.954 288.716 450.012L288.509 450.062C287.983 450.188 287.432 450.161 286.92 449.985L286.175 449.728C285.865 449.621 285.617 449.384 285.496 449.079V449.079C285.465 449.002 285.426 448.928 285.379 448.859L283.299 445.819C282.678 444.911 281.958 444.074 281.153 443.323L280.884 443.072C280.234 442.466 279.518 441.936 278.749 441.492V441.492C277.807 440.948 276.794 440.538 275.739 440.273L262.329 436.908C261.225 436.63 260.058 436.767 259.048 437.292V437.292C258.278 437.692 257.634 438.3 257.191 439.047L256.276 440.587L256.05 440.884C255.045 442.2 253.555 443.058 251.913 443.268V443.268C251.333 443.342 250.766 443.498 250.23 443.731L248.307 444.566C246.878 445.187 245.522 445.966 244.266 446.888L238.238 451.313C237.059 452.178 235.76 452.864 234.381 453.35V453.35C232.29 454.087 230.062 454.347 227.858 454.112L220.388 453.314C219.396 453.208 218.392 453.322 217.449 453.648V453.648C216.97 453.813 216.511 454.031 216.081 454.298L214.076 455.543C212.772 456.352 211.405 457.052 209.986 457.636L204.571 459.864C202.86 460.568 201.08 461.095 199.261 461.435V461.435C197.788 461.71 196.294 461.862 194.796 461.889L193.715 461.909C192.37 461.933 191.025 461.809 189.706 461.538L188.956 461.383C187.433 461.07 185.975 460.502 184.642 459.702V459.702C183.448 458.986 182.37 458.093 181.445 457.054L176.232 451.201C175.735 450.643 175.291 450.04 174.908 449.398V449.398C174.536 448.775 174.221 448.119 173.969 447.438L173.467 446.085L172.314 441.77L170.432 436.867L170.006 435.059C169.899 434.606 169.625 434.21 169.239 433.95V433.95C169.108 433.862 168.99 433.758 168.886 433.64L158.856 422.283C158.252 421.6 157.507 421.054 156.673 420.686L155.119 419.999C154.539 419.742 153.878 419.732 153.29 419.973V419.973C152.992 420.095 152.721 420.278 152.497 420.51L151.837 421.192C151.724 421.309 151.602 421.417 151.473 421.515L151.184 421.735C150.691 422.109 150.068 422.271 149.455 422.184V422.184C149.061 422.129 148.688 421.973 148.372 421.731L147.786 421.285C147.547 421.103 147.39 420.834 147.349 420.537V420.537C147.324 420.36 147.342 420.18 147.399 420.011L147.502 419.711C147.6 419.425 147.645 419.123 147.633 418.821L147.589 417.614C147.585 417.507 147.626 417.404 147.702 417.329V417.329C147.777 417.256 147.818 417.155 147.816 417.051L147.768 414.746L147.498 412.547C147.49 412.476 147.413 412.435 147.348 412.467V412.467C147.255 412.515 147.158 412.41 147.213 412.321L147.768 411.41L149.09 409.798L149.916 408.502C150.131 408.165 150.263 407.781 150.3 407.382V407.382C150.371 406.622 150.093 405.87 149.545 405.339L148.836 404.651C148.226 404.06 147.464 403.649 146.635 403.466L137.62 401.473C135.897 401.092 134.49 399.852 133.895 398.191V398.191C133.589 397.335 133.515 396.414 133.681 395.521L134.936 388.771L137.293 381.747C137.62 380.773 138.02 379.824 138.489 378.91L140.794 374.415C142.542 371.007 142.438 366.945 140.519 363.63V363.63C140.087 362.885 139.572 362.192 138.982 361.564L134.942 357.262C134.353 356.635 133.838 355.942 133.407 355.197L131.914 352.614C131.281 351.518 131.14 350.205 131.528 348.999V348.999C131.708 348.438 131.998 347.917 132.38 347.468L134.685 344.756C135.634 343.64 136.338 342.337 136.751 340.932V340.932C137.11 339.709 137.243 338.431 137.143 337.161L137.11 336.751L137.045 335.921C136.975 335.035 136.685 334.181 136.202 333.436V333.436C135.681 332.632 134.954 331.983 134.096 331.556L133.617 331.319C133.166 331.095 132.692 330.918 132.205 330.791L112.6 325.703C109.877 324.997 107.085 324.595 104.274 324.505L101.403 324.413C98.8556 324.331 96.3072 324.541 93.8075 325.038V325.038C89.3554 325.924 85.1303 327.707 81.3899 330.279L75.946 334.022C74.0464 335.328 71.9316 336.289 69.6984 336.862V336.862C68.4038 337.193 67.0786 337.392 65.7436 337.454L64.473 337.514C63.0715 337.579 61.6852 337.832 60.3508 338.266V338.266C58.3437 338.918 56.4885 339.967 54.8956 341.351L53.7945 342.308C52.5924 343.353 51.5025 344.52 50.5427 345.791L47.3074 350.076L44.643 353.708C44.1184 354.423 43.73 355.228 43.4973 356.084V356.084C43.1513 357.357 43.1603 358.7 43.5232 359.967L43.6464 360.397L44.9944 364.003C45.1012 364.289 45.169 364.587 45.196 364.891V364.891C45.2254 365.222 45.3033 365.547 45.4272 365.856L46.6742 368.961C46.7687 369.196 46.8891 369.42 47.0333 369.628L47.1437 369.788C47.3852 370.137 47.7079 370.422 48.0842 370.619V370.619C48.8027 370.995 49.6546 371.016 50.3912 370.677L51.4563 370.188C51.7827 370.037 52.1577 370.034 52.487 370.177V370.177C52.8826 370.35 53.1624 370.713 53.2286 371.139L53.5712 373.348C53.8231 374.972 53.3113 376.621 52.1844 377.817L50.5386 379.563C50.0029 380.131 49.4038 380.637 48.7531 381.069L43.6816 384.437C40.7345 386.394 38.0753 388.754 35.7808 391.447L31.9957 395.889C31.2493 396.765 30.339 397.486 29.3158 398.013L29.1952 398.075C26.8864 399.264 24.1625 399.338 21.7922 398.278V398.278C20.5906 397.74 19.281 397.487 17.9656 397.539L14.6899 397.668C13.2263 397.726 11.7719 397.926 10.3472 398.265L5.26054 399.478C5.11925 399.511 4.98392 399.566 4.85925 399.641V399.641C3.67334 400.35 2.27272 399.128 2.81383 397.857L3.28436 396.751L4.3482 394.727C4.47452 394.487 4.57172 394.232 4.63773 393.969V393.969C4.89594 392.939 4.65984 391.847 3.99899 391.015L2.61318 389.271C2.30398 388.882 2.03207 388.465 1.80102 388.025L1.07215 386.637C0.665835 385.863 0.543381 384.972 0.726052 384.117V384.117C0.935448 383.137 1.5292 382.282 2.37421 381.744L5.05436 380.038L14.782 375.261C14.8515 375.227 14.9234 375.198 14.9971 375.174L15.0137 375.169C15.1092 375.139 15.2077 375.119 15.3074 375.11V375.11C15.3879 375.102 15.4689 375.102 15.5494 375.108L15.559 375.109C15.6749 375.119 15.7892 375.142 15.8998 375.177L16.6047 375.406C16.8819 375.495 17.1669 375.559 17.4559 375.594L17.4727 375.597C17.8259 375.64 18.183 375.642 18.5366 375.601V375.601C18.8138 375.569 19.0874 375.511 19.3538 375.427L21.5031 374.756C22.1781 374.545 22.7305 374.056 23.0209 373.411V373.411C23.1886 373.038 23.2615 372.63 23.2331 372.223L23.1234 370.65L23.2084 368.087L24.3784 361.367L24.9204 359.514L27.3074 354.156L29.6414 348.916C30.0245 348.056 30.1842 347.114 30.1056 346.175V346.175C30.0395 345.386 29.8064 344.619 29.4216 343.927L28.7217 342.667C28.2458 341.81 27.6693 341.014 27.0044 340.294L26.8825 340.162C26.0304 339.24 25.0563 338.439 23.9873 337.78L17.5344 333.805V333.805C17.1371 333.56 17.0777 333.006 17.4142 332.683L18.261 331.87C18.502 331.639 18.6656 331.338 18.7294 331.01L18.8174 330.558C18.8959 330.154 18.8928 329.739 18.8081 329.337L18.7125 328.882C18.6359 328.519 18.5974 328.148 18.5974 327.777V326.838C18.5974 326.627 18.5804 326.417 18.5466 326.209V326.209C18.5012 325.93 18.4258 325.657 18.3217 325.394L18.0884 324.806L17.8883 324.176C17.785 323.851 17.7324 323.512 17.7324 323.171V323.171C17.7324 322.707 17.8295 322.249 18.0174 321.825L18.0936 321.653C18.2808 321.231 18.5363 320.842 18.8497 320.503L19.4969 319.803C19.9917 319.268 20.4262 318.68 20.7927 318.049L25.5878 309.802C25.8319 309.382 26.1053 308.98 26.4062 308.599L29.0105 305.3C30.5957 303.292 30.5102 300.436 28.8079 298.527L28.6424 298.341C28.0821 297.712 27.713 296.937 27.5787 296.106V296.106C27.3856 294.911 27.6911 293.689 28.4237 292.726L29.1009 291.835C29.3534 291.503 29.6349 291.195 29.9421 290.913L48.1691 274.181C50.4046 272.129 52.8778 270.352 55.5359 268.888L62.1967 265.22C62.918 264.823 63.5979 264.354 64.2262 263.822V263.822C64.6804 263.438 65.1062 263.021 65.5004 262.575L66.3657 261.596C68.2607 259.452 70.5252 257.666 73.0513 256.323L73.9624 255.838C74.435 255.587 74.8877 255.3 75.3164 254.979L75.4218 254.9C76.0721 254.414 76.6571 253.846 77.1622 253.21L77.5604 252.709C78.0158 252.135 78.3624 251.484 78.5828 250.785V250.785C78.7772 250.169 78.8708 249.526 78.86 248.881L78.839 247.633C78.8192 246.461 78.3749 245.335 77.5884 244.466V244.466L77.0309 243.708C76.8271 243.431 76.6671 243.124 76.5566 242.798V242.798C76.3659 242.236 76.3278 241.633 76.4462 241.051L76.5024 240.776L78.3404 235.214C78.832 233.726 79.4596 232.287 80.2154 230.915V230.915L80.5324 230.121V230.121C80.9295 229.127 81.1756 228.079 81.2627 227.012L81.2944 226.623C81.366 225.745 81.3156 224.861 81.1445 223.997V223.997C80.9673 223.101 80.6625 222.236 80.2393 221.427L78.9329 218.93C78.7715 218.622 78.6382 218.299 78.5344 217.967V217.967C78.1939 216.877 78.1818 215.711 78.4996 214.613L79.0591 212.682C79.954 209.592 82.0053 206.967 84.7864 205.351L86.681 204.25C87.8257 203.585 89.0405 203.049 90.3031 202.65L99.7014 199.686C103.901 198.362 107.839 196.317 111.339 193.644L115.833 190.212C116.6 189.625 117.271 188.922 117.819 188.127L119.284 186.005C120.187 184.696 120.347 183.012 119.706 181.556V181.556C119.118 180.221 119.201 178.686 119.929 177.422L120.564 176.32C120.837 175.847 121.172 175.412 121.561 175.027V175.027C122.192 174.404 122.95 173.926 123.784 173.626L125.708 172.935C126.299 172.722 126.77 172.266 127.003 171.683V171.683C127.162 171.283 127.436 170.939 127.79 170.693L127.857 170.647L128.519 170.304C129.551 169.769 130.177 168.682 130.12 167.521V167.521C130.055 166.207 130.863 165.006 132.105 164.572L133.853 163.961C134.561 163.713 135.21 163.322 135.76 162.812L145.311 153.952V153.952C145.552 153.729 145.759 153.473 145.929 153.193L147.4 150.757C148.325 149.226 149.421 147.803 150.665 146.518L152.334 144.793C153.147 143.954 154.028 143.183 154.97 142.491L160.893 138.133C163.245 136.403 164.495 133.551 164.175 130.65V130.65C164.08 129.784 164.123 128.91 164.304 128.058L164.669 126.336C164.939 125.06 165.564 123.886 166.471 122.949L167.399 121.991C167.896 121.477 168.554 121.148 169.264 121.058L169.37 121.045C169.562 121.021 169.75 120.972 169.93 120.901V120.901C170.33 120.743 170.677 120.475 170.931 120.129L172.214 118.388C172.687 117.746 173.286 117.206 173.974 116.802V116.802C174.418 116.541 174.895 116.339 175.392 116.202L175.709 116.115C176.115 116.002 176.502 115.83 176.856 115.603L177.049 115.48C177.582 115.138 178.009 114.655 178.282 114.084V114.084C178.809 112.981 178.704 111.68 178.005 110.676L177.7 110.239C177.516 109.975 177.303 109.732 177.066 109.516L174.965 107.602L174.553 107.264C173.316 106.253 171.604 106.05 170.165 106.746V106.746C169.73 106.956 169.261 107.088 168.781 107.135L152.94 108.7C151.514 108.841 150.384 107.516 150.748 106.129V106.129C150.79 105.973 150.849 105.821 150.926 105.679L152.298 103.124C152.662 102.447 153.078 101.801 153.544 101.19L154.473 99.9714C154.998 99.282 155.652 98.7009 156.399 98.2602L157.856 97.3998C158.877 96.7973 159.529 95.725 159.595 94.5417V94.5417C159.683 92.9572 160.812 91.624 162.361 91.2769L163.323 91.0613C163.804 90.9534 164.267 90.7727 164.693 90.5255L165.717 89.9327C166.378 89.55 167.002 89.1079 167.583 88.6118L168.657 87.6936C168.985 87.414 169.265 87.0839 169.488 86.7158V86.7158C170.063 85.7664 170.221 84.6223 169.926 83.5524L169.741 82.8819C169.642 82.5205 169.506 82.17 169.337 81.8355L168.09 79.3677C167.962 79.1139 167.875 78.8412 167.832 78.56V78.56C167.635 77.2507 168.426 75.9943 169.692 75.6064L169.997 75.5132C170.356 75.403 170.692 75.2272 170.987 74.9947L171.462 74.6208C171.816 74.3423 172.223 74.1401 172.659 74.027V74.027C173.193 73.8883 173.754 73.8875 174.288 74.0247L174.305 74.029C174.741 74.1408 175.149 74.34 175.506 74.6143L175.622 74.7035C176.17 75.1251 176.817 75.399 177.501 75.4988L179.093 75.7312C180.166 75.8877 181.256 75.8723 182.324 75.6854L186.764 74.9084C188.164 74.6634 189.598 74.6871 190.989 74.9783L191.625 75.1113C192.369 75.2671 193.084 75.5406 193.742 75.9216L194.618 76.4283C194.794 76.5302 194.987 76.5991 195.188 76.6315V76.6315L196.756 76.883C196.965 76.9165 197.178 76.9227 197.389 76.9014V76.9014C199.244 76.7138 200.616 78.5905 199.874 80.301L199.031 82.246C198.716 82.9735 198.643 83.7834 198.825 84.5553L198.956 85.1101C199.289 86.528 200.181 87.7511 201.429 88.5023L202.254 88.9989C202.774 89.3122 203.33 89.5627 203.91 89.7452L207.457 90.8623C207.718 90.9444 207.961 91.0744 208.174 91.2456V91.2456C208.408 91.4342 208.602 91.669 208.742 91.9355L208.869 92.1768C208.948 92.327 209.039 92.4706 209.141 92.6061L209.263 92.7689C209.516 93.1034 209.851 93.3656 210.237 93.5292V93.5292C210.681 93.7176 211.173 93.7668 211.645 93.6703L211.742 93.6505C211.928 93.6126 212.109 93.5554 212.282 93.4797L216.406 91.6812C217.616 91.1535 218.876 90.7503 220.168 90.4779V90.4779C221.76 90.1423 223.388 90.0078 225.013 90.0776L227.131 90.1685L232.248 90.3881C233.375 90.4365 234.498 90.2429 235.543 89.8205V89.8205V89.8205C237.489 89.0344 239.188 87.7385 240.46 86.0692L240.847 85.5605L241.047 85.2985C241.513 84.6875 241.843 83.9845 242.016 83.2361L242.12 82.7869C242.224 82.3358 242.232 81.8677 242.142 81.4135V81.4135C242.053 80.9664 241.872 80.5428 241.61 80.1699L241.21 79.6005L241.041 79.4163C240.793 79.1474 240.579 78.85 240.402 78.5304L240.019 77.8393C239.9 77.6248 239.836 77.3841 239.833 77.1389V77.1389C239.826 76.4768 240.259 75.8903 240.894 75.7032L241.164 75.6239C241.535 75.5144 241.932 75.5245 242.298 75.6526L243.602 76.1096C244.051 76.267 244.481 76.4761 244.882 76.7327L245.017 76.8195L245.851 77.3532C246.222 77.5907 246.64 77.7469 247.076 77.8115V77.8115C247.999 77.9483 248.934 77.6648 249.626 77.0388L250.888 75.8976C250.99 75.8045 251.083 75.7003 251.163 75.5869V75.5869C251.274 75.4296 251.36 75.2561 251.419 75.0726L251.456 74.9542C251.519 74.7587 251.553 74.5554 251.559 74.3503L251.565 74.1208C251.566 74.0583 251.559 73.9958 251.542 73.9355V73.9355V73.9355C251.447 73.5928 251.293 73.2691 251.087 72.9789L250.405 72.0166C250.288 71.8513 250.193 71.671 250.124 71.4805L249.991 71.1156C249.914 70.903 249.906 70.6714 249.968 70.454V70.454C250.048 70.178 250.235 69.9454 250.487 69.8084L250.596 69.7489C250.884 69.5932 251.217 69.5452 251.536 69.6138L252.191 69.7545C252.548 69.8311 252.919 69.8109 253.265 69.6959V69.6959C253.774 69.5271 254.195 69.1656 254.44 68.6891L254.547 68.4815C254.693 68.1953 254.767 67.8771 254.76 67.5555L254.728 66.0301C254.723 65.8017 254.534 65.6205 254.306 65.6254V65.6254C254.212 65.6274 254.12 65.5972 254.045 65.5399L251.456 63.5503C251.44 63.5378 251.422 63.5275 251.403 63.5196V63.5196C251.239 63.4511 251.207 63.2326 251.344 63.1197L257.296 58.2285V58.2285C257.894 57.7375 258.534 57.3007 259.209 56.9233L260.344 56.2884C261.531 55.6247 262.766 55.0497 264.038 54.5681L267.894 53.1075L267.983 53.0735L276.148 50.6149C276.281 50.5747 276.418 50.5472 276.557 50.5326V50.5326C278.183 50.3618 279.431 51.9446 278.886 53.4859L278.186 55.4658C278.003 55.9832 277.702 56.4512 277.308 56.8329L277.271 56.8685C276.56 57.5567 275.607 57.9376 274.617 57.9289L272.297 57.9085V57.9085C272.189 57.9075 272.152 58.0528 272.248 58.1037L277.966 61.1375L278.7 61.5266C279.79 62.1046 280.974 62.4843 282.197 62.6478V62.6478C283.364 62.8038 284.548 62.761 285.7 62.5213L291.156 61.3855L296.003 60.3763C297.075 60.1532 298.106 59.7634 299.057 59.2213V59.2213C300.148 58.5998 301.118 57.7876 301.921 56.8232L303.317 55.1475L305.169 52.2421C305.81 51.2356 307.112 50.8824 308.174 51.4266V51.4266C308.459 51.5723 308.77 51.6575 309.089 51.6768L318.337 52.2372C318.881 52.2702 319.393 52.507 319.77 52.9004V52.9004C320.041 53.1825 320.23 53.5326 320.317 53.9135L320.708 55.6115L321.004 57.7055C321.022 57.8289 321.055 57.9496 321.104 58.0642V58.0642C321.327 58.5843 321.838 58.9215 322.404 58.9215H340.034C341.881 58.9215 343.723 58.717 345.525 58.3118L349.234 57.4779C349.864 57.3363 350.461 57.0766 350.995 56.7128V56.7128C351.515 56.3583 351.965 55.911 352.322 55.3934L352.51 55.1215L352.636 54.887C352.992 54.2247 353.42 53.604 353.913 53.0362L353.96 52.9817C354.36 52.5207 354.804 52.0996 355.285 51.7245V51.7245C355.695 51.4057 356.13 51.1213 356.586 50.8742L358.696 49.7307C359.204 49.4553 359.739 49.2329 360.293 49.067L361.248 48.7809C362.042 48.5429 362.76 48.0996 363.328 47.4959V47.4959C363.495 47.3186 363.649 47.1286 363.786 46.9276L364.114 46.4493C364.298 46.1801 364.534 45.9497 364.807 45.7714V45.7714C365.094 45.5848 365.415 45.4591 365.752 45.4022L373.068 44.1654C373.748 44.0503 374.437 43.9911 375.128 43.9883V43.9883C376.406 43.9831 377.678 44.1716 378.9 44.5473L380.96 45.1804C381.194 45.2523 381.434 45.3015 381.677 45.3272L382.246 45.3874C382.864 45.4529 383.433 45.7566 383.832 46.2343V46.2343C384.004 46.4403 384.14 46.6738 384.234 46.9249L384.973 48.8905L385.592 50.1485C385.942 50.8595 386.476 51.4637 387.138 51.8983V51.8983C387.534 52.1581 387.97 52.3527 388.427 52.4748L391.205 53.2156C393.033 53.7029 394.915 53.9553 396.806 53.9666L403.946 54.0095C404.282 54.0115 404.617 53.9844 404.947 53.9285V53.9285L408.524 53.3238C409.11 53.2247 409.57 52.7673 409.673 52.1817V52.1817C409.694 52.0627 409.73 51.947 409.78 51.8372L410.304 50.6992C411.164 48.8316 412.526 47.2402 414.239 46.1029L415.193 45.4699C416.141 44.8398 417.229 44.4499 418.363 44.334L418.921 44.2769C420.593 44.1058 422.257 44.6691 423.481 45.8206V45.8206C423.878 46.194 424.22 46.6216 424.498 47.0906L425.752 49.2112C425.936 49.5217 426.142 49.8182 426.369 50.0985V50.0985L426.896 50.7488C427.465 51.4497 428.408 51.7236 429.264 51.4357L429.362 51.4026C429.802 51.2544 430.172 50.9493 430.401 50.5455V50.5455L430.447 50.4661C430.886 49.6929 431.16 48.8364 431.25 47.9515V47.9515C431.283 47.6313 431.291 47.3091 431.276 46.9876L431.252 46.5047C431.227 45.9968 431.245 45.4878 431.307 44.9831L431.377 44.4109C431.404 44.1919 431.408 43.9707 431.39 43.7507V43.7507C431.363 43.4159 431.284 43.0873 431.157 42.7766L431.056 42.5306C430.992 42.3741 430.939 42.213 430.899 42.0486L430.843 41.8187C430.741 41.4055 430.748 40.9732 430.862 40.5633L430.93 40.3172C431.016 40.0076 431.191 39.7301 431.433 39.5192V39.5192C431.579 39.3917 431.747 39.2914 431.929 39.2227L432.394 39.047C432.477 39.0156 432.565 38.9979 432.654 38.9948L432.888 38.9866C433.094 38.9793 433.282 39.1029 433.357 39.2948V39.2948C433.375 39.341 433.386 39.3897 433.389 39.4392L433.462 40.5109C433.477 40.7283 433.605 40.9218 433.8 41.0203V41.0203C434.173 41.2094 434.623 40.9806 434.689 40.5675L435.276 36.9343C435.39 36.2301 434.886 35.5761 434.176 35.5071V35.5071C434.056 35.4954 433.938 35.4661 433.826 35.4201L433.235 35.1775L432.215 34.7569C431.651 34.5247 431.063 34.3558 430.462 34.2534V34.2534C429.546 34.0972 428.611 34.0973 427.695 34.2535L422.899 35.0716C421.714 35.2737 420.566 35.6516 419.492 36.1928V36.1928C418.934 36.4742 418.398 36.7985 417.89 37.1627L416.925 37.8547C416.563 38.1142 416.183 38.3475 415.788 38.5526L415.081 38.9193C414.341 39.3034 413.526 39.5212 412.692 39.5572V39.5572C411.614 39.6038 410.544 39.3443 409.607 38.8086L407.651 37.6915L403.917 34.8358C403.609 34.6 403.246 34.4454 402.863 34.386V34.386C402.71 34.3624 402.56 34.3236 402.415 34.2702L402.126 34.1635C400.992 33.7454 400.425 32.4743 400.873 31.3514V31.3514C401.026 30.9676 401.065 30.5479 400.986 30.1425L400.795 29.1702C400.732 28.8484 400.64 28.5328 400.521 28.2273L400.29 27.6342C400.103 27.1547 400.033 26.6374 400.086 26.1254V26.1254C400.154 25.4605 400.427 24.8331 400.867 24.3294L401.225 23.9188C401.606 23.482 402.061 23.1155 402.569 22.8359L405.323 21.3198C405.742 21.0892 406.131 20.807 406.48 20.4797V20.4797C407.277 19.7319 407.843 18.7715 408.112 17.7121L408.722 15.3059C408.865 14.7412 409.053 14.1886 409.283 13.6533V13.6533C409.56 13.0062 409.899 12.387 410.294 11.8041L411.557 9.94049C411.797 9.58615 412.112 9.28879 412.479 9.06938V9.06938C412.956 8.78468 413.504 8.64156 414.059 8.65671L414.528 8.66953C414.869 8.67881 415.209 8.64984 415.542 8.58316L423.514 6.99008C423.752 6.94259 423.982 6.86271 424.198 6.75269L424.456 6.62102C425.528 6.0749 426.008 4.80283 425.565 3.68456V3.68456C425.138 2.60806 425.566 1.38137 426.57 0.804204L426.765 0.691978C427.201 0.441729 427.699 0.323337 428.201 0.351197L430.671 0.488495C431.231 0.519567 431.779 0.6541 432.289 0.885265L432.915 1.16872C433.934 1.63077 434.647 2.58139 434.805 3.68951V3.68951L434.935 6.36918C434.951 6.68433 434.989 6.99796 435.049 7.30768L435.398 9.10517C435.486 9.55586 435.738 9.95781 436.106 10.2328V10.2328C436.729 10.6989 437.578 10.7247 438.228 10.2971L438.393 10.1888C438.893 9.8599 439.232 9.3356 439.326 8.74432L439.914 5.05319C439.987 4.59557 440.199 4.16956 440.519 3.83427V3.83427C441.127 3.19643 442.052 2.96459 442.888 3.24359V3.24359C443.373 3.40547 443.79 3.72607 444.071 4.15368L444.929 5.46176C445.119 5.75117 445.286 6.05469 445.43 6.36966L446.502 8.7215L447.116 9.69825C447.504 10.3162 448.122 10.7543 448.834 10.916V10.916C449.218 11.0033 449.617 11.0069 450.003 10.9265L450.784 10.7638C451.079 10.7022 451.384 10.7004 451.68 10.7583V10.7583C452.408 10.9007 453.019 11.39 453.318 12.0684L453.515 12.5167C453.776 13.1094 453.765 13.7865 453.485 14.3705V14.3705L451.553 17.2835C451.384 17.5378 451.249 17.8127 451.15 18.1016V18.1016C450.894 18.8525 450.897 19.6675 451.159 20.4164L451.218 20.5864C451.372 21.0265 451.6 21.437 451.892 21.8L452.003 21.9369C452.498 22.5509 452.843 23.2715 453.012 24.0418L453.162 24.7285V24.7285C453.349 25.6488 454.165 26.3056 455.104 26.292L460.545 26.2128C461.172 26.2037 461.499 25.4618 461.082 24.993V24.993C460.911 24.8015 460.854 24.5349 460.929 24.2899L461.429 22.6704C461.931 21.0419 463.471 19.959 465.174 20.037L465.625 20.0577C466.324 20.0898 467.005 20.2909 467.609 20.6438L471.75 23.0631C471.947 23.1783 472.13 23.3159 472.296 23.4731L474.716 25.7709C475.389 26.4102 475.082 27.5415 474.178 27.7528V27.7528C473.646 27.8771 473.271 28.354 473.277 28.9004L473.279 29.0879C473.282 29.4537 473.429 29.8036 473.687 30.0626L475.605 31.9865V31.9865C475.685 32.0671 475.771 32.1417 475.862 32.2097L478.748 34.3717C479.897 35.2328 481.203 35.8634 482.591 36.2288V36.2288C483.858 36.562 485.174 36.6691 486.478 36.5453L489.122 36.2943C490.324 36.1801 491.51 35.9365 492.661 35.5678L494.924 34.842C496.217 34.4275 497.38 33.6834 498.298 32.683L498.43 32.5392C499.139 31.7667 500.013 31.1642 500.988 30.7766V30.7766C502.052 30.3531 503.205 30.1987 504.344 30.3272L520.9 32.1955H527.906C529.192 32.1955 530.388 32.8512 531.079 33.9345V33.9345C531.771 35.0189 532.969 35.6747 534.256 35.6735L537.264 35.6705C537.97 35.6699 538.674 35.75 539.361 35.9095L540.927 36.2725C543.425 36.8518 545.811 37.841 547.986 39.1998L585.966 62.9246C588.342 64.4092 590.015 66.795 590.599 69.5355V69.5355V69.5355C590.804 70.497 591.188 71.4113 591.73 72.2311L593.318 74.6307C594.912 77.0397 597.149 78.9543 599.775 80.1576L600.469 80.4752C601.391 80.8978 602.246 81.4539 603.006 82.1257V82.1257C605.259 84.1171 606.531 86.9918 606.492 89.9985L606.45 93.1095C606.429 94.7116 606.091 96.2936 605.455 97.7642V97.7642C605.037 98.7296 604.495 99.6366 603.844 100.462L599.468 106.006C598.806 106.844 598.288 107.787 597.933 108.795V108.795C597.782 109.227 597.661 109.669 597.571 110.118L596.972 113.132C596.896 113.516 596.788 113.892 596.65 114.258V114.258C596.372 114.995 595.974 115.681 595.472 116.288L592.446 119.947C591.241 121.403 590.155 122.953 589.196 124.581L585.364 131.093L584.432 132.676C583.249 134.688 582.266 136.811 581.5 139.015L581.017 140.406C580.224 142.686 579.763 145.069 579.647 147.481L579.567 149.137C579.477 151.011 579.719 152.887 580.282 154.676V154.676C580.55 155.53 580.89 156.36 581.298 157.157L582.465 159.437L583.137 161.326C583.601 162.633 583.85 164.007 583.873 165.394L583.888 166.307C583.911 167.712 583.603 169.102 582.99 170.366V170.366C582.575 171.22 582.027 172.003 581.366 172.685L579.854 174.245L578.416 176.185C578.109 176.599 577.963 177.111 578.005 177.625V177.625C578.078 178.498 578.679 179.238 579.519 179.486L581.82 180.169L590.257 181.577C591.296 181.751 592.375 181.67 593.379 181.35V181.35C594.565 180.972 595.851 180.935 597.054 181.255V181.255C597.864 181.47 598.706 181.527 599.537 181.424L605.111 180.73C606.28 180.584 607.464 180.6 608.629 180.777V180.777C609.288 180.877 609.939 181.029 610.575 181.23L639.007 190.222C639.939 190.517 640.538 191.424 640.441 192.397L640.385 192.962C640.354 193.279 640.48 193.592 640.722 193.8V193.8C640.919 193.969 641.176 194.053 641.436 194.032L641.485 194.028C641.838 194 642.185 194.147 642.413 194.418V194.418C642.581 194.618 642.674 194.872 642.674 195.133V195.744C642.674 196.011 642.636 196.276 642.561 196.532L642.286 197.463C642.094 198.116 641.972 198.788 641.923 199.467L641.589 204.085L641.241 205.805C641.124 206.38 641.1 206.971 641.17 207.554V207.554C641.279 208.455 641.609 209.315 642.131 210.058L642.633 210.773C643.155 211.515 643.786 212.174 644.505 212.726L645.327 213.358C646.757 214.457 648.366 215.303 650.082 215.859L659.547 218.92C663.158 220.088 666.46 222.055 669.207 224.674L669.754 225.195C670.83 226.221 672.023 227.117 673.309 227.863V227.863C674.479 228.541 675.717 229.092 677.004 229.506L679.984 230.465C681.424 230.929 682.921 231.193 684.432 231.251V231.251V231.251C685.169 231.279 685.906 231.191 686.615 230.988L688.176 230.543C689.244 230.239 690.281 229.831 691.27 229.326L695.402 227.218C696.408 226.704 697.27 225.947 697.908 225.016V225.016C698.374 224.335 698.712 223.575 698.905 222.773L698.916 222.728C699.008 222.344 699.068 221.954 699.094 221.56L699.119 221.185C699.167 220.484 699.278 219.788 699.453 219.107L699.68 218.224C699.873 217.473 700.171 216.753 700.565 216.086L701.54 214.438C702.126 213.447 703.112 212.757 704.243 212.547V212.547C704.823 212.439 705.419 212.46 705.989 212.61L707.754 213.074C708.123 213.171 708.504 213.211 708.885 213.194V213.194C710.86 213.101 712.424 211.492 712.462 209.515L712.463 209.44C712.475 208.849 712.622 208.269 712.895 207.744L714.382 204.882C715.022 203.651 715.805 202.499 716.716 201.452L717.654 200.373C718.864 198.981 720.298 197.801 721.896 196.881L723.886 195.737C724.123 195.6 724.381 195.503 724.65 195.449L724.68 195.443C724.985 195.382 725.299 195.381 725.605 195.441L725.657 195.452C725.921 195.504 726.173 195.606 726.399 195.754L726.919 196.093C726.921 196.095 726.924 196.096 726.926 196.096V196.096C726.933 196.096 726.939 196.101 726.939 196.109V196.122C726.939 196.129 726.942 196.135 726.946 196.14L728.298 197.596C728.522 197.837 728.721 198.099 728.895 198.379L729.864 199.944C731.316 202.288 733.161 204.365 735.317 206.084L737.112 207.514C737.46 207.792 737.779 208.104 738.064 208.446V208.446C738.74 209.258 739.211 210.221 739.435 211.253L739.545 211.757C739.827 213.057 739.807 214.405 739.485 215.696L738.725 218.745C738.205 220.828 738.026 222.981 738.193 225.121V225.121Z" fill="#A4ACAE"></path>
                    <circle cx="441.641" cy="181.444" r="7.57301" fill="#D9D9D9"></circle>
                    <path d="M456.767 159.366C456.977 159.357 457.181 159.436 457.329 159.585L463.179 165.434C463.327 165.582 463.406 165.786 463.397 165.996C463.389 166.205 463.292 166.401 463.133 166.537L457.925 170.954L458.277 174.347C458.3 174.572 458.221 174.795 458.061 174.955L456.364 176.651C456.223 176.792 456.033 176.871 455.834 176.871C455.635 176.871 455.444 176.792 455.304 176.651L451.399 172.747L445.172 178.974C444.879 179.267 444.404 179.267 444.111 178.974C443.818 178.681 443.818 178.207 444.111 177.914L450.339 171.686L446.111 167.459C445.971 167.318 445.891 167.127 445.891 166.928C445.892 166.73 445.971 166.539 446.111 166.398L447.808 164.702L447.871 164.645C448.024 164.523 448.219 164.466 448.416 164.486L451.807 164.837L456.226 159.63L456.28 159.573C456.411 159.447 456.584 159.373 456.767 159.366Z" fill="black"></path>
                    <circle cx="443.081" cy="127.826" r="7.57301" fill="#D9D9D9"></circle>
                    <path d="M458.207 105.748C458.416 105.739 458.62 105.819 458.768 105.967L464.618 111.816C464.766 111.965 464.845 112.169 464.837 112.378C464.828 112.587 464.732 112.783 464.572 112.919L459.364 117.336L459.717 120.729C459.74 120.954 459.66 121.177 459.501 121.337L457.804 123.033C457.663 123.174 457.472 123.253 457.273 123.253C457.074 123.253 456.884 123.174 456.743 123.033L452.839 119.129L446.611 125.356C446.318 125.649 445.844 125.649 445.551 125.356C445.258 125.064 445.258 124.589 445.551 124.296L451.778 118.068L447.551 113.841C447.41 113.7 447.331 113.509 447.331 113.311C447.331 113.112 447.41 112.921 447.551 112.78L449.248 111.084L449.31 111.027C449.463 110.906 449.659 110.848 449.855 110.868L453.247 111.219L457.666 106.012L457.72 105.955C457.851 105.83 458.024 105.756 458.207 105.748Z" fill="black"></path>
                    <circle cx="388.911" cy="95.1302" r="7.57301" fill="#D9D9D9"></circle>
                    <path d="M404.037 73.0524C404.246 73.0439 404.45 73.123 404.599 73.2712L410.448 79.1208C410.596 79.2689 410.675 79.473 410.667 79.6823C410.658 79.8915 410.562 80.0878 410.402 80.2233L405.194 84.6403L405.547 88.0339C405.57 88.2583 405.49 88.4817 405.331 88.6413L403.634 90.3376C403.493 90.4782 403.302 90.5573 403.103 90.5573C402.904 90.5573 402.714 90.4782 402.573 90.3376L398.669 86.4333L392.441 92.6608C392.148 92.9535 391.674 92.9535 391.381 92.6608C391.088 92.368 391.088 91.8932 391.381 91.6003L397.608 85.3727L393.381 81.1452C393.24 81.0046 393.161 80.8137 393.161 80.6149C393.161 80.4162 393.24 80.2252 393.381 80.0846L395.078 78.3884L395.14 78.3317C395.293 78.21 395.489 78.1522 395.685 78.1725L399.077 78.5231L403.496 73.3161L403.55 73.2594C403.681 73.1341 403.854 73.0599 404.037 73.0524Z" fill="black"></path>
                  </svg></div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_testimonial1">
        <div class="padding-global testimonial-padding">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="max-width-xlarge">
                <div class="testimonial1_component">
                  <div class="testimonial1_content">
                    <div class="margin-bottom margin-medium"></div>
                    <h3 class="heading-style-h2">&quot;Bei Wölfli zählt nicht, wo du herkommst – sondern, was du mitbringst und wohin du willst.&quot;</h3>
                    <div class="margin-top margin-medium">
                      <div class="testimonial1_client">
                        <div>
                          <div class="testimonial1_client-image-wrapper"><img sizes="(max-width: 714px) 100vw, 714px" srcset="images/wölflI_profile_avatar-p-500.png 500w, images/wölflI_profile_avatar.png 714w" alt="" src="images/wölflI_profile_avatar.png" loading="lazy" class="testimonial1_client-image"></div>
                        </div>
                        <div>
                          <div class="text-size-medium">Sandro Wölfli</div>
                          <div class="text-size-small text-color-black">Geschäftsführer</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_layout67 text-color-white">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="layout67_component">
                <div class="w-layout-grid layout67_content">
                  <div class="layout67_content-left">
                    <h2 class="heading-style-h1 hyphen">Traineeprogramm – Dein Einstieg ins Baumanagement</h2>
                  </div>
                  <div class="layout67_content-right">
                    <div class="margin-bottom margin-small trainee-text-group">
                      <p class="text-size-regular text-color-white">Du hast dein Studium oder eine Ausbildung im Bauwesen abgeschlossen – aber dir fehlt noch die Erfahrung, um Projekte eigenständig zu leiten?</p>
                      <p class="text-size-regular text-color-white">Unser Traineeprogramm ist dein Sprungbrett.<br>Wir fördern motivierte Talente gezielt, begleiten dich eng durch die ersten Praxisjahre und führen dich Schritt für Schritt in die Projektleitung.<br></p>
                    </div>
                  </div>
                </div>
                <div class="trainee-box akkordion-anim">
                  <div blocks-name="layout497_tab-title" blocks-slot-children="ST265" class="trainee-box-content">
                    <div class="trainee-title">
                      <h4 blocks-non-deletable="true" blocks-name="heading-style-h4-2" blocks-slot-item-canonical="EL127" class="heading-style-h2 text-color-white">Was dich erwartet:</h4>
                    </div>
                    <div class="margin-bottom">
                      <div class="layout67_item-list">
                        <div id="w-node-_3209c964-a010-ef8c-ef24-19293d8d9e14-182d6528" class="layout67_item">
                          <div class="layout67_item-icon-wrapper">
                            <div class="icon-embed-xsmall w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16.061 10.4037L13.9996 17H9.99957L7.93822 10.4037C7.35042 8.52271 7.72374 6.47305 8.93695 4.92013L11.5268 1.60518C11.767 1.2977 12.2322 1.2977 12.4724 1.60518L15.0622 4.92013C16.2754 6.47305 16.6487 8.52271 16.061 10.4037Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M10 20C10 22 12 23 12 23C12 23 14 22 14 20" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M8.49955 12.5C4.99955 15 6.99955 19 6.99955 19L9.99955 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M15.9316 12.5C19.4316 15 17.4316 19 17.4316 19L14.4316 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M12 11C10.8954 11 10 10.1046 10 9C10 7.89543 10.8954 7 12 7C13.1046 7 14 7.89543 14 9C14 10.1046 13.1046 11 12 11Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg></div>
                          </div>
                          <div class="layout67_item-text-wrapper">
                            <p class="heading-style-h4 text-color-white">Mitarbeit an echten Bauprojekten ab dem ersten Tag</p>
                          </div>
                        </div>
                        <div id="w-node-_3209c964-a010-ef8c-ef24-19293d8d9e1a-182d6528" class="layout67_item">
                          <div class="layout67_item-icon-wrapper">
                            <div class="icon-embed-xsmall w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16.061 10.4037L13.9996 17H9.99957L7.93822 10.4037C7.35042 8.52271 7.72374 6.47305 8.93695 4.92013L11.5268 1.60518C11.767 1.2977 12.2322 1.2977 12.4724 1.60518L15.0622 4.92013C16.2754 6.47305 16.6487 8.52271 16.061 10.4037Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M10 20C10 22 12 23 12 23C12 23 14 22 14 20" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M8.49955 12.5C4.99955 15 6.99955 19 6.99955 19L9.99955 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M15.9316 12.5C19.4316 15 17.4316 19 17.4316 19L14.4316 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M12 11C10.8954 11 10 10.1046 10 9C10 7.89543 10.8954 7 12 7C13.1046 7 14 7.89543 14 9C14 10.1046 13.1046 11 12 11Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg></div>
                          </div>
                          <div class="layout67_item-text-wrapper">
                            <p class="heading-style-h4 text-color-white">Unterstützung durch erfahrene Kolleg:innen</p>
                          </div>
                        </div>
                        <div id="w-node-_3209c964-a010-ef8c-ef24-19293d8d9e20-182d6528" class="layout67_item">
                          <div class="layout67_item-icon-wrapper">
                            <div class="icon-embed-xsmall w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16.061 10.4037L13.9996 17H9.99957L7.93822 10.4037C7.35042 8.52271 7.72374 6.47305 8.93695 4.92013L11.5268 1.60518C11.767 1.2977 12.2322 1.2977 12.4724 1.60518L15.0622 4.92013C16.2754 6.47305 16.6487 8.52271 16.061 10.4037Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M10 20C10 22 12 23 12 23C12 23 14 22 14 20" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M8.49955 12.5C4.99955 15 6.99955 19 6.99955 19L9.99955 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M15.9316 12.5C19.4316 15 17.4316 19 17.4316 19L14.4316 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M12 11C10.8954 11 10 10.1046 10 9C10 7.89543 10.8954 7 12 7C13.1046 7 14 7.89543 14 9C14 10.1046 13.1046 11 12 11Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg></div>
                          </div>
                          <div class="layout67_item-text-wrapper">
                            <p class="heading-style-h4 text-color-white">Regelmässige Entwicklungsgespräche &amp; individuelles Mentoring</p>
                          </div>
                        </div>
                        <div id="w-node-_8c387e4f-2224-9aea-8be6-fb1a9e93ffc9-182d6528" class="layout67_item">
                          <div class="layout67_item-icon-wrapper">
                            <div class="icon-embed-xsmall w-embed"><svg width="24" height="24" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M16.061 10.4037L13.9996 17H9.99957L7.93822 10.4037C7.35042 8.52271 7.72374 6.47305 8.93695 4.92013L11.5268 1.60518C11.767 1.2977 12.2322 1.2977 12.4724 1.60518L15.0622 4.92013C16.2754 6.47305 16.6487 8.52271 16.061 10.4037Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M10 20C10 22 12 23 12 23C12 23 14 22 14 20" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M8.49955 12.5C4.99955 15 6.99955 19 6.99955 19L9.99955 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M15.9316 12.5C19.4316 15 17.4316 19 17.4316 19L14.4316 17" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                <path d="M12 11C10.8954 11 10 10.1046 10 9C10 7.89543 10.8954 7 12 7C13.1046 7 14 7.89543 14 9C14 10.1046 13.1046 11 12 11Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg></div>
                          </div>
                          <div class="layout67_item-text-wrapper">
                            <p class="heading-style-h4 text-color-white">Nach ca. 1–2 Jahren: Übernahme eigener Projekte und Ablösung des Junior-Titels</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="trainee-text-block">
                      <p class="heading-style-h4 text-color-white">Dein Weg bei uns ist kein Schema F – sondern dein persönlicher Fortschritt.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="layout67_background-image-wrapper">
          <div class="image-overlay-layer"></div><img sizes="(max-width: 1800px) 100vw, 1800px" srcset="images/Wölfli-52-p-500.webp 500w, images/Wölfli-52-p-800.webp 800w, images/Wölfli-52-p-1080.webp 1080w, images/Wölfli-52-p-1600.webp 1600w, images/Wölfli-52.webp 1800w" alt="" src="images/Wölfli-52.webp" loading="lazy" class="layout67_background-image">
        </div>
      </section>
      <section class="fragen-sektion">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div blocks-name="layout364_card" class="karriere-card">
                <div blocks-name="layout364_card-content" blocks-slot-children="ST265" class="karriere-card-content">
                  <div blocks-slot-children="ST265" blocks-name="layout364_card-content-top" class="karriere-card-top">
                    <div class="karriere-card-title">
                      <h4 blocks-non-deletable="true" blocks-name="heading-2" blocks-slot-item-canonical="EL125" class="heading-style-h1 text-color-black">Noch Fragen?</h4>
                    </div>
                    <p blocks-slot-item-canonical="EL10" blocks-name="paragraph-2" class="heading-style-h4">Melde dich bei uns:</p>
                    <div class="after-intro-content">
                      <div blocks-name="max-width-xlarge" blocks-slot-children="ST265" class="max-width-full">
                        <div blocks-non-deletable="true" blocks-name="header105_heading-wrapper" class="kontakt-intro-wrapper">
                          <div class="w-layout-grid layout128_content">
                            <div class="karriere-card-item">
                              <div class="contact-block-icon">
                                <div class="layout128_item-icon-wrapper">
                                  <div class="icon-embed-medium w-embed"><svg width="46" height="46" viewbox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M34.7266 28.179L26.8334 29.7085C21.5018 27.0324 18.2084 23.9585 16.2917 19.1668L17.7674 11.2508L14.9779 3.8335H7.78861C5.62747 3.8335 3.92564 5.61941 4.24841 7.7563C5.05419 13.091 7.43006 22.7635 14.375 29.7085C21.6683 37.0018 32.1726 40.1666 37.9539 41.4245C40.1864 41.9104 42.1667 40.1687 42.1667 37.884V31.0141L34.7266 28.179Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg></div>
                                </div>
                              </div>
                              <div class="contact-content-block">
                                <p class="text-size-regular">Telefon</p>
                                <a href="tel:+41445352222" class="contact-link w-inline-block">
                                  <h3 class="heading-style-h4">+41 44 535 22 22</h3>
                                </a>
                              </div>
                            </div>
                            <div class="karriere-card-item">
                              <div class="contact-block-icon">
                                <div class="layout128_item-icon-wrapper">
                                  <div class="icon-embed-medium w-embed"><svg width="50" height="50" viewbox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                      <path d="M14.583 25L24.9997 32.2917L35.4163 25" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                                      <path d="M4.16699 41.6669V19.026C4.16699 17.5624 4.93491 16.2062 6.18993 15.4531L22.8566 5.45314C24.1762 4.66145 25.8245 4.66145 27.1441 5.45316L43.8107 15.4532C45.0657 16.2062 45.8337 17.5624 45.8337 19.026V41.6669C45.8337 43.9682 43.9682 45.8336 41.667 45.8336H8.33366C6.03247 45.8336 4.16699 43.9682 4.16699 41.6669Z" stroke="black" stroke-width="1.5"></path>
                                    </svg></div>
                                </div>
                              </div>
                              <div class="contact-content-block">
                                <p class="text-size-regular">E-Mail</p>
                                <a href="mailto:<EMAIL>" class="contact-link w-inline-block">
                                  <h3 class="heading-style-h4"><EMAIL></h3>
                                </a>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="fortschritt-p">
                      <p blocks-slot-item-canonical="EL10" blocks-name="paragraph-2" class="text-size-regular">Oder komm bei uns im Büro vorbei – wir freuen uns, dich kennenzulernen.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <header class="section_header67 text-color-alternate">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="before-footer-content">
                <div data-w-id="f849abdc-8677-3e74-93c1-c5142c12fd8d">
                  <div data-w-id="f849abdc-8677-3e74-93c1-c5142c12fd8e" class="text-style-tagline text-color-white">Spezialisten im Einsatz</div>
                  <div class="karriere-grid-footer">
                    <h2 class="heading-style-h1">Jetzt bewerben</h2>
                    <div class="bewerben-button-group">
                      <a role="button" href="#stellen-header" class="link-block-4 w-inline-block">
                        <div class="cta-button">
                          <div class="heading-style-h4 cta-text">Alle Stellenangebote ansehen</div>
                          <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                            </svg></div>
                        </div>
                      </a>
                      <a role="button" href="mailto:<EMAIL>" class="link-block-4 w-inline-block">
                        <div class="cta-button">
                          <div class="heading-style-h4 cta-text">Initiativbewerbung schreiben</div>
                          <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                            </svg></div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="header67_background-image-wrapper">
          <div class="image-overlay-layer footer-overlay-bg"></div><img loading="lazy" src="images/20200818_101902815_iOS.avif" alt="" class="header67_background-image">
        </div>
      </header>
      <section class="footer">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large footer-padding">
              <div class="layout367_component">
                <div class="w-layout-grid layout367_grid-list">
                  <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b19-398b1b13" class="w-layout-grid layout367_row">
                    <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b1a-398b1b13" class="layout367_card-large">
                      <div class="layout367_card-small-content footer-top-margin">
                        <div class="footer-column-content-top">
                          <div class="layout367_card-small-content-top">
                            <div class="div-block-10">
                              <h3 class="heading-style-h2 text-weight-medium">Engagement für  Bauprojekte.</h3>
                            </div>
                          </div>
                          <div class="layout367_card-small-content-top small-content-contact">
                            <a href="mailto:<EMAIL>" class="footer-email w-inline-block"><img src="images/footer-mail-icon.svg" loading="lazy" alt="" class="footer-contact-icon hide">
                              <p class="footer-contact-text"><EMAIL></p>
                            </a>
                            <a href="tel:+41445352222" class="footer-phone w-inline-block"><img src="images/footer-phone-icon.svg" loading="lazy" alt="" class="footer-contact-icon hide">
                              <p class="footer-contact-text">+41 44 535 22 22</p>
                            </a>
                          </div>
                        </div>
                        <div class="footer-logos-desktop">
                          <p class="footer-contact-text">Mitgliedschaften</p>
                          <div class="layout367_card-small-content-top footer-logo-grid">
                            <a aria-label="Maneco Logo" href="https://maneco.pro/" target="_blank" class="footer-logo w-inline-block"><img src="images/maneco-logo.svg" loading="lazy" alt="Maneco Logo" class="footer-logo-img"></a>
                            <a aria-label="Future Areas Logo" href="https://www.future-areas.swiss/" target="_blank" class="footer-logo w-inline-block"><img src="images/future-areas-logo.svg" loading="lazy" alt="Future Areas Logo" class="footer-logo-img"></a>
                            <a aria-label="OBS OBD Logo" href="https://www.obs-osd.ch/" target="_blank" class="footer-logo w-inline-block"><img src="images/logo_obs.svg" loading="lazy" alt="OBS OSD Logo" class="footer-logo-img"></a>
                            <a aria-label="Swiss Leaders Logo" href="https://swissleaders.ch/" target="_blank" class="footer-logo w-inline-block"><img src="images/swiss-leaders-logo.svg" loading="lazy" alt="Swiss Leaders Logo" class="footer-logo-img"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b2d-398b1b13" class="layout367_card-large">
                      <div class="layout367_card-large-content">
                        <div class="layout367_card-large-content-top">
                          <div class="margin-bottom margin-xxsmall hide">
                            <div class="tag is-text uppercase text-color-black">Menu</div>
                          </div>
                          <div class="footer13_link-list">
                            <a href="team.html" class="footer_link">Team</a>
                            <a href="leistungen.html" class="footer_link">Leistungen</a>
                            <a href="marken.html" class="footer_link">Marken</a>
                            <a href="projekte.html" class="footer_link">Projekte</a>
                            <a href="karriere.html" aria-current="page" class="footer_link w--current">Karriere</a>
                            <a href="kontakt.html" class="footer_link">Kontakt</a>
                          </div>
                        </div>
                        <div class="layout367_card-large-content-top">
                          <div class="margin-bottom margin-xxsmall hide">
                            <div class="tag is-text uppercase text-color-black">Socials</div>
                          </div>
                          <div class="footer13_link-list social-link-list">
                            <a href="https://www.instagram.com/woelfli_bauplanung" target="_blank" class="footer-link-icon w-inline-block"><img src="images/instagram.svg" loading="lazy" alt="" class="footer-icon">
                              <div class="footer_link footer-social-link">Instagram</div>
                            </a>
                            <a href="https://www.linkedin.com/company/woelfli-bauplanung-gmbh/" target="_blank" class="footer-link-icon w-inline-block"><img src="images/linkedin.svg" loading="lazy" alt="" class="footer-icon">
                              <div class="footer_link footer-social-link">LinkedIn</div>
                            </a>
                          </div>
                        </div>
                      </div>
                      <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b49-398b1b13" class="layout367_card-large-footer desktop-large-footer">
                        <div class="wolf-column-mobile">
                          <a href="index.html" class="footer-logo-link w-inline-block">
                            <div class="logo-wolf-column"><img src="images/woelfli-logo-sw.svg" loading="lazy" alt="Wöfli Logo "></div>
                          </a>
                          <div class="wolf-symbol-footer wolf-mobile-2">
                            <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                          </div>
                        </div>
                        <div class="footer-column">
                          <div class="policy-column policy-mobile">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                          </div>
                          <div class="policy-column policy-desktop">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                          </div>
                          <div class="copyright-column copyright-desktop">
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                        </div>
                        <div class="wolf-symbol-footer wolf-desktop">
                          <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                        </div>
                        <div class="copyright-column copyright-mobile">
                          <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                        </div>
                      </div>
                      <div class="layout367_card-large-footer mobile-large-footer">
                        <div class="woelfli-logo-column">
                          <div class="wolf-column-mobile">
                            <a href="index.html" class="footer-logo-link w-inline-block">
                              <div class="logo-wolf-column"><img src="images/woelfli-logo-sw.svg" loading="lazy" alt="Wöfli Logo "></div>
                            </a>
                            <div class="wolf-symbol-footer wolf-mobile-2">
                              <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                            </div>
                          </div>
                          <div class="wolf-symbol-footer wolf-desktop">
                            <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                          </div>
                        </div>
                        <div class="footer-column footer-column-mobile">
                          <div class="policy-column policy-mobile">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                          <div class="copyright-column copyright-mobile"></div>
                          <div class="policy-column policy-desktop">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                          </div>
                          <div class="copyright-column copyright-desktop">
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layout367_card-large card-mobile">
                      <div class="layout367_card-small-content footer-top-margin">
                        <div class="footer-logos-mobile">
                          <p class="footer-contact-text">Mitgliedschaften</p>
                          <div class="layout367_card-small-content-top footer-logo-grid">
                            <a href="https://maneco.pro/" target="_blank" class="footer-logo w-inline-block"><img src="images/maneco-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="https://www.future-areas.swiss/" target="_blank" class="footer-logo w-inline-block"><img src="images/future-areas-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="#" target="_blank" class="footer-logo w-inline-block"><img src="images/logo_obs.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="https://swissleaders.ch/" target="_blank" class="footer-logo w-inline-block"><img src="images/swiss-leaders-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
    <div class="cw-cookies">
      <div id="cw-cookie-banner" class="cw-cookie_banner">
        <div class="cw-cookie_content">
          <h3 class="heading-style-h3">Cookie Einstellungen</h3>
          <p class="text-size-regular">Wir verwenden Cookies, um Ihnen die bestmögliche Nutzung unserer Website zu ermöglichen. Diese helfen uns dabei, die Website zu analysieren und zu verbessern sowie Ihnen ein optimales Nutzungserlebnis zu bieten.</p>
          <a href="datenschutzerklaerung.html" class="text-size-medium text-color-black">Datenschutzerklärung ansehen</a>
          <div class="cw-cookie_buttons"><button id="cw-btn-reject-all" class="cw-button_secondary">
              <div class="text-size-regular">Alle ablehnen</div>
            </button><button data-w-id="b7421a8e-479b-8458-8a2b-6c4cb088a137" id="cw-btn-options" class="cw-button_secondary">
              <div class="text-size-regular">Auswählen</div>
            </button><button id="cw-btn-accept-all" class="cw-button_primary">
              <div class="text-size-regular">Alle akzeptieren</div>
            </button></div>
          <div id="cw-cookie-options" class="cw-cookie_selection">
            <div class="w-form">
              <form id="email-form" name="email-form" data-name="Email Form" method="get" class="cw-cookie_options" data-wf-page-id="6863cffb7ce5d82a182d6528" data-wf-element-id="b7421a8e-479b-8458-8a2b-6c4cb088a13f"><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Notwendige-Cookies-2" id="consent-necessary" data-name="Notwendige Cookies 2" style="opacity:0;position:absolute;z-index:-1" checked=""><span id="consent-necessary" class="form_checkbox-label text-size-regular w-form-label" for="Notwendige-Cookies-2">Notwendige Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Analyse-Cookies" id="consent-analytics" data-name="Analyse Cookies" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Analyse-Cookies">Analyse Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Marketing" id="consent-ad-marketing" data-name="Marketing" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Marketing">Marketing-Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Nutzerdaten" id="consent-ad-user" data-name="Nutzerdaten" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Nutzerdaten">Nutzerdaten für Werbung</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Personalisierte-Werbung" id="consent-ad-personalization" data-name="Personalisierte Werbung" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Personalisierte-Werbung">Personalisierte Werbung</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Speichern-von-Informationen" id="consent-personalization" data-name="Speichern von Informationen" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="text-size-regular w-form-label" for="Speichern-von-Informationen">Speichern der Informationen für Zugriff oder auf Endgeräten</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Sicherheits-Cookies" id="consent-security" data-name="Sicherheits-Cookies" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Sicherheits-Cookies">Sicherheits-Cookies</span>
                </label></form>
              <div class="w-form-done">
                <div>Thank you! Your submission has been received!</div>
              </div>
              <div class="w-form-fail">
                <div>Oops! Something went wrong while submitting the form.</div>
              </div>
            </div><button id="cw-btn-accept-some" class="cw-button_secondary">
              <div class="text-size-regular">Auswahl übernehmen</div>
            </button>
          </div>
        </div>
      </div>
      <div class="cw-cookie_script w-embed w-script">
        <script>
// Immediate execution - prevent layout shift
(function() {
    const consentModeString = localStorage.getItem('consentMode');
    if (consentModeString) {
        document.write('<style>#cw-cookie-banner { display: none; } #cw-cookie-icon { display: flex; }</style>');
        // Apply consent to scripts immediately
        try {
            const consentMode = JSON.parse(consentModeString);
            setTimeout(function() {
                document.querySelectorAll('script[type="text/plain"][data-consent]').forEach(script => {
                    const consentCategory = script.getAttribute('data-consent');
                    const consentStatus = consentMode[consentCategory] || 'denied';
                    if (consentStatus === 'granted') {
                        const newScript = document.createElement('script');
                        Array.from(script.attributes).forEach(attr => {
                            if (attr.name !== 'data-consent' && attr.name !== 'type') {
                                newScript.setAttribute(attr.name, attr.value);
                            }
                        });
                        newScript.type = 'text/javascript';
                        if (script.innerHTML) newScript.innerHTML = script.innerHTML;
                        script.parentNode.insertBefore(newScript, script.nextSibling);
                    }
                });
            }, 100);
        } catch (e) {
            console.error("Error applying consent:", e);
        }
    }
})();
// FIXED: Find ALL cookie links, not just the first one with the ID
function setupAllCookieLinks() {
    // Find all links that contain "Cookies" text (case insensitive)
    const allCookieLinks = Array.from(document.querySelectorAll('a')).filter(link => 
        link.textContent.toLowerCase().includes('cookie') && 
        link.href && 
        link.href.includes('#')
    );
    console.log(`Found ${allCookieLinks.length} cookie links`);
    allCookieLinks.forEach((link, index) => {
        if (!link.hasAttribute('data-cookie-listener-added')) {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log(`Cookie link ${index + 1} clicked`);
                // Force show banner
                const banner = document.getElementById('cw-cookie-banner');
                if (banner) {
                    banner.style.display = 'block';
                    console.log('Banner shown');
                }
                // Force hide all cookie icons
                document.querySelectorAll('#cw-cookie-icon').forEach(icon => {
                    icon.style.display = 'none';
                });
                // Reset checkboxes and options
                setTimeout(function() {
                    if (window.setConsentCheckboxes) {
                        window.setConsentCheckboxes();
                    }
                    if (window.hideOptions) {
                        window.hideOptions();
                    }
                }, 10);
                return false;
            });
            link.setAttribute('data-cookie-listener-added', 'true');
            console.log(`Attached listener to cookie link ${index + 1}: "${link.textContent.trim()}"`);
        }
    });
}
// Try multiple times to catch all cookie links
setupAllCookieLinks();
setTimeout(setupAllCookieLinks, 100);
setTimeout(setupAllCookieLinks, 500);
setTimeout(setupAllCookieLinks, 1000);
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupAllCookieLinks);
} else {
    setupAllCookieLinks();
}
// Show banner if no consent
if (localStorage.getItem('consentMode') === null) {
    setTimeout(function() {
        const banner = document.getElementById('cw-cookie-banner');
        if (banner) banner.style.display = 'block';
    }, 10);
}
// Main script logic
document.addEventListener('DOMContentLoaded', function() {
    const banner = document.getElementById('cw-cookie-banner');
    if (!banner) return;
    const consentMapping = {
        'functionality_storage': 'consent-necessary',
        'ad_storage': 'consent-ad-marketing', 
        'analytics_storage': 'consent-analytics',
        'ad_user_data': 'consent-ad-user',
        'ad_personalization': 'consent-ad-personalization',
        'personalization_storage': 'consent-personalization',
        'security_storage': 'consent-security',
    };
    function uncheckAllConsentCheckboxes() {
        ['consent-analytics', 'consent-ad-personalization', 'consent-ad-marketing', 'consent-ad-user', 'consent-personalization', 'consent-security'].forEach(checkboxId => {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = false;
                const checkboxDiv = checkbox.previousElementSibling;
                if (checkboxDiv && checkboxDiv.classList.contains('w--redirected-checked')) {
                    checkboxDiv.classList.remove('w--redirected-checked');
                }
            }
        });
    }
    function setConsentCheckboxes() {
        uncheckAllConsentCheckboxes();
        const consentModeString = localStorage.getItem('consentMode');
        if (consentModeString) {
            const consentMode = JSON.parse(consentModeString);
            Object.entries(consentMapping).forEach(([storageKey, checkboxId]) => {
                const checkbox = document.getElementById(checkboxId);
                if (checkbox) {
                    const isChecked = consentMode[storageKey] === 'granted';
                    checkbox.checked = isChecked;
                    const checkboxDiv = checkbox.previousElementSibling;
                    if (checkboxDiv) {
                        if (isChecked) {
                            checkboxDiv.classList.add('w--redirected-checked');
                        } else {
                            checkboxDiv.classList.remove('w--redirected-checked');
                        }
                    }
                }
            });
        }
    }
    function hideOptions() {
        const options = document.getElementById('cw-cookie-options');
        if (options) options.style.height = '0px';
    }
    function hideBanner() {
        banner.style.display = 'none';
        // Show all cookie icons
        document.querySelectorAll('#cw-cookie-icon').forEach(icon => {
            icon.style.display = 'flex';
        });
    }
    function setConsent(consent) {
        const consentMode = {
            'functionality_storage': consent.necessary ? 'granted' : 'denied',
            'ad_user_data': consent.aduser ? 'granted' : 'denied',
            'ad_storage': consent.admarketing ? 'granted' : 'denied',
            'analytics_storage': consent.analytics ? 'granted' : 'denied',
            'ad_personalization': consent.adpersonalized ? 'granted' : 'denied',
            'personalization_storage': consent.personalized ? 'granted' : 'denied',
            'security_storage': consent.security ? 'granted' : 'denied',
        };
        localStorage.setItem('consentMode', JSON.stringify(consentMode));
        hideBanner();
    }
    // Make functions global for the cookie link handlers
    window.setConsentCheckboxes = setConsentCheckboxes;
    window.hideOptions = hideOptions;
    // Button event listeners
    const acceptAll = document.getElementById('cw-btn-accept-all');
    if (acceptAll) {
        acceptAll.addEventListener('click', function() {
            setConsent({
                necessary: true, analytics: true, adpersonalized: true, admarketing: true,
                aduser: true, personalized: true, security: true,
            });
        });
    }
    const rejectAll = document.getElementById('cw-btn-reject-all');
    if (rejectAll) {
        rejectAll.addEventListener('click', function() {
            setConsent({
                necessary: true, analytics: false, adpersonalized: false, admarketing: false,
                aduser: false, personalized: false, security: false
            });
        });
    }
    const acceptSome = document.getElementById('cw-btn-accept-some');
    if (acceptSome) {
        acceptSome.addEventListener('click', function() {
            setConsent({
                necessary: true,
                analytics: document.getElementById('consent-analytics')?.checked || false,
                adpersonalized: document.getElementById('consent-ad-personalization')?.checked || false,
                admarketing: document.getElementById('consent-ad-marketing')?.checked || false,
                aduser: document.getElementById('consent-ad-user')?.checked || false,
                personalized: document.getElementById('consent-personalization')?.checked || false,
                security: document.getElementById('consent-security')?.checked || false,
            });
        });
    }
    const optionsBtn = document.getElementById('cw-btn-options');
    if (optionsBtn) {
        optionsBtn.addEventListener('click', function() {
            const options = document.getElementById('cw-cookie-options');
            if (options) {
                if (options.style.height === '0px' || options.style.height === '') {
                    options.style.height = options.scrollHeight + 'px';
                } else {
                    options.style.height = '0px';
                }
            }
        });
    }
    // Initialize
    setConsentCheckboxes();
    // Set up cookie links one more time
    setTimeout(setupAllCookieLinks, 100);
});
</script>
      </div>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=68257cde3c60ae59e717c715" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script src="https://instant.page/5.2.0" type="module" integrity="sha384-jnZyxPjiipYXnSU0ygqeac2q7CVYMbh84q0uHVRRxEtvFPiQYbXWUorga2aqZJ0z"></script>
  <script>
//Navbar Scroll Script
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar2_component');
    const navLinks = document.querySelectorAll('.navbar2_link');
    const navButton = document.querySelector('.button.is-navbar2-button');
    const logoSvgPaths = document.querySelectorAll('.navbar2_component .w-embed svg path');
    const menuLines = document.querySelectorAll('.button_dialog-menu-line');
    let scrollThreshold = 50; // Adjust this value to change when the animation triggers
    function updateNavbar() {
        if (window.scrollY > scrollThreshold) {
            // Scrolled state
            navbar.style.position = 'fixed';
            navbar.style.top = '0px';
            navbar.style.left = '0px';
            navbar.style.width = '100%';
            navbar.style.zIndex = '1000'; // Ensure it's on top
            navbar.style.backgroundColor = 'white';
            // Update nav links color
            navLinks.forEach(link => {
                link.style.color = '#000000'; // Dark grey color
            });
            // Update button background
            if (navButton) {
                navButton.style.backgroundColor = '#f5f5f5'; // Light grey background
            }
            // Update SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = '#000000';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = '#000000';
                }
            });
            // Update hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = '#000000';
            });
        } else {
            // Reset to original state
            navbar.style.position = ''; // Revert to original position
            navbar.style.top = '';
            navbar.style.left = '';
            navbar.style.width = '';
            navbar.style.zIndex = '';
            navbar.style.backgroundColor = 'transparent';
            // Reset nav links color
            navLinks.forEach(link => {
                link.style.color = ''; // Reset to original color
            });
            // Reset button background
            if (navButton) {
                navButton.style.backgroundColor = ''; // Reset to original background
            }
            // Reset SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = 'white';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = 'white';
                }
            });
            // Reset hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = 'white';
            });
        }
    }
    // Add smooth transition styles
    navbar.style.transition = 'all 0.3s ease';
    navLinks.forEach(link => {
        link.style.transition = 'color 0.3s ease';
    });
    if (navButton) {
        navButton.style.transition = 'background-color 0.3s ease';
    }
    logoSvgPaths.forEach(path => {
        path.style.transition = 'fill 0.3s ease, stroke 0.3s ease';
    });
    menuLines.forEach(line => {
        line.style.transition = 'color 0.3s ease';
    });
    // Initial check
    updateNavbar();
    // Add scroll event listener
    window.addEventListener('scroll', updateNavbar);
});
</script>
  <script>
(function(){
  document.addEventListener("DOMContentLoaded",function(){
    const resources={animeJs:false,scripts:false};
    const navLinks=document.querySelectorAll("a.nav-link,a.navbar2_link,nav a,.navigation a,.menu a");
    navLinks.forEach(link=>{
      link.addEventListener("mouseenter",preloadResources);
    });
    let scrollTriggered=false;
    window.addEventListener("scroll",function(){
      if(!scrollTriggered){
        scrollTriggered=true;
        preloadResources();
      }
    },{once:true});
    function preloadResources(){
      if(resources.animeJs&&resources.scripts){
        return;
      }
      if(!resources.animeJs&&typeof anime==="undefined"){
        resources.animeJs=true;
        const preloadLink=document.createElement("link");
        preloadLink.rel="preload";
        preloadLink.as="script";
        preloadLink.href="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js";
        document.head.appendChild(preloadLink);
        const script=document.createElement("script");
        script.src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js";
        document.head.appendChild(script);
      }
      if(!resources.scripts){
        resources.scripts=true;
        const scriptPreload=document.createElement("link");
        scriptPreload.rel="prefetch";
        scriptPreload.as="script";
        scriptPreload.href="https://cdn.jsdelivr.net/gh/woelfli/webflow-animations@main/woelfli-moving-image.min.js";
        document.head.appendChild(scriptPreload);
        if(typeof anime!=="undefined"){
          initializeAnimations();
        } else {
          setTimeout(checkAnimeAndInitialize,100);
        }
      }
    }
    function checkAnimeAndInitialize(){
      if(typeof anime!=="undefined"){
        initializeAnimations();
      } else {
        setTimeout(checkAnimeAndInitialize,100);
      }
    }
    function initializeAnimations(){
      const movingImage=document.querySelector(".moving-image");
      if(movingImage){
        movingImage.style.transition="none";
        movingImage.style.transform="scale(1.1) translateX(-5%)";
        const parent=movingImage.parentElement;
        if(parent&&!parent.style.overflow){
          parent.style.overflow="hidden";
        }
      }
      const sections=document.querySelectorAll("section,header:not(.navbar2_component)");
      sections.forEach(section=>{
        if(section.classList.contains("woelfli-animation-ready")||
           section.classList.contains("navbar2_component")||
           section.classList.contains("section-header-2")){
          return;
        }
        section.classList.add("woelfli-animation-ready");
      });
    }
  });
})(); 
</script>
</body>
</html>