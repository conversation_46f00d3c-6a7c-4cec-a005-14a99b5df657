<!DOCTYPE html><!--  Last Published: Mon Aug 11 2025 09:01:09 GMT+0000 (Coordinated Universal Time)  -->
<html data-wf-page="68257cde3c60ae59e717c714" data-wf-site="68257cde3c60ae59e717c715" lang="de">
<head>
  <meta charset="utf-8">
  <title>Wölfli - Bauplanung, Bauleitung &amp; GU in Zürich, Zug und Baden</title>
  <meta content="Ihr Partner für Bauprojekte in Zürich, Zug und Baden. Wir unterstützen Sie in allen Phasen, als Planer, Baumanagement / Bauleitung oder Generalunternehmer." name="description">
  <meta content="Wölfli - Bauplanung, Bauleitung &amp; GU in Zürich, Zug und Baden" property="og:title">
  <meta content="Ihr Partner für Bauprojekte in Zürich, Zug und Baden. Wir unterstützen Sie in allen Phasen, als Planer, Baumanagement / Bauleitung oder Generalunternehmer." property="og:description">
  <meta content="Wölfli - Bauplanung, Bauleitung &amp; GU in Zürich, Zug und Baden" property="twitter:title">
  <meta content="Ihr Partner für Bauprojekte in Zürich, Zug und Baden. Wir unterstützen Sie in allen Phasen, als Planer, Baumanagement / Bauleitung oder Generalunternehmer." property="twitter:description">
  <meta property="og:type" content="website">
  <meta content="summary_large_image" name="twitter:card">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <link href="css/normalize.css" rel="stylesheet" type="text/css">
  <link href="css/webflow.css" rel="stylesheet" type="text/css">
  <link href="css/woelfli-staging-de009659c94824f4bd1e533.webflow.css" rel="stylesheet" type="text/css">
  <style>@media (min-width:992px) {html.w-mod-js:not(.w-mod-ix) [data-w-id="1818cbf1-6206-ff10-4bf4-dd1b5e22180e"] {width:100%;height:100%;}}@media (max-width:991px) and (min-width:768px) {html.w-mod-js:not(.w-mod-ix) [data-w-id="1818cbf1-6206-ff10-4bf4-dd1b5e22180e"] {width:100%;height:100%;}}</style>
  <script type="text/javascript">!function(o,c){var n=c.documentElement,t=" w-mod-";n.className+=t+"js",("ontouchstart"in o||o.DocumentTouch&&c instanceof DocumentTouch)&&(n.className+=t+"touch")}(window,document);</script>
  <link href="images/favicon.png" rel="shortcut icon" type="image/x-icon">
  <link href="images/webclip.png" rel="apple-touch-icon">
  <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js" as="script">
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/animation/beautified_scroll_animations_noheader.js?=v7" defer=""></script>
  <script defer="" src="https://cloud.umami.is/script.js" data-website-id="634ce2ab-8a79-4bd9-b693-703b47938caf"></script>
  <style>
body {
  overscroll-behavior-y: contain;
}
  .image-3 {
  content-visibility: auto;
  contain: layout style paint;
}
.text-size-regular {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
  .text-size-regular {
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto;
  -ms-hyphens: auto;
  -o-hyphens: auto;
  hyphens: auto;
}
  .no-hyphen {
  word-break: normal;
  -webkit-hyphens: none;
  -moz-hyphens: none;
  -ms-hyphens: none;
  -o-hyphens: none;
  hyphens: none;
}
  .text-size-medium{
  word-break: break-word;
  -webkit-hyphens: auto;
  -moz-hyphens: auto ;
  -ms-hyphens: auto ;
  -o-hyphens: auto ;
  hyphens: auto ;
}
/*.heading-style-h1 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h2 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h3 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}*/
.heading-style-h4 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h5 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
</style>
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/woelfli-moving-image.min.js" defer=""></script>
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/mapbox/mapbox-static.js?v=28" defer=""></script>
  <script src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/mapbox/locations-data.js?=v5" defer=""></script>
  <style>
/* Custom styles for Mapbox project cards */
/* Project Type Tag */
.project-type-tag {
  display: inline-block;
  background-color: #f0f0f0;
  color: #333;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 12px;
  font-weight: 500;
}
/* Project Type in Popup */
.project-type {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}
/* Activity Title */
.card_activity-title {
  font-weight: 600;
  font-size: 16px;
  margin-top: 12px;
  margin-bottom: 8px;
}
/* Activity Content */
.card_activity {
  font-size: 14px;
  line-height: 1.5;
  color: #333;
}
.header_background-video {
  background-attachment: scroll, scroll; !important;
}
/*style map popups*/
.mapboxgl-popup-content {
	pointer-events: auto;
  border-radius: 4px;
  box-shadow: none;
  padding: 12px 16px;
  color: #161616;
  background-color: #fff;
}
/*popup bottom arrow color*/
.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip {
    border-top-color: #fefae0;
}
body:has( [data-cursor]:hover ) .cursor{ opacity: 1; }
.button:hover .button-bg{
	transform: scale(0.95);
}
.reveal-type {
  will-change: opacity;
  transform: translateZ(0);
  -webkit-font-smoothing: antialiased;
}
</style>
  <style>
      .mapboxgl-popup {
        z-index: 1;
      }
      .mapboxgl-popup-content {
        z-index: 1;
      }
      .mapboxgl-popup-close-button {
        z-index: 12;
        padding: 8px !important; /* Larger touch target */
        font-size: 20px !important; /* Larger X */
        right: 0 !important;
        top: 0 !important;
        color: #000 !important;
        background: rgba(255, 255, 255, 0.8) !important;
        border-radius: 0 3px 0 3px !important;
      }
      @media (max-width: 767px) {
        .mapboxgl-popup-close-button {
          padding: 12px !important;
          font-size: 24px !important;
        }
.mapboxgl-popup { 
max-width:5rem;
}
      }
  .handwriting-svg path {
    stroke-dasharray: 0;
    stroke-dashoffset: 0;
  }
    </style>
  <style>
:root {
  --vh: 1vh;
}
</style>
  <script>
function setVh() {
  let vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}
setVh();
window.addEventListener('resize', setVh);
</script>
</head>
<body class="body">
  <div class="menu-main-css u-embed w-embed">
    <style>
:root {
	--font-family-serif: "Overused Grotesk", 
  iowan old style,
  apple garamond,
  baskerville,
  times new roman,
  droid serif,
  times,
  source serif pro,
  serif,
  apple color emoji,
  segoe ui emoji,
  segoe ui symbol;
  --font-family-normal: "Overused Grotesk",
  -apple-system,
  blinkmacsystemfont,
  avenir next,
  avenir,
  segoe ui,
  helvetica neue,
  helvetica,
  cantarell,
  ubuntu,
  roboto,
  noto,
  arial,
  sans-serif;
  --font-weight-regular: 400;
	--font-size-h1: clamp(3rem, 0.578rem + 6.92vw, 7.5rem);
	--font-size-h2: clamp(2.25rem, 0.7695rem + 4.23vw, 5rem);
	--font-size-h3: clamp(1.25rem, 0.8475rem + 1.15vw, 2rem);
	--size-48: clamp(2rem, 1.461rem + 1.54vw, 3rem);
	--size-64: clamp(2.5rem, 1.6915rem + 2.31vw, 4rem);
	--size-80: clamp(3rem, 1.922rem + 3.08vw, 5rem);
	--size-96: clamp(4rem, 2.922rem + 3.08vw, 6rem);
	--size-120: clamp(5rem, 3.6525rem + 3.85vw, 7.5rem);
	--size-144: clamp(6rem, 4.383rem + 4.62vw, 9rem);
  --z-below: -1;
  --z-above: 1;
  --z-dialog: 300;
  --grid-margin: clamp(1rem, 0.1915rem + 2.31vw, 2.5rem);
  --grid-gutter: 1rem;
  --grid-columns: 12;
  --grid-column-width:  calc(((var(--vw, 1vw) * 100) - (2 * var(--grid-margin))) / var(--grid-columns) - (var(--grid-gutter) * (var(--grid-columns) - 1) / var(--grid-columns)));
}
html {
	-webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
  text-size-adjust: none;
  touch-action: manipulation;
  scrollbar-width: thin;
  scrollbar-color: color-mix(in hsl, var(--color), transparent 50%) var(--color-background);
}
::selection {
  background-color: var(--selection-background);
  color: var(--selection-foreground);
  text-shadow: none;
}
*:focus-visible:not(input, textarea, select) {
  outline: var(--color) auto 6px;
  outline-offset: 4px;
}
/* theme */
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
[data-theme="light"] .link {
	color: var(--color);
}
/* webflow */
.wf-design-mode .wf-empty {
  padding: 0;
}
.wf-editor-mode .wf-empty {
  padding: 0;
}
/* utilities */
.u-screen-reader-text {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  margin: 0;
  padding: 0;
  width: 1px;
  height: 1px;
  border: 0;
}
.u-embed {
	position: fixed;
  inset: 0 auto auto 0;
}
.u-embed::before {
	content: none !important;
}
/* icons */
.icon.is-small {
	--icon-width: .75rem;
  --icon-height: .75rem;
}
/* header */
.header_breadcrumb-item:nth-child(1),
.header_breadcrumb-item:nth-child(2) {
	opacity: 60%;
}
.header_breadcrumb-item:nth-child(1)::after {
	content: "//";
  margin-left: var(--size-4);
}
.header_breadcrumb-item:nth-child(2)::after {
	content: "/";
  margin-left: var(--size-4);
}
/* buttons */
.button_cloneable::after,
.button_info::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.25rem;
}
.button_dialog {
	-webkit-tap-highlight-color: transparent;
  transition: transform .15s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog[data-active] {
	transform: translate3d(0, .125rem, 0);
}
.button_dialog[data-active] .button_dialog-bg {
	background-color: color-mix(in hsl, var(--color-accent), transparent 25%);
}
.button_dialog[data-active] .button_dialog-bg::after {
	background-color: color-mix(in hsl, var(--color), transparent 25%);
}
.button_cloneable-bg {
	transition: box-shadow .3s cubic-bezier(.62, .08, 0, 1);
}
.button_info .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-bg {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1), background-color .15s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-menu-line-wrap {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
.button_dialog-bg::before {
  content: "";
  display: block;
  position: absolute;
  background-color: var(--color-accent-orange);
  inset: 0;
  width: 100%;
  height: 100%;
  clip-path: inset(100% 0 0 0 round 0 0 .5rem .5rem);
  transition: clip-path .3s cubic-bezier(.36, .08, 0, 1);
  transition-delay: 0.15s;
}
@media screen and (prefers-reduced-motion) {
	.button_dialog-bg::before {
  	content: none;
  }
}
.button_dialog-bg::after {
  content: "";
  display: block;
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  clip-path: inset(100% 0 0 0 round 0 0 .5rem .5rem);
  transition: clip-path .45s cubic-bezier(.62, .08, 0, 1), background-color .15s cubic-bezier(.62, .08, 0, 1);
}
@media screen and (prefers-reduced-motion) {
	.button_dialog-bg::after {
  	clip-path: inset(0 0 0 0 round .5rem);
    transition: opacity .3s cubic-bezier(.62, .08, 0, 1);
    opacity: 0;
  }
}
@media (hover: hover) and (pointer: fine) {
  .button_cloneable:hover .button_cloneable-bg,
  .button_cloneable:focus-visible .button_cloneable-bg {
    box-shadow: 0 0 0 .1875rem var(--color);
  }
  .button_info:hover .icon,
  .button_info:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
  .button_dialog:hover .button_dialog-bg,
  .button_dialog:focus-visible .button_dialog-bg {
    transform: scale(1.05) translateZ(0);
  }
  .button_dialog:hover .button_dialog-bg::before,
  .button_dialog:focus-visible .button_dialog-bg::before,
  .button_dialog:hover .button_dialog-bg::after,
  .button_dialog:focus-visible .button_dialog-bg::after {
    clip-path: inset(0 0 0 0 round .5rem);
  }
  .button_dialog:hover .button_dialog-bg::before,
  .button_dialog:focus-visible .button_dialog-bg::before {
    transition-delay: 0s;
  }
  .button_dialog:hover .button_dialog-menu-line-wrap,
  .button_dialog:focus-visible .button_dialog-menu-line-wrap {
  	transform: scaleX(.9) translateZ(0);
  }
  .button_dialog:focus-visible {
    outline-offset: .5rem;
  }
}
@media screen and (prefers-reduced-motion) and (hover: hover) and (pointer: fine) {
	.button_dialog:hover .button_dialog-bg,
  .button_dialog:focus-visible .button_dialog-bg {
    transform: scale(1) translateZ(0);
  }
	.button_dialog:hover .button_dialog-bg::after,
  .button_dialog:focus-visible .button_dialog-bg::after {
    opacity: 1;
  }
}
/* External Links */
.link[target="_blank"]::after {
	content: "";
  display: inline-block;
  background-color: currentColor;
  mask: url("https://cdn.prod.website-files.com/66d5c7d781a3c8330fefd851/66eade6255eed3286f87a03c_icon-external.svg") no-repeat 50% 50%;
	height: .625rem;
  width: .625rem;
  margin-left: .25rem;
}
/* header info dialog */
.header_dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --content-speed: .45s;
  --content-ease: cubic-bezier(.215, .61, .355, 1);
  --content-stagger: .05s;
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .header_dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.header_dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.header_dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.header_dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.header_dialog:not([open]) .header_dialog-inner {
	transform: translate3d(100%, 0, 0);
}
.header_dialog-inner {
	transform: translate3d(0, 0, 0);
	contain: layout style paint;
	transition: transform var(--modal-speed) var(--modal-ease);  
}
.header_dialog-inner:focus-visible {
	outline-offset: -.25rem;
}
/* move wrapper */
@media screen and (prefers-reduced-motion: no-preference) {
  .hero,
  .block-text,
  .footer {
    transition: transform .75s cubic-bezier(.62, .08, 0, 1);  
  }
  .wrapper:has(.header_dialog[open]) .hero,
  .wrapper:has(.header_dialog[open]) .block-text,
  .wrapper:has(.header_dialog[open]) .footer {
    transform: translate3d(-10rem, 0 , 0);
  }
}
@media screen and (prefers-reduced-motion) {
  .header_dialog:not([open]) .header_dialog-inner {
  	transform: translate3d(0, 0, 0);
    opacity: 0;
  }
  .header_dialog-inner {
    transition: opacity var(--modal-speed) var(--modal-ease);  
  }
}
/* initial state of close button */
.header_dialog:not([open]) .header_dialog-button-close {
	transform: translate3d(.75rem, 0, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.header_dialog-button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.header_dialog-button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.header_dialog-button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .header_dialog-button-close:hover .icon,
  .header_dialog-button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.header_dialog:not([open]) .header_dialog-backdrop {
	opacity: 0;
}
.header_dialog-backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
</style>
  </div>
  <div class="css-dialog-styles u-embed w-embed">
    <style>
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
.css-dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --content-speed: .45s;
  --content-ease: cubic-bezier(.215, .61, .355, 1);
  --content-stagger: .05s;
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .css-dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.css-dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.css-dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.css-dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.css-dialog:not([open]) .css-dialog_inner {
	transform: translate3d(0, 100%, 0);
}
.css-dialog_inner {
	contain: content;
	transition: transform var(--modal-speed) var(--modal-ease);  
}
.css-dialog_inner:focus-visible {
	outline-offset: -.25rem;
}
@media screen and (prefers-reduced-motion) {
  .css-dialog:not([open]) .css-dialog_inner {
  	transform: translate3d(0, 0, 0);
    opacity: 0;
  }
  .css-dialog_inner {
    transition: opacity var(--modal-speed) var(--modal-ease);  
  }
}
/* preload content for better performance */
.css-dialog_title,
.css-dialog_paragraph,
.css-dialog_visual-number,
.css-dialog_visual-author,
.css-dialog_visual-img {
  content-visibility: auto;
  contain: layout style paint;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
/* initial state of close button */
.css-dialog:not([open]) .css-dialog_button-close {
	transform: translate3d(0, -.75rem, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.css-dialog_button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.css-dialog_button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.css-dialog_button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .css-dialog_button-close:hover .icon,
  .css-dialog_button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.css-dialog:not([open]) .css-dialog_backdrop {
	opacity: 0;
}
.css-dialog_backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
/* initial state of content when modal is not opened (only when reduce motion is not activated) */
@media screen and (prefers-reduced-motion: no-preference) {
  .css-dialog:not([open]) .css-dialog_title,
  .css-dialog:not([open]) .css-dialog_paragraph.u-h3,
  .css-dialog:not([open]) .css-dialog_visual-img {
    transform: translate3d(0, 2.5rem, 0);
    opacity: 0;
  }
  .css-dialog:not([open]) .css-dialog_paragraph,
  .css-dialog:not([open]) .css-dialog_visual-number,
  .css-dialog:not([open]) .css-dialog_visual-author {
    transform: translate3d(0, 1.25rem, 0);
    opacity: 0;
  }
  .css-dialog_title,
  .css-dialog_paragraph,
  .css-dialog_visual-number,
  .css-dialog_visual-author,
  .css-dialog_visual-img {
    transition: transform var(--content-speed) var(--content-ease), opacity var(--content-speed) var(--content-ease);
    transition-delay: 0s, 0s;
    opacity: 1;
  }
  .css-dialog[open] .css-dialog_title,
  .css-dialog[open] .css-dialog_paragraph,
  .css-dialog[open] .css-dialog_visual-number,
  .css-dialog[open] .css-dialog_visual-author,
  .css-dialog[open] .css-dialog_visual-img {
    transition-delay: calc((var(--dialog-animation-speed) - .2s) + var(--content-stagger) * (var(--index) - 1)), calc((var(--dialog-animation-speed) - .2s) + var(--content-stagger) * (var(--index) - 1));
  }
}
</style>
  </div>
  <div class="css-dialog-javascript u-embed w-embed w-script">
    <script defer="">
// handles scrollbar jump when opening modal and disabling scrolling behind the modal
// normaly scrollbar-gutter: stable; would handle this, but safari doesn't support it at this time
class CssScrollbarHandler {
  constructor(options = {}) {
    this.html = document.documentElement;
    this.scrollbarThreshold = options.scrollbarThreshold || 5;
    this.scrollbarWidth = 0;
    this.calculateScrollbarWidth();
    this.handleScrollbar();
  }
  calculateScrollbarWidth() {
    // calculate scrollbar width
    const documentWidth = this.html.clientWidth;
    this.scrollbarWidth = window.innerWidth - documentWidth;
  }
  handleScrollbar() {
    if (this.scrollbarWidth > this.scrollbarThreshold) {   
      this.html.style.paddingRight = `${this.scrollbarWidth}px`;
    }
  }
  destroy() {
    this.html.style.paddingRight = '';
  }
}
// JavaScript for Dialog open/close
class CssDialog {
	constructor(element, options = {}) {
  	this.toggler = element;
		this.dialog = this.toggler.parentNode.querySelector("[data-css-dialog]");
    this.dialogClose = this.dialog.querySelector("[data-css-dialog-close]");
		this.dialogBackdrop = this.dialog.querySelector("[data-css-dialog-backdrop]");
    this.activeClass = "is-active";
    // get transition-duration from CSS variable --dialog-animation-speed
    this.dialogStyles = getComputedStyle(this.dialog);
    this.transitionDuration = parseFloat(this.dialogStyles.getPropertyValue("--dialog-animation-speed")) * 1000; // convert to miliseconds
    // ScrollbarHandler configuration
    this.scrollbarOptions = {
      scrollbarThreshold: 5,
      ...options  // add to options
    };
    // bind methods
    this.onKeyDown = this.onKeyDown.bind(this);
    this.removeStyles = this.removeStyles.bind(this);
    this.init();
  }
	init() {
  	this.toggler.addEventListener("click", () => this.toggle());
    this.dialogClose.addEventListener("click", () => this.hide());
    this.dialogBackdrop.addEventListener("click", () => this.hide());
    return this;
  }
  // add ESC trigger so this.activeClass will also be removed
  onKeyDown(e) {
    if ("Escape" === e.key) {
      e.preventDefault();
      this.dialogClose.click();
    }
  }
  // toggle between show & hide
  toggle() {
    if (this.dialog.classList.contains(this.activeClass)) {
      this.hide();
    } else {
      this.show();
    }
  }
  // show dialog, add event listener for ESC key, add overflow hidden on body to disable scroll
  show() {
  	document.documentElement.style.overflow = "hidden";
    window.addEventListener("keydown", this.onKeyDown);
    this.scrollbarHandler = new CssScrollbarHandler(this.scrollbarOptions);
    this.dialog.classList.add(this.activeClass);
    this.dialog.showModal();
  }
  // hide dialog, remove event listener for ESC key, remove overflow hidden on body to enable scroll
  hide() {
    window.removeEventListener("keydown", this.onKeyDown);
    this.dialog.classList.remove(this.activeClass);
    this.dialog.close();
    setTimeout(() => {
      this.removeStyles();
      // cleanup ScrollbarHandler
      if (this.scrollbarHandler) {
        this.scrollbarHandler.destroy();
        this.scrollbarHandler = null;
      }
    }, this.transitionDuration);
  }
  // remove styles from html
  removeStyles() {
  	document.documentElement.style.overflow = "";
  }
}
// add to webflow
window.Webflow ||= [];
window.Webflow.push(async () => {	
  const cssDialogElements = document.querySelectorAll("[data-css-dialog-toggle]");
  if (cssDialogElements.length > 0) {
  	cssDialogElements.forEach(element => {
    	new CssDialog(element);
  	});
  }
});
</script>
  </div>
  <div class="menu-dialog-styles u-embed w-embed">
    <style>
[data-theme="light"] {
	--color: var(--ci-black);
  --color-background: var(--ci-cream);
  --selection-foreground: var(--color-accent);
  --selection-background: var(--ci-black);
}
.menu-dialog {	
	--dialog-animation-speed: .75s; /* this is your complete animation time */
  --backdrop-speed: .3s;
  --backdrop-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-speed: .75s;
  --modal-ease: cubic-bezier(.62, .08, 0, 1);
  --modal-close-ease: cubic-bezier(.36, 0, .087, 1);
  --modal-opacity-speed: .45s;
  --modal-opacity-ease: cubic-bezier(.62, .08, 0, 1);
  --close-button-speed: .45s;
  --close-button-ease: cubic-bezier(.62, .08, 0, 1);
  --nav-speed: .45s;
  --nav-ease: cubic-bezier(.215, .61, .355, 1);
}
/* when reduced motion is activated in user settings */
@media screen and (prefers-reduced-motion) {
  .menu-dialog {	
    --dialog-animation-speed: .3s; /* this is your complete animation time */
    --backdrop-speed: .3s;
    --modal-speed: .3s;
    --modal-opacity-speed: .3s;
    --close-button-speed: .3s;
  }
}
/* make the native backdrop invisible */
.menu-dialog::backdrop {
	opacity: 0;
}
/* modal animation */
.menu-dialog:not([open]) {
	pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear var(--dialog-animation-speed);
}
.menu-dialog[open] {
	transition-duration: var(--dialog-animation-speed);
}
/* initial state of actual modal */
.menu-dialog:not([open]) .menu-dialog_inner {
  opacity: 0;
  transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
}
.menu-dialog:not([open]) .menu-dialog_footer {
  opacity: 0;
  transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
}
.menu-dialog_inner,
.menu-dialog_footer {
	transition: opacity var(--modal-opacity-speed) var(--modal-opacity-ease);  
}
.menu-dialog_inner:focus-visible {
	outline-offset: -.25rem;
}
@media screen and (prefers-reduced-motion: no-preference) {
  .menu-dialog:not([open]) .menu-dialog_inner {
    transform: translate3d(0, 8rem, 0) rotate(-3deg);
    transition: transform var(--modal-speed) var(--modal-close-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
  }
  .menu-dialog:not([open]) .menu-dialog_footer {
    transform: translate3d(0, 14rem, 0) rotate(3deg);
    transition: transform var(--modal-speed) var(--modal-close-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);
  }
  .menu-dialog_inner,
  .menu-dialog_footer {
    transition: transform var(--modal-speed) var(--modal-ease), opacity var(--modal-opacity-speed) var(--modal-opacity-ease);  
  }
}
/* initial state of close button */
.menu-dialog:not([open]) .menu-dialog_button-close {
	transform: translate3d(0, -.75rem, 0) scale(0.8);
  opacity: 0;
  transition: transform var(--close-button-speed) var(--close-button-ease), opacity var(--close-button-speed) var(--close-button-ease);
}
.menu-dialog_button-close {
	transition: transform var(--close-button-speed) .15s var(--close-button-ease), opacity var(--close-button-speed) .15s var(--close-button-ease);
  -webkit-tap-highlight-color: transparent;
}
.menu-dialog_button-close::after {
	content: "";
  display: block;
  position: absolute;
  inset: -.75rem;
}
.menu-dialog_button-close .icon {
	transition: transform .3s cubic-bezier(.62, .08, 0, 1);
}
@media (hover: hover) and (pointer: fine) {
  .menu-dialog_button-close:hover .icon,
  .menu-dialog_button-close:focus-visible .icon {
    transform: scale(1.1) translateZ(0);
  }
}
/* initial state of backdrop when modal is not opened */
.menu-dialog:not([open]) .menu-dialog_backdrop {
	opacity: 0;
}
.menu-dialog_backdrop {
	transition: opacity var(--backdrop-speed) var(--backdrop-ease);
  -webkit-tap-highlight-color: transparent;
}
/* hide scrollbar on transform animation */
.menu-dialog[open] .menu-dialog_outer {
	animation: hide-scroll var(--dialog-animation-speed) backwards;
}
@keyframes hide-scroll {
  from, to { overflow: hidden; } 
}
/* menu navigation */
.menu-dialog_nav-link {
	transition: opacity calc(var(--nav-speed) * .5) var(--nav-ease);
}
.menu-dialog_nav-list:has(.menu-dialog_nav-link:hover) .menu-dialog_nav-link:not(:hover) {
	opacity: .5;
}
@media screen and (prefers-reduced-motion: no-preference) {
  .menu-dialog_nav-link {
    transition: padding-left var(--nav-speed) var(--nav-ease), opacity calc(var(--nav-speed) * .5) var(--nav-ease);
  }
  .menu-dialog_nav-link::before {
    content: "";
    display: block;
    position: absolute;
    background-color: currentColor;
    mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E %3Cpath fill='%23000' fill-rule='evenodd' d='M9 5h2v2h2v2h2v2h-2v2h-2v2H9v-2H7v-2H5V9h2V7h2V5Z' clip-rule='evenodd'/%3E %3C/svg%3E") no-repeat 50% 50%;
    left: calc((var(--icon-height) + var(--size-8)) * -1);
    height: var(--icon-height);
    width: var(--icon-width);
    pointer-events: none;
    transform: translate3d(-.5rem, 0, 0);
    transition: transform calc(var(--nav-speed) * .75) var(--nav-ease), opacity calc(var(--nav-speed) * .5) var(--nav-ease);
    opacity: 0;
  }
  .menu-dialog_nav-link:hover,
  .menu-dialog_nav-link:focus-visible {
    padding-left: var(--size-4);
  }
  .menu-dialog_nav-link:hover::before,
  .menu-dialog_nav-link:focus-visible::before {
    transform: translate3d(.5rem, 0, 0);
    opacity: 1;
  }
}
.button_dialog-bg::after {
background-color:none!important;
position:relative!important;
}
</style>
  </div>
  <div class="menu-dialog-javascript u-embed w-embed w-script">
    <script defer="">
// handles scrollbar jump when opening modal and disabling scrolling behind the modal
// normaly scrollbar-gutter: stable; would handle this, but safari doesn't support it at this time
class MenuScrollbarHandler {
  constructor(options = {}) {
    this.html = document.documentElement;
    this.scrollbarThreshold = options.scrollbarThreshold || 5;
    this.scrollbarWidth = 0;
    this.calculateScrollbarWidth();
    this.handleScrollbar();
  }
  calculateScrollbarWidth() {
    // Berechne die tatsächliche Scrollbar-Breite
    const documentWidth = this.html.clientWidth;
    this.scrollbarWidth = window.innerWidth - documentWidth;
  }
  handleScrollbar() {
    if (this.scrollbarWidth > this.scrollbarThreshold) {   
      this.html.style.paddingRight = `${this.scrollbarWidth}px`;
    }
  }
  destroy() {
    this.html.style.paddingRight = '';
  }
}
// JavaScript for Dialog open/close
class MenuDialog {
	constructor(element, options = {}) {
  	this.toggler = element;
		this.dialog = this.toggler.parentNode.querySelector("[data-menu-dialog]");
    this.dialogClose = this.dialog.querySelector("[data-menu-dialog-close]");
		this.dialogBackdrop = this.dialog.querySelector("[data-menu-dialog-backdrop]");
    this.activeClass = "is-active";
    // Hole die transition-duration aus der CSS Variable
    this.dialogStyles = getComputedStyle(this.dialog);
    this.transitionDuration = parseFloat(this.dialogStyles.getPropertyValue("--dialog-animation-speed")) * 1000; // convert to miliseconds
    // ScrollbarHandler Konfiguration
    this.scrollbarOptions = {
      scrollbarThreshold: 5,
      ...options  // Hier fügen wir die übergebenen Optionen hinzu
    };
    // Bind methods
    this.onKeyDown = this.onKeyDown.bind(this);
    this.removeStyles = this.removeStyles.bind(this);
    this.init();
  }
	init() {
  	this.toggler.addEventListener("click", () => this.toggle());
    this.dialogClose.addEventListener("click", () => this.hide());
    this.dialogBackdrop.addEventListener("click", () => this.hide());
    return this;
  }
  // add ESC trigger so this.activeClass will also be removed
  onKeyDown(e) {
    if ("Escape" === e.key) {
      e.preventDefault();
      this.dialogClose.click();
    }
  }
  // toggle between show & hide
  toggle() {
    if (this.dialog.classList.contains(this.activeClass)) {
      this.hide();
    } else {
      this.show();
    }
  }
  // show dialog, add event listener for ESC key, add overflow hidden on body to disable scroll
  show() {
  	document.documentElement.style.overflow = "hidden";
    console.log("test")
    window.addEventListener("keydown", this.onKeyDown);
    this.scrollbarHandler = new MenuScrollbarHandler(this.scrollbarOptions);
    this.dialog.classList.add(this.activeClass);
    this.dialog.showModal();
  }
  // hide dialog, remove event listener for ESC key, remove overflow hidden on body to enable scroll
  hide() {
    window.removeEventListener("keydown", this.onKeyDown);
    this.dialog.classList.remove(this.activeClass);
    this.dialog.close();
    setTimeout(() => {
      this.removeStyles();
      // Cleanup ScrollbarHandler
      if (this.scrollbarHandler) {
        this.scrollbarHandler.destroy();
        this.scrollbarHandler = null;
      }
    }, this.transitionDuration);
  }
  // remove styles from html
  removeStyles() {
  	document.documentElement.style.overflow = "";
  }
}
window.Webflow ||= [];
window.Webflow.push(async () => {	
  const menuDialogElements = document.querySelectorAll("[data-menu-dialog-toggle]");
  if (menuDialogElements.length > 0) {
  	menuDialogElements.forEach(element => {
    	new MenuDialog(element);
  	});
  }
});
</script>
  </div>
  <div class="page-wrapper">
    <div class="animations-css w-embed">
      <style>
  .hero-header .hero-heading {
    -webkit-animation: fadeIn 1000ms backwards;
    animation: fadeIn 1000ms backwards;
  }
  .hero-header .subheading {
    -webkit-animation: fadeIn 1200ms backwards;
    animation: fadeIn 1200ms backwards;
    -webkit-animation-delay: 400ms;
    animation-delay: 400ms;
  }
  .hero-header .cta-button {
    -webkit-animation: fadeIn 1200ms backwards;
    animation: fadeIn 1200ms backwards;
    -webkit-animation-delay: 800ms;
    animation-delay: 800ms;
  }
  @-webkit-keyframes fadeIn {
    from {
      opacity: 0;
      -webkit-transform: translateY(-10px);
      -ms-transform: translateY(-10px);
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  @keyframes fadeIn {
    from {
      opacity: 0;
      -webkit-transform: translateY(-10px);
      -ms-transform: translateY(-10px);
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  .hero-img-target.visible {
    -webkit-animation: heroRotate 1400ms forwards;
    animation: heroRotate 1400ms forwards;
    -webkit-animation-delay: 400ms;
    animation-delay: 400ms;
  }
  @-webkit-keyframes heroRotate {
    0% {
      -webkit-transform: rotateX(25deg);
      -ms-transform: rotateX(25deg);
      transform: rotateX(25deg);
    }
    25% {
      -webkit-transform: rotateX(25deg) scale(0.9);
      -ms-transform: rotateX(25deg) scale(0.9);
      transform: rotateX(25deg) scale(0.9);
    }
    60%,
    to {
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  @keyframes heroRotate {
    0% {
      -webkit-transform: rotateX(25deg);
      -ms-transform: rotateX(25deg);
      transform: rotateX(25deg);
    }
    25% {
      -webkit-transform: rotateX(25deg) scale(0.9);
      -ms-transform: rotateX(25deg) scale(0.9);
      transform: rotateX(25deg) scale(0.9);
    }
    60%,
    to {
      -webkit-transform: none;
      -ms-transform: none;
      transform: none;
    }
  }
  .visible .hero-img-blur {
    -webkit-animation: heroBlur 4.1s ease-out forwards;
    animation: heroBlur 4.1s ease-out forwards;
    -webkit-animation-delay: 600ms;
    animation-delay: 600ms;
  }
  @-webkit-keyframes heroBlur {
    0% {
      opacity: 0;
      -webkit-animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
      animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
    }
    10% {
      opacity: 1;
      -webkit-animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
      animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
    }
    100% {
      opacity: 0.2;
    }
  }
  @keyframes heroBlur {
    0% {
      opacity: 0;
      -webkit-animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
      animation-timing-function: cubic-bezier(0.74, 0.25, 0.76, 1);
    }
    10% {
      opacity: 1;
      -webkit-animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
      animation-timing-function: cubic-bezier(0.12, 0.01, 0.08, 0.99);
    }
    100% {
      opacity: 0.2;
    }
  }
  .hero-lines path {
    stroke-dasharray: 1;
    stroke-dashoffset: 1;
    stroke: white;
    stroke-opacity: 0.2;
  }
  .visible .hero-lines path {
    -webkit-animation: heroLines 1200ms ease-out forwards;
    animation: heroLines 1200ms ease-out forwards;
    -webkit-animation-delay: 0s;
    animation-delay: 0s;
  }
  @-webkit-keyframes heroLines {
    from {
      stroke-dashoffset: 1;
    }
    50% {
      stroke-dashoffset: 0;
    }
    99% {
      stroke-dashoffset: 0;
    }
    100% {
      visibility: hidden;
    }
  } /*!sc*/
  @keyframes heroLines {
    from {
      stroke-dashoffset: 1;
    }
    50% {
      stroke-dashoffset: 0;
    }
    99% {
      stroke-dashoffset: 0;
    }
    100% {
      visibility: hidden;
    }
  }
  .visible .hero-img {
    -webkit-animation: heroImg 400ms forwards;
    animation: heroImg 400ms forwards;
    -webkit-animation-delay: 680ms;
    animation-delay: 680ms;
  }
  @-webkit-keyframes heroImg {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  @keyframes heroImg {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
@media only screen and (min-width: 992px){
  .lightning svg g:last-of-type path {
    fill: transparent;
    -webkit-animation: lightning 2250ms linear infinite;
    animation: lightning 2250ms linear infinite;
    -webkit-animation-delay: calc(var(--index) * 20ms);
    animation-delay: calc(var(--index) * 20ms);
  }
  }
  @-webkit-keyframes lightning {
    0%,
    9%,
    11%,
    100% {
      fill: transparent;
    }
    10% {
      fill: rgba(255, 255, 255, 0.2);
      fill: #fff;
    }
  }
  @keyframes lightning {
    0%,
    9%,
    11%,
    100% {
      fill: transparent;
    }
    10% {
      fill: rgba(255, 255, 255, 0.2);
      fill: #fff;
    }
  }
  .visible .integrations-bg-circle {
    opacity: 1;
    -webkit-animation: integrationsBg 3400ms infinite backwards;
    animation: integrationsBg 3400ms infinite backwards;
    -webkit-animation-delay: calc(500ms + var(--delay, 0s));
    animation-delay: calc(500ms + var(--delay, 0s));
  }
  @-webkit-keyframes integrationsBg {
    from {
      opacity: 0;
      -webkit-transform: translate(-50%, -50%) scale(0.9);
      -ms-transform: translate(-50%, -50%) scale(0.9);
      transform: translate(-50%, -50%) scale(0.9);
    }
    40%,
    50% {
      opacity: var(--opacity);
      -webkit-transform: translate(-50%, -50%) scale(1);
      -ms-transform: translate(-50%, -50%) scale(1);
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0;
    }
  }
  @keyframes integrationsBg {
    from {
      opacity: 0;
      -webkit-transform: translate(-50%, -50%) scale(0.9);
      -ms-transform: translate(-50%, -50%) scale(0.9);
      transform: translate(-50%, -50%) scale(0.9);
    }
    40%,
    50% {
      opacity: var(--opacity);
      -webkit-transform: translate(-50%, -50%) scale(1);
      -ms-transform: translate(-50%, -50%) scale(1);
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0;
    }
  }</div><div class="global-styles w-embed"><style>
* {
    -webkit-font-smoothing: antialiased; -moz-font-smoothing: antialiased; -o-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
}
.heading-style-h2 {
  font-smooth: always; /* or auto, high, medium, small - but support is inconsistent */
}
:root {
  /* ... existing code ... */
  --selection-background: var(--wölfli-black, #262725);
  --selection-color: var(--wölfli-beige, #f7f3eb);
}
/* Global selection styles */
::selection {
  background-color: var(--selection-background) !important;
  color: var(--selection-color) !important;
  text-shadow: none;
}
::-moz-selection {
  background-color: var(--selection-background) !important;
  color: var(--selection-color) !important;
  text-shadow: none;
}
.text-size-regular {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h3 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h4 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
.heading-style-h5 {
-webkit-hyphens: auto;
   -moz-hyphens: auto;
        hyphens: auto;
hyphenate-limit-chars: auto 3;
hyphenate-limit-lines: 4;
}
/* Set color style to inherit */
.inherit-color * {
    color: inherit;
}
/* Focus state style for keyboard navigation for the focusable elements */
*[tabindex]:focus-visible,
  input[type="file"]:focus-visible {
   outline: 0.125rem solid #4d65ff;
   outline-offset: 0.125rem;
}
/* Get rid of top margin on first element in any rich text element */
.w-richtext > :not(div):first-child, .w-richtext > div:first-child > :first-child {
  margin-top: 0 !important;
}
/* Get rid of bottom margin on last element in any rich text element */
.w-richtext>:last-child, .w-richtext ol li:last-child, .w-richtext ul li:last-child {
	margin-bottom: 0 !important;
}
/* Prevent all click and hover interaction with an element */
.pointer-events-off {
	pointer-events: none;
}
/* Enables all click and hover interaction with an element */
.pointer-events-on {
  pointer-events: auto;
}
/* Create a class of .div-square which maintains a 1:1 dimension of a div */
.div-square::after {
	content: "";
	display: block;
	padding-bottom: 100%;
}
/* Make sure containers never lose their center alignment */
.container-medium,.container-small, .container-large {
	margin-right: auto !important;
  margin-left: auto !important;
}
/* 
Make the following elements inherit typography styles from the parent and not have hardcoded values. 
Important: You will not be able to style for example "All Links" in Designer with this CSS applied.
Uncomment this CSS to use it in the project. Leave this message for future hand-off.
*/
/*
a,
.w-input,
.w-select,
.w-tab-link,
.w-nav-link,
.w-dropdown-btn,
.w-dropdown-toggle,
.w-dropdown-link {
  color: inherit;
  text-decoration: inherit;
  font-size: inherit;
}
*/
/* Apply "..." after 3 lines of text */
.text-style-3lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}
/* Apply "..." after 2 lines of text */
.text-style-2lines {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}
/* Adds inline flex display */
.display-inlineflex {
  display: inline-flex;
}
/* These classes are never overwritten */
.hide {
  display: none !important;
}
@media screen and (max-width: 991px) {
    .hide, .hide-tablet {
        display: none !important;
    }
}
  @media screen and (max-width: 767px) {
    .hide-mobile-landscape{
      display: none !important;
    }
}
  @media screen and (max-width: 479px) {
    .hide-mobile{
      display: none !important;
    }
}
.margin-0 {
  margin: 0rem !important;
}
.padding-0 {
  padding: 0rem !important;
}
.spacing-clean {
padding: 0rem !important;
margin: 0rem !important;
}
.margin-top {
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-top {
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-right {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
  margin-left: 0rem !important;
}
.padding-right {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
  padding-left: 0rem !important;
}
.margin-bottom {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-bottom {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
.margin-left {
  margin-top: 0rem !important;
  margin-right: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-left {
  padding-top: 0rem !important;
  padding-right: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-horizontal {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
}
.padding-horizontal {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
}
.margin-vertical {
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}
.padding-vertical {
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}
/* Apply "..." at 100% width */
.truncate-width { 
		width: 100%; 
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 
}
/* Removes native scrollbar */
.no-scrollbar {
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-none; 
}
.no-scrollbar::-webkit-scrollbar {
    display: none;
}
.marken-grid {
  display: grid;
  gap: 2rem;
  width: 100%;
}
/* Apply single column layout only for mobile */
@media screen and (max-width: 479px) {
  .marken-grid-row {
    display: grid;
    grid-template-columns: 1fr !important; /* Force single column */
    gap: 1rem;
    width: 100%;
  }
  .marken-grid-card {
    width: 100%;
    max-width: 100%;
  }
}
/* Optional: Add intermediate breakpoint for tablets */
@media screen and (max-width: 767px) and (min-width: 480px) {
  .marken-grid-row {
    gap: 1.5rem;
  }
}
.heading-style-subtitle {
  font-size: var(--font-size-h5);
  line-height: 1.4;
}
/* Utility classes for heading styles */
.heading-style-regular {
  font-weight: var(--font-weight-regular);
}
.heading-style-medium {
  font-weight: 500;
}
.heading-style-bold {
  font-weight: 700;
}
/* Optional: Add responsive margin adjustments 
@media screen and (max-width: 767px) {
  .heading-style-h1,
  .heading-style-h2,
  .heading-style-h3 {
    margin-top: calc(var(--size-24) * 0.75);
    margin-bottom: calc(var(--size-16) * 0.75);
  }
}*/
.hyphen
 {
  word-break: break-word !important;
  -webkit-hyphens: auto !important;
  -moz-hyphens: auto !important;
  -ms-hyphens: auto !important;
  -o-hyphens: auto !important;
  hyphens: auto !important;
}
.cdn-video::-webkit-media-controls-start-playback-button {
    display: none;
}
  .handwriting-svg path {
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
    animation-fill-mode: forwards;
    animation-timing-function: ease-in-out;
  }
  .handwriting-svg.animate path {
    animation-name: draw;
    animation-duration: 2s;
  }
  @keyframes draw {
    to {
      stroke-dashoffset: 0;
    }
  }
  /* Add a small delay for each path */
  .handwriting-svg path:nth-child(1) { animation-delay: 0s; }
  .handwriting-svg path:nth-child(2) { animation-delay: 0.1s; }
  .handwriting-svg path:nth-child(3) { animation-delay: 0.2s; }
  /* Continue for all paths */
</style>
      <style>
/* Fluid Typography System for Woelfli Bauplanung */
:root {
  /* Base responsive variables */
  --fluid-min-width: 320;
  --fluid-max-width: 1200;
  --fluid-screen: 100vw;
  --fluid-bp: calc((var(--fluid-screen) - var(--fluid-min-width) / 16 * 1rem) / (var(--fluid-max-width) - var(--fluid-min-width)));
}
/* Heading Styles */
/* H1 - Main Headings */
/* Heading Styles - Adjusted to maintain hierarchy */
/* H1 - Main Headings */
h1, .heading-style-h1 {
  font-size: clamp(26px, calc(26px + 26 * var(--fluid-bp)), 52px);
  line-height: clamp(31px, calc(31px + 26.72 * var(--fluid-bp)), 56.72px);
}
/* H2 - Section Headings */
h2, .heading-style-h2 {
  font-size: clamp(18px, calc(18px + 10 * var(--fluid-bp)), 28px);
  line-height: clamp(25px, calc(25px + 16.4 * var(--fluid-bp)), 48.4px);
}
/* H3 - Sub-Section Headings */
h3, .heading-style-h3 {
  font-size: clamp(20px, calc(20px + 6 * var(--fluid-bp)), 24px);
  line-height: clamp(26px, calc(26px + 5.6 * var(--fluid-bp)), 30.6px);
}
/* H4 - Minor Headings */
h4, .heading-style-h4 {
  font-size: clamp(18px, calc(18px + 4 * var(--fluid-bp)), 22px);
  line-height: clamp(20px, calc(20px + 2 * var(--fluid-bp)), 22px);
}
/* H5 */
.heading-style-h5 {
  font-size: clamp(20px, calc(20px + 4 * var(--fluid-bp)), 24px);
  line-height: clamp(28px, calc(28px + 5.6 * var(--fluid-bp)), 33.6px);
}
/* H6 */
.heading-style-h6 {
  font-size: clamp(18px, calc(18px + 2 * var(--fluid-bp)), 20px);
  line-height: clamp(24px, calc(24px + 4 * var(--fluid-bp)), 28px);
}
/* Text Size Styles */
/* Large Text */
.text-size-large, .portfolio-expose {
  font-size: clamp(18px, calc(18px + 4 * var(--fluid-bp)), 22px);
  line-height: clamp(28px, calc(28px + 5.88 * var(--fluid-bp)), 33.88px);
}
/* Medium Text */
.text-size-medium {
  font-size: clamp(17px, calc(17px + 2 * var(--fluid-bp)), 18px);
  line-height: clamp(24px, calc(24px + 3 * var(--fluid-bp)), 27px);
}
/* Regular Text */
.text-size-regular, p {
  font-size: clamp(16px, calc(16px + 1 * var(--fluid-bp)), 16px);
  line-height: clamp(22px, calc(22px + 2 * var(--fluid-bp)), 24px);
}
/* Small Text */
.text-size-small {
  font-size: clamp(13px, calc(13px + 1 * var(--fluid-bp)), 14px);
  line-height: clamp(19px, calc(19px + 2 * var(--fluid-bp)), 21px);
}
/* Tiny Text */
.text-size-tiny {
  font-size: clamp(11px, calc(11px + 1 * var(--fluid-bp)), 12px);
  line-height: clamp(16px, calc(16px + 2 * var(--fluid-bp)), 18px);
}
/* Special Elements */
/* Buttons */
.button, .btn {
  font-size: clamp(15px, calc(15px + 1 * var(--fluid-bp)), 16px);
  line-height: clamp(22px, calc(22px + 2 * var(--fluid-bp)), 24px);
}
/* Navigation Links */
.nav-link, .menu-link {
  font-size: clamp(14px, calc(14px + 2 * var(--fluid-bp)), 16px);
  line-height: clamp(20px, calc(20px + 4 * var(--fluid-bp)), 24px);
}
.marken-cta:hover .text-color-white {
    color: #4b4b4b;
}
</style>
    </div>
    <main class="main-wrapper">
      <div class="page-header">
        <header class="section-header-2">
          <div data-collapse="medium" data-animation="default" data-duration="400" fs-scrolldisable-element="smart-nav" data-easing="ease" data-easing2="ease" role="banner" class="navbar2_component w-nav">
            <div class="navbar2_container">
              <a href="index.html" aria-current="page" class="navbar2_logo-link w-nav-brand w--current">
                <div class="w-embed"><svg width="81" height="34" viewbox="0 0 81 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.330078 11.272V10.568C1.01609 10.4199 2.03156 10.3631 3.37649 10.3974C3.86031 10.4082 4.14644 10.6384 4.2349 11.0879C4.52916 12.5682 5.3966 16.9451 6.83722 24.2186C6.86791 24.3757 6.91575 24.484 6.98074 24.5436C7.0005 24.5617 7.02458 24.5744 7.05067 24.5806C7.07676 24.5868 7.10399 24.5862 7.12978 24.5788C7.15557 24.5715 7.17905 24.5577 7.19799 24.5387C7.21693 24.5198 7.2307 24.4962 7.23799 24.4704L10.9262 10.8171C10.9582 10.6997 11.0278 10.596 11.1242 10.5221C11.2206 10.4482 11.3385 10.4082 11.4596 10.4082H13.1034C13.2349 10.4083 13.3627 10.4517 13.467 10.5315C13.5713 10.6113 13.6462 10.7231 13.6801 10.8496C15.6786 18.2422 16.9053 22.7536 17.3602 24.3838C17.418 24.5932 17.5209 24.6275 17.6689 24.4867C17.6899 24.4673 17.7042 24.4427 17.7095 24.4163L20.4039 10.9227C20.4334 10.7725 20.5143 10.6373 20.6328 10.54C20.7514 10.4427 20.9002 10.3894 21.0538 10.3893H23.8592C23.9259 10.3891 23.9918 10.4038 24.0521 10.4323C24.1124 10.4608 24.1656 10.5023 24.2078 10.5539C24.2501 10.6055 24.2803 10.6659 24.2964 10.7306C24.3124 10.7953 24.3139 10.8628 24.3006 10.9281L19.8569 33.1873C19.8269 33.3347 19.747 33.4672 19.6305 33.5625C19.5141 33.6577 19.3683 33.7098 19.2178 33.7099H17.0434C16.6733 33.7099 16.4404 33.5321 16.3447 33.1764L12.4724 18.8001C12.4651 18.7731 12.4506 18.7487 12.4305 18.7296C12.4105 18.7105 12.3856 18.6974 12.3587 18.6917C12.2197 18.6611 12.1258 18.7414 12.077 18.9327C11.1961 22.3086 9.92064 27.0782 8.25075 33.2414C8.2134 33.3776 8.13275 33.4977 8.0212 33.5833C7.90966 33.6689 7.77339 33.7153 7.63335 33.7153H5.42368C5.2609 33.7153 5.10314 33.659 4.97724 33.5558C4.85134 33.4526 4.76509 33.309 4.73316 33.1493L0.330078 11.272Z" fill="white"></path>
                    <path d="M48.9372 29.0686C49.5952 29.8268 50.4428 29.8024 51.3337 30.0082C51.4495 30.0342 51.5532 30.0985 51.6283 30.1908C51.7034 30.2831 51.7456 30.398 51.748 30.5173C51.7679 31.2647 51.7778 31.9895 51.7778 32.6918C51.776 33.2424 51.4998 33.5195 50.9492 33.5231C49.2648 33.5393 47.5128 33.2442 46.2401 32.0716C44.6289 30.5823 44.3066 27.7823 44.3012 25.5076C44.2868 18.3623 44.2877 10.605 44.3039 2.23575C44.3039 1.45858 44.2118 0.857422 45.2517 0.857422C45.9558 0.857422 46.7203 0.864643 47.5453 0.879085C47.7304 0.882632 47.9067 0.958657 48.0363 1.09082C48.166 1.22298 48.2386 1.40073 48.2385 1.58585C48.2476 6.83381 48.2448 14.7436 48.2304 25.3154C48.2277 26.515 48.2196 28.2399 48.9372 29.0686Z" fill="white"></path>
                    <path d="M59.1849 14.1371C59.0351 14.1371 58.9602 14.2121 58.9602 14.3619V32.7487C58.9602 33.2614 58.7038 33.5177 58.1911 33.5177H55.7757C55.2774 33.5177 55.0283 33.2677 55.0283 32.7676V14.3727C55.0283 14.2157 54.9488 14.1371 54.79 14.1371H54.2376C54.0803 14.1371 53.9294 14.0747 53.8182 13.9634C53.707 13.8522 53.6445 13.7014 53.6445 13.5441V11.218C53.6445 11.0794 53.6996 10.9465 53.7976 10.8484C53.8956 10.7504 54.0285 10.6954 54.1672 10.6954H54.7575C54.8257 10.6954 54.8911 10.6683 54.9394 10.62C54.9876 10.5718 55.0147 10.5063 55.0147 10.4381C55.0147 9.51381 55.0319 8.62471 55.0662 7.77081C55.1203 6.4349 55.3803 5.17842 55.8461 4.00138C56.0717 3.42549 56.4445 2.87578 56.9644 2.35225C58.183 1.12827 60.3385 0.800613 62.0499 0.879143C62.5698 0.903514 62.7567 1.19055 62.7756 1.68881C62.8063 2.55354 62.8009 3.2928 62.7594 3.9066C62.7341 4.29654 62.5265 4.50415 62.1366 4.52942C60.4739 4.63503 59.2851 5.13871 59.0766 6.96926C58.9557 8.02896 58.9232 9.17351 58.9791 10.4029C58.9882 10.5979 59.0902 10.6954 59.2851 10.6954H61.4406C61.8956 10.6954 62.123 10.9228 62.123 11.3778V13.4547C62.123 13.5444 62.1054 13.6331 62.0711 13.7159C62.0368 13.7987 61.9865 13.8739 61.9232 13.9373C61.8598 14.0006 61.7846 14.0509 61.7018 14.0852C61.619 14.1195 61.5303 14.1371 61.4406 14.1371H59.1849Z" fill="white"></path>
                    <path d="M70.2085 29.0634C70.8666 29.8216 71.7141 29.7972 72.6051 30.003C72.7204 30.0289 72.8238 30.0928 72.8989 30.1846C72.9739 30.2763 73.0163 30.3906 73.0194 30.5094C73.0392 31.2568 73.0501 31.9816 73.0519 32.6839C73.0501 33.2345 72.7738 33.5116 72.2232 33.5152C70.5389 33.5341 68.7869 33.239 67.5141 32.0665C65.9029 30.5798 65.578 27.7798 65.5726 25.5052C65.5545 18.3617 65.5518 10.6062 65.5644 2.23868C65.5644 1.46151 65.4724 0.860352 66.5122 0.860352C67.2163 0.860352 67.9808 0.86667 68.8058 0.879307C68.9909 0.882853 69.1672 0.958879 69.2969 1.09104C69.4265 1.2232 69.4991 1.40095 69.4991 1.58607C69.5099 6.83403 69.5108 14.7421 69.5018 25.3102C69.4991 26.5098 69.4909 28.2347 70.2085 29.0634Z" fill="white"></path>
                    <path d="M30.3661 8.10338C31.6179 8.10338 32.6327 7.08862 32.6327 5.83684C32.6327 4.58507 31.6179 3.57031 30.3661 3.57031C29.1144 3.57031 28.0996 4.58507 28.0996 5.83684C28.0996 7.08862 29.1144 8.10338 30.3661 8.10338Z" fill="white"></path>
                    <path d="M36.3356 8.10684C37.5889 8.10684 38.6049 7.09087 38.6049 5.8376C38.6049 4.58433 37.5889 3.56836 36.3356 3.56836C35.0824 3.56836 34.0664 4.58433 34.0664 5.8376C34.0664 7.09087 35.0824 8.10684 36.3356 8.10684Z" fill="white"></path>
                    <path d="M78.2829 8.10684C79.5362 8.10684 80.5521 7.09087 80.5521 5.8376C80.5521 4.58433 79.5362 3.56836 78.2829 3.56836C77.0296 3.56836 76.0137 4.58433 76.0137 5.8376C76.0137 7.09087 77.0296 8.10684 78.2829 8.10684Z" fill="white"></path>
                    <path d="M33.3314 10.0234C36.1693 10.0234 38.4331 11.4965 39.5353 14.1314C40.5724 16.6037 40.3341 19.5445 40.3341 21.8679C40.3341 24.1913 40.5751 27.1321 39.538 29.6044C38.4331 32.2392 36.172 33.7151 33.3341 33.7151C30.4962 33.7151 28.2351 32.2392 27.1303 29.6071C26.0931 27.1321 26.3341 24.1913 26.3341 21.8679C26.3314 19.5472 26.0931 16.6064 27.1276 14.1314C28.2324 11.4993 30.4935 10.0234 33.3314 10.0234ZM30.2633 21.8706C30.2633 24.9197 30.2715 26.5607 30.2877 26.7936C30.4177 28.5538 31.3763 30.081 33.3368 30.081C35.3001 30.0837 36.2587 28.5538 36.3887 26.7936C36.4049 26.5607 36.413 24.9197 36.413 21.8706C36.413 18.8197 36.4049 17.1778 36.3887 16.9449C36.2587 15.1847 35.3001 13.6575 33.3395 13.6575C31.3763 13.6575 30.4177 15.1847 30.2877 16.9449C30.2715 17.1778 30.2633 18.8197 30.2633 21.8706Z" fill="white"></path>
                    <path d="M79.5267 10.1943H77.0029C76.614 10.1943 76.2988 10.5096 76.2988 10.8984V32.8163C76.2988 33.2052 76.614 33.5204 77.0029 33.5204H79.5267C79.9155 33.5204 80.2307 33.2052 80.2307 32.8163V10.8984C80.2307 10.5096 79.9155 10.1943 79.5267 10.1943Z" fill="white"></path>
                  </svg></div>
              </a>
              <nav role="navigation" id="w-node-d392c3b2-4450-74b5-1131-dbdbdf68d69c-df68d698" class="navbar2_menu is-page-height-tablet w-nav-menu">
                <a href="team.html" class="navbar2_link text-size-medium w-nav-link">Team</a>
                <a href="leistungen.html" class="navbar2_link text-size-medium w-nav-link">Leistungen</a>
                <a href="marken.html" class="navbar2_link text-size-medium w-nav-link">Marken</a>
                <a href="projekte.html" class="navbar2_link text-size-medium w-nav-link">Projekte</a>
                <a href="karriere.html" class="navbar2_link text-size-medium w-nav-link">Karriere</a>
                <a href="kontakt.html" class="navbar2_link text-size-medium w-nav-link">Kontakt</a>
              </nav>
              <a href="identitaet.html" class="navbar-icon-link w-nav-brand">
                <div class="icon-embed-small w-embed"><svg width="191" height="194" viewbox="0 0 191 194" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M183.945 9.64457C183.926 9.25248 183.877 8.86021 183.807 8.46873C183.796 8.41001 183.782 8.3527 183.77 8.29435C183.713 8.00986 183.642 7.72715 183.556 7.44559C183.532 7.36472 183.507 7.28434 183.481 7.20432C183.393 6.94382 183.292 6.68607 183.18 6.43009C183.148 6.35709 183.12 6.28288 183.086 6.21085C183.064 6.16373 183.048 6.11533 183.025 6.0684C182.727 5.46134 182.366 4.90635 181.96 4.39841C181.94 4.37302 181.917 4.34946 181.897 4.32426C181.692 4.07298 181.474 3.83591 181.246 3.61082C181.204 3.5687 181.16 3.52781 181.117 3.48655C180.884 3.26585 180.642 3.05839 180.39 2.86509C180.361 2.84287 180.333 2.81937 180.304 2.79746C179.714 2.35624 179.076 1.99046 178.404 1.70524C178.361 1.68674 178.317 1.67112 178.273 1.65342C177.984 1.53599 177.69 1.43412 177.391 1.34678C177.336 1.33079 177.282 1.314 177.226 1.29911C175.826 0.918678 174.336 0.871864 172.877 1.19956C172.872 1.2006 172.868 1.20145 172.863 1.20255C172.178 1.35758 171.5 1.5853 170.843 1.91074L95.4955 38.9214L20.1513 1.91074C19.4994 1.58891 18.8269 1.36234 18.1481 1.20725C18.1288 1.20286 18.1095 1.19938 18.0902 1.19517C17.7393 1.11741 17.3874 1.0615 17.0345 1.02634C17.0257 1.02543 17.0169 1.02433 17.008 1.02348C15.8966 0.916237 14.788 1.01621 13.7309 1.30711C13.7045 1.31437 13.6783 1.32243 13.6519 1.32993C13.3104 1.42722 12.9752 1.54295 12.6473 1.6793C12.6402 1.68223 12.6331 1.68473 12.6261 1.68766C11.5992 2.11759 10.6521 2.73661 9.83469 3.52372C9.81607 3.5416 9.7974 3.55912 9.7789 3.57719C9.52292 3.82798 9.27835 4.09226 9.051 4.37589C9.05002 4.37711 9.04886 4.37827 9.04788 4.37949C8.63694 4.89262 8.27115 5.45353 7.97019 6.0684C7.94718 6.11533 7.93112 6.16373 7.90897 6.21085C7.87516 6.28288 7.84671 6.35709 7.81473 6.43009C7.70243 6.68601 7.60135 6.94364 7.51425 7.20408C7.48746 7.2844 7.46268 7.36503 7.43808 7.44621C7.35263 7.72739 7.28153 8.00974 7.22446 8.29374C7.21268 8.35239 7.1987 8.40995 7.18808 8.46891C7.11746 8.86027 7.06858 9.25236 7.04996 9.64433L0.213412 124.731C0.0229827 127.923 1.52738 130.989 4.17435 132.792L90.3748 191.539C91.9173 192.593 93.7074 193.12 95.4973 193.12C97.2874 193.12 99.0774 192.593 100.62 191.539L186.814 132.792C189.461 130.989 190.965 127.923 190.775 124.731L183.945 9.64457ZM95.4973 173.004L18.685 120.655L24.3978 24.4765L73.8308 49.563L45.7385 63.3619C41.2253 65.5772 39.3655 71.0298 41.5808 75.5429C43.1614 78.7612 46.3986 80.6338 49.7565 80.6338C51.1022 80.6338 52.4733 80.3354 53.7619 79.7007L95.0284 59.4306C95.3401 59.4468 95.6509 59.4469 95.9626 59.4309L137.227 79.7007C138.515 80.3354 139.886 80.6338 141.232 80.6338C144.59 80.6338 147.827 78.7612 149.408 75.5429C151.623 71.0298 149.763 65.5772 145.25 63.3619L117.161 49.5639L166.591 24.4765L172.303 120.655L95.4973 173.004Z" fill="white"></path>
                  </svg></div>
              </a>
              <div id="w-node-_7728f4df-2d5d-59ec-10e7-ad72bf765fec-bf765fec" class="navbar2_button-wrapper">
                <a href="#" class="button is-navbar2-button w-button">Button</a>
                <div class="navbar2_menu-button w-nav-button">
                  <div class="menu-icon2">
                    <div class="menu-icon2_line-top"></div>
                    <div class="menu-icon2_line-middle">
                      <div class="menu-icon2_line-middle-inner"></div>
                    </div>
                    <div class="menu-icon2_line-bottom"></div>
                  </div>
                </div>
                <div class="block-text_action"><button type="button" data-button="" data-menu-dialog-toggle="" aria-label="Open Menu" class="button_dialog is-menu-button">
                    <div class="menu-text hide">Menü</div><span class="button_dialog-menu-line-wrap"><span class="button_dialog-menu-line"></span><span class="button_dialog-menu-line"></span><span class="button_dialog-menu-line"></span></span><span class="button_dialog-bg"></span>
                  </button>
                  <dialog data-menu-dialog="" data-theme="light" class="menu-dialog">
                    <div class="menu-dialog_outer">
                      <div class="menu-dialog_inner">
                        <div class="menu-dialog_header"><span class="menu-dialog_header-text">Menü</span><button type="button" data-menu-dialog-close="" aria-label="Close Dialog" autofocus="" class="menu-dialog_button-close"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 24 24" fill="none" aria-hidden="true" class="icon">
                              <path fill-rule="evenodd" clip-rule="evenodd" d="M6.75827 17.2426L12.0009 12M17.2435 6.75736L12.0009 12M12.0009 12L6.75827 6.75736M12.0009 12L17.2435 17.2426" fill="currentColor" stroke="#000000" stroke-width="1.5" color="#000000"></path>
                            </svg></button></div>
                        <nav class="menu-dialog_nav">
                          <a href="identitaet.html" class="navbar-icon-link icon-link-mobile w-nav-brand"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 32 32" fill="none" class="icon-embed-small">
                              <path d="M30.5892 1.59116C30.586 1.52648 30.578 1.46178 30.5664 1.39721C30.5646 1.38752 30.5623 1.37807 30.5603 1.36844C30.5509 1.32152 30.5392 1.27488 30.525 1.22844C30.5211 1.2151 30.5169 1.20184 30.5126 1.18864C30.4981 1.14567 30.4815 1.10316 30.463 1.06094C30.4577 1.04889 30.4531 1.03665 30.4475 1.02477C30.4439 1.017 30.4412 1.00902 30.4374 1.00128C30.3883 0.901142 30.3287 0.809597 30.2618 0.725813C30.2585 0.721625 30.2547 0.717739 30.2514 0.713582C30.2175 0.672134 30.1816 0.63303 30.144 0.595901C30.1371 0.588954 30.1298 0.582209 30.1227 0.575403C30.0843 0.538999 30.0444 0.504779 30.0028 0.472894C29.998 0.469229 29.9934 0.465353 29.9886 0.461739C29.8913 0.38896 29.786 0.328625 29.6752 0.281579C29.6681 0.278527 29.6608 0.275951 29.6536 0.273031C29.6059 0.253661 29.5574 0.236858 29.5081 0.222451C29.499 0.219814 29.4901 0.217044 29.4809 0.214588C29.25 0.151837 29.0042 0.144115 28.7635 0.198168C28.7627 0.198339 28.762 0.198479 28.7612 0.198661C28.6482 0.224233 28.5364 0.261795 28.428 0.315476L15.9996 6.42033L3.57166 0.315476C3.46413 0.26239 3.3532 0.225018 3.24124 0.199436C3.23805 0.198712 3.23487 0.198138 3.23169 0.197444C3.1738 0.184617 3.11576 0.175395 3.05755 0.169595C3.0561 0.169445 3.05465 0.169264 3.05318 0.169124C2.86985 0.151434 2.68699 0.167924 2.51263 0.215908C2.50827 0.217105 2.50395 0.218435 2.49959 0.219672C2.44326 0.23572 2.38797 0.254809 2.33389 0.2773C2.33272 0.277783 2.33154 0.278196 2.33039 0.278679C2.161 0.349595 2.00478 0.451702 1.86995 0.581534C1.86688 0.584484 1.8638 0.587373 1.86075 0.590354C1.81853 0.631721 1.77818 0.675314 1.74068 0.722098L1.74017 0.722692C1.67238 0.807332 1.61205 0.899853 1.56241 1.00128C1.55861 1.00902 1.55596 1.017 1.55231 1.02477C1.54673 1.03665 1.54204 1.04889 1.53676 1.06094C1.51824 1.10315 1.50157 1.14564 1.4872 1.1886C1.48278 1.20185 1.47869 1.21515 1.47463 1.22854C1.46054 1.27492 1.44881 1.3215 1.4394 1.36834C1.43746 1.37802 1.43515 1.38751 1.4334 1.39724C1.42175 1.46179 1.41369 1.52646 1.41061 1.59112L0.282937 20.5745C0.251526 21.101 0.499674 21.6067 0.936287 21.9041L15.1549 31.5944C15.4094 31.7682 15.7046 31.8551 15.9999 31.8551C16.2951 31.8551 16.5904 31.7682 16.8448 31.5944L31.0624 21.9041C31.499 21.6067 31.7471 21.101 31.7158 20.5745L30.5892 1.59116ZM15.9999 28.537L3.3298 19.9022L4.27211 4.03766L12.426 8.17564L7.79223 10.4517C7.04778 10.8172 6.74101 11.7166 7.10642 12.461C7.36714 12.9918 7.90111 13.3007 8.45499 13.3007C8.67696 13.3007 8.90312 13.2515 9.11568 13.1468L15.9225 9.80329C15.9739 9.80596 16.0252 9.80598 16.0766 9.80334L22.8831 13.1468C23.0956 13.2515 23.3217 13.3007 23.5437 13.3007C24.0976 13.3007 24.6316 12.9918 24.8924 12.461C25.2577 11.7166 24.9509 10.8172 24.2065 10.4517L19.5733 8.17579L27.7267 4.03766L28.6688 19.9022L15.9999 28.537Z" fill="currentColor"></path>
                            </svg></a>
                          <ul role="list" class="menu-dialog_nav-list w-list-unstyled">
                            <li class="menu-dialog_nav-list-item">
                              <a href="index.html" aria-current="page" class="menu-dialog_nav-link u-h2 w-inline-block w--current"><span class="menu-dialog_nav-link-outer">Start</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="team.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Team</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="leistungen.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Leistungen</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="marken.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Marken</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="projekte.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Projekte</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="identitaet.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Wölfli Identität</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="karriere.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Karriere</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item">
                              <a href="kontakt.html" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer">Kontakt</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item hide">
                              <a href="tel:+41445352222" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer phone-number text-size-regular">+41 44 535 22 22</span></a>
                            </li>
                            <li class="menu-dialog_nav-list-item hide">
                              <a href="mailto:<EMAIL>" class="menu-dialog_nav-link u-h2 w-inline-block"><span class="menu-dialog_nav-link-outer email"><EMAIL></span></a>
                            </li>
                          </ul>
                        </nav>
                      </div><button type="button" tabindex="-1" data-menu-dialog-backdrop="" class="menu-dialog_backdrop"></button>
                    </div>
                  </dialog>
                </div>
              </div>
              <div class="button-row">
                <a data-cursor="Ruf den Chef direkt an" href="#" class="button w-inline-block">
                  <div class="header-phone-svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" stroke-width="1.5" viewbox="0 0 24 24" fill="none" color="#000000" class="phone-svg">
                      <path d="M22 5H16M16 5L19 2M16 5L19 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                      <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg></div>
                  <p class="button-text">+41 44 535 22 22</p>
                  <div class="button-bg"></div>
                </a>
              </div>
              <div class="header-phone">
                <div class="header-phone-svg"><svg xmlns="http://www.w3.org/2000/svg" width="100%" stroke-width="1.5" viewbox="0 0 24 24" fill="none" color="#000000">
                    <path d="M22 5H16M16 5L19 2M16 5L19 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                    <path d="M18.1182 14.702L14 15.5C11.2183 14.1038 9.5 12.5 8.5 10L9.26995 5.8699L7.81452 2L4.0636 2C2.93605 2 2.04814 2.93178 2.21654 4.04668C2.63695 6.83 3.87653 11.8765 7.5 15.5C11.3052 19.3052 16.7857 20.9564 19.802 21.6127C20.9668 21.8662 22 20.9575 22 19.7655L22 16.1812L18.1182 14.702Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                  </svg></div>
              </div>
              <div class="cursor">
                <p class="cursor-paragraph">Learn more</p>
              </div>
            </div>
          </div>
          <div class="header_component-2">
            <div class="header_card-2 text-color-alternate-2">
              <div class="header-content">
                <div class="padding-global padding-custom1">
                  <div class="container-large">
                    <div class="padding-section-large">
                      <div class="header_content">
                        <div class="max-width-large">
                          <div>
                            <h1 class="heading-style-h1">Engagement für Bauprojekte.  <span class="header-h1-span">Ganzheitlich im Ansatz, präzise in der Umsetzung, stark im Ergebnis.</span></h1>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="header-gradient landing-gradient"></div>
              <div class="header_background-video">
                <div class="vimeo-wrapper"><video loading="eager" preload="metadata" title="Wölfli Header Video" loop="" playsinline="" muted="" autoplay="" defaultmuted="" class="cdn-video">
                    <source src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/video/woelfli-header-vid_1080.mp4" type="video/mp4" data-resolution="1080p" data-format="mp4">
                    <source src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/video/woelfli-header-vid_720.mp4" type="video/mp4" data-resolution="720p" data-format="mp4">
                    <source src="https://buerowilden.fra1.cdn.digitaloceanspaces.com/woelfli/video/header-video25fps-576p.mp4" type="video/mp4" data-resolution="576p" data-format="mp4">
                  </video></div>
              </div>
            </div>
          </div>
        </header>
        <section class="intro-section">
          <div class="padding-global">
            <div class="container-large">
              <div class="padding-section-large">
                <div class="header-intro">
                  <div blocks-name="max-width-xlarge" blocks-slot-children="ST265" class="max-width-large header-text-width">
                    <div class="margin-bottom margin-small intro-margin">
                      <div blocks-non-deletable="true" blocks-name="header105_heading-wrapper" class="header-intro-wrapper">
                        <h1 class="heading-style-h2 text-weight-medium line-height">Kompetent. Engagiert. Zielorientiert. </h1>
                        <h1 class="heading-style-h2 hyphen">Wir sind erfahrene Spezialisten in Planung, Bauleitung und Baurealisierung. Mit voller Aufmerksamkeit bringen wir Projekte voran. Als Teil von interdisziplinären Projektteams setzen wir auf Transparenz, Zusammenarbeit und vorausschauendes Handeln – für nachhaltige und erfolgreiche Bauprojekte.</h1>
                      </div>
                      <a role="button" href="leistungen.html" class="link-block-3 w-inline-block">
                        <div data-w-id="da8b3bb7-03c9-5824-f8d1-5430c16ee583" style="-webkit-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-moz-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);-ms-transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);transform:translate3d(0, 5rem, 0) scale3d(1, 1, 1) rotateX(0) rotateY(0) rotateZ(0) skew(0, 0);opacity:0" class="cta-button">
                          <div class="heading-style-h4 cta-text">So arbeiten wir<br></div>
                          <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                            </svg></div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <section class="stark-section">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="team-section-heading">
                <div class="margin-bottom margin-xxlarge">
                  <div class="max-width-full">
                    <div class="headline-tag-group">
                      <div blocks-name="tagline" class="text-style-tagline">Kernkompetenzen</div>
                      <div class="after-header-headline">
                        <h1 class="heading-style-h1 text-color-black">Know-How und organisatorische Stärke</h1>
                        <div class="intro-headline-slogan"><svg xmlns="http://www.w3.org/2000/svg" width="100%" viewbox="0 0 681 63" fill="none" class="svg-3 handwriting-svg">
                            <path d="M22.8 8C17.1 18 13.7 29.3 12.8 40.8C12.6 43.2 12.7 46 14.4 47.7C16 49.2 18.6 49.3 20.7 48.4C22.8 47.5 24.3 45.8 25.8 44.1C34.2 34.2 39.8 22 41.8 9.2" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M44.9999 26.8999C45.6999 27.4999 45.8999 28.3999 45.7999 29.2999C45.6999 30.1999 45.2999 30.9999 44.9999 31.7999L40.9999 40.7999C40.8999 40.8999 41.0999 41.0999 41.1999 40.9999C44.5999 38.2999 48.0999 35.7999 51.6999 33.3999C52.7999 32.6999 54.0999 31.8999 55.2999 32.2999C56.6999 32.6999 57.3999 34.3999 57.3999 35.8999C57.3999 37.3999 56.9999 38.8999 57.1999 40.2999C57.3999 41.7999 58.2999 43.3999 59.7999 43.3999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M90 11.8C85.1 22.3 80.1 33.0001 78.8 44.4001C79.2 40.3001 79.6 36.1 80.1 32C80.2 31.3 80.2 30.6 79.9 30C79.3 28.9 77.8 28.8 76.6 29.1C70.9 30.1 66.2 35.3 65.8 41.1C65.7 42.5 65.9 44.0001 67.1 44.7001C67.9 45.2001 68.9 45.1001 69.8 44.7001C70.6 44.3001 71.3 43.7 72 43.1C74.5 40.8 76.9 38.6 79.4 36.3" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M116 10.6001C111.9 20.7001 108.1 31.0001 104.6 41.4001C104.5 41.8001 105 42.1001 105.3 41.8001C108.7 37.9001 112.7 34.5001 117.2 31.9001C117.8 31.5001 118.5 31.2001 119.1 31.3001C120.3 31.6001 120.6 33.1001 120.7 34.3001C120.9 37.7001 121.1 41.1001 121.3 44.4001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M134 30.8999C133.8 33.2999 132.7 35.4999 131.8 37.6999C130.9 39.8999 130.1 42.2999 130.6 44.6999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M137.4 17.3C136.8 17.4 136.3 18.1001 136.5 18.7001C136.7 19.3001 137.4 19.7 138 19.4C138.6 19.1 138.8 18.3 138.5 17.8C138.4 17.7 138.4 17.7 138.3 17.6" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M143.2 32.4001C144.8 36.6001 147.7 36.8001 149.8 36.0001C151.9 35.2001 153.3 33.2001 153.8 31.0001C154.1 29.4001 153.9 27.5001 152.7 26.4001C151.6 25.4001 149.8 25.3001 148.4 25.9001C147 26.5001 145.9 27.6001 145 28.8001C142.5 32.0001 141.1 35.9001 140.9 39.9001C140.8 41.9001 141.1 44.2001 142.7 45.5001C144.1 46.7001 146.2 46.7001 147.9 46.1001C149.6 45.5001 151 44.3001 152.4 43.1001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M166 25.3C162.6 31.6 160.3 38.5 159.5 45.6C159.6 41.9 161.8 38.5001 164.3 35.9001C168 32.1001 172.6 29.2 177.6 27.5" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M207.7 25.3C204.6 25.2 201.5 26.2001 199 28.1001C196.5 30.0001 194.8 32.8 194.1 35.8C193.8 37.3 193.9 39.2001 195.1 40.0001C196.3 40.8001 197.8 40.2001 199.1 39.7001C200.9 39.0001 202.6 38.2001 204.4 37.5001C206 36.8001 207.8 36.1 208.7 34.6C209.6 33.3 209.7 31.7001 209.5 30.2001C209.3 28.7001 207.6 33.2001 206.4 36.7001C204.9 40.8001 203.9 45.0001 203.1 49.2001C202.7 51.0001 202.4 52.9 201.6 54.6C200.7 56.3001 199.2 57.7001 197.4 57.9001C195.3 58.2001 193.1 56.7001 192.6 54.7001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M226.7 41.7001C225.4 43.5001 223.9 45.5001 221.7 45.9001C219.7 46.2001 217.6 45.1001 216.6 43.3001C215.6 41.6001 215.4 39.4001 215.9 37.4001C216.3 35.4001 217.3 33.6001 218.3 31.9001C219.5 30.0001 220.8 28.0001 222.8 27.0001C224.8 26.0001 227.7 26.3001 228.8 28.3001C229.7 29.8001 229.2 31.8001 228.1 33.1001C226.9 34.4001 225.2 35.0001 223.5 35.2001C221.8 35.3001 220.1 35.1001 218.4 34.8001C216.7 34.5001 214.9 33.9001 213.7 32.6001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M246.7 11C243.5 18.9 240.2 26.9 237 34.8C236.2 36.8 235.4 38.7 234.9 40.8C234.7 41.5 235.6 42 236.1 41.5L245.2 32.3C246.1 31.4 246.9 30.5 248.1 30.1C249.2 29.6 250.7 29.7 251.5 30.6C252.3 31.4 252.3 32.7 252.3 33.8C252.3 36.1 252.2 38.4 252.2 40.7C252.2 41.7 252.2 42.7 252.5 43.7C252.8 44.7 253.5 45.6 254.4 45.9" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M264.1 34C266.1 34.5 268.3 35 270.3 34.6C272.4 34.3 274.4 33.1 275.2 31.1C276 29.1 275.1 26.5 273 25.9C271 25.3 269 26.8 267.6 28.2C266 29.9 264.5 31.8 263.3 33.8C262.4 35.4 261.5 37.1 261.2 38.9C260.9 40.7 261.2 42.7 262.2 44.2C263.2 45.7 265.1 46.8 266.9 46.5C269.1 46.2 270.6 44.3 271.9 42.5" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M285.1 26.6001C286.2 28.1001 285.6 30.3001 284.9 32.0001C283.5 35.6001 282 39.2001 280.6 42.7001C284 38.5001 288.2 35.1001 293 32.6001C294.2 32.0001 295.8 31.4001 296.9 32.3001C297.6 32.8001 297.8 33.8001 297.8 34.6001C297.8 35.4001 297.5 36.3001 297.4 37.1001C297.1 39.5001 298.1 42.1001 299.9 43.7001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M327.1 25.1001C325.7 28.2001 325.1 30.5001 324 33.1001C322.9 35.7001 321.8 38.7001 322.4 41.5001C322.7 42.8001 323.6 44.2001 325 44.1001C326.1 44.0001 326.8 43.0001 327.3 42.1001C329.8 38.1001 332.3 34.1001 334.8 30.1001C334.1 33.6001 333.4 37.2001 332.7 40.7001C332.5 41.7001 332.3 42.8001 332.5 43.8001C332.7 44.8001 333.6 45.8001 334.6 45.8001C335.7 45.8001 336.6 44.9001 337.3 44.0001C341.1 39.2001 344.4 33.9001 347.2 28.4001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M354.7 29.8999C353.7 31.9999 353.5 33.8999 352.8 35.8999C351.8 38.4999 351.5 41.3999 350.3 44.0999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M358.6 16.8C357.6 17 357 18.3001 357.5 19.2001C358 19.3001 358.6 19.1001 358.9 18.7001C359.2 18.3001 359.2 17.6001 358.9 17.2001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M367.7 26.3999C364.6 32.3999 362.5 38.8999 361.6 45.5999C362.5 41.5999 364.9 37.9999 367.9 35.1999C370.9 32.3999 374.6 30.3999 378.5 28.8999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M418.3 11.3C414.5 22.8 411.6 34.6 409.6 46.6C410.1 41.2 410.7 35.8 411.2 30.4C411.3 33.4 410.4 36.5001 408.5 38.9001C406.7 41.3001 403.9 43 401 43.6C400.2 43.8 399.2 43.9 398.5 43.5C397 42.8 396.6 40.9 396.7 39.3C397 34.8 399.9 30.5 404 28.5C405.6 27.8 407.2 28 409 28.1C409.6 28.1 410.3 28.2 410.8 28.6C411.3 29 411.6 29.8 411.2 30.3" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M423.7 29.6001C423.3 34.2001 421.5 38.7001 418.6 42.4001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M427.5 17.1C426.6 17.4 426.1 18.4 426.4 19.2C426.7 20.1 427.7 20.6 428.5 20.3C428.8 19.2 428.6 18 428.1 17" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M433.9 33.7999C435.6 34.2999 437.3 34.4999 439 34.1999C440.7 33.8999 442.3 32.7999 443.1 31.1999C443.9 29.5999 443.6 27.5999 442.4 26.3999C441.3 25.3999 439.7 25.1999 438.2 25.5999C436.7 25.9999 435.6 26.9999 434.6 27.9999C431.6 31.0999 429.8 35.3999 429.8 39.6999C429.8 41.1999 430.1 42.8999 431.1 43.9999C432.3 45.1999 434.3 45.3999 435.9 44.9999C437.6 44.4999 439 43.4999 440.4 42.3999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M491.5 8.8999C488.1 9.2999 484.7 9.6999 481.3 10.1999C479.8 10.3999 478.3 10.5999 476.9 11.2999C475.3 12.0999 474.2 13.5999 473.1 14.9999C469.1 20.5999 465.9 26.6999 463.5 33.0999C462.7 35.2999 462 37.5999 462.1 39.8999C462.2 42.1999 463.1 44.6999 465 45.9999C466.5 47.0999 468.4 47.3999 470.3 47.4999C473.4 47.7999 476.6 47.6999 479.6 46.6999C482.6 45.6999 485.3 43.6999 486.7 40.9999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M464.6 28.6C471.4 29.7 478.5 28.7 484.8 25.8" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M497.7 26.8C499.6 32.8 501.5 38.8 503.5 44.8C504 46.3 504.5 47.8001 505.6 48.9001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M510 26.6001C506.9 30.1001 503.6 33.4001 500.3 36.7001C497.5 39.5001 494.7 42.3001 491.8 45.2001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M530.1 11.8999C527.9 16.7999 525.7 21.7999 523.4 26.6999C520.3 33.6999 517 40.9999 517.3 48.6999C517.8 48.1999 518.3 47.6999 518.8 47.1999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M515.5 24.7C522 23.8 528.5 22.9 535 22" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M539.4 25.1001C538.6 32.1001 533.6 38.2001 533.2 45.3001C535.1 37.1001 542.2 30.3001 550.5 28.9001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M573.3 43.1C572.5 43.9 571.1 43.8 570.2 43C569.3 42.2 569 41.1 568.8 39.9C568.5 38 568.8 36.2 569.2 34.4C569.6 32.9 570.1 31.4 569.7 29.8C569.2 28.1 567.5 26.6 565.8 26.4C564.1 26.3 562.3 28 561.1 29.1C558.1 31.6 555.8 34.9 554.3 38.5C553.9 39.4 553.6 40.4 553.7 41.4C553.8 42.4 554.3 43.4 555.3 43.8C556.5 44.3 557.8 43.6 558.8 42.9C560.6 41.7 562.5 40.5 564.3 39.2C565.9 38.1 567.4 36.9 568.6 35.3C568.9 34.9 569.1 34.5 569.3 34.1" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M580.7 26.9C581 25.8 582.4 25.3 583.5 25.8C584.5 26.3 585.1 27.4 585.2 28.5C585.3 29.6 585 30.7 584.7 31.8C583.4 36.1 581.5 40.1 579 43.8C582.4 39.5 586.2 35.4 590.1 31.5C590.8 30.8 591.8 30 592.8 30.3C593.6 30.5 594.1 31.3 594.2 32.1C594.3 32.9 594.1 33.7 593.9 34.5C593.1 37.4 592.4 40.3 591.6 43.2C592.8 38.1 596.7 33.7 601.7 31.8C601.9 34.4 602.1 37 602.3 39.6C602.4 40.6 602.5 41.7 603.2 42.3C604 43.1 605.4 43 606.4 42.5C607.4 41.9 608.1 41 608.8 40.1" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M613.3 33.5C616.9 33.9 623.9 35.2 626.8 33.1C627.6 32.5 628.4 31.7 628.7 30.7C629 29.7 629 28.6 628.4 27.7C627.4 26.3 625.2 26.1 623.5 26.7C620.4 27.7 618 30.5 616.8 33.5C615.6 36.5 615.4 39.9 615.5 43.2C615.5 44 615.6 44.9 616.1 45.5C616.8 46.5 618.2 46.8 619.4 46.7C622.6 46.5 625.6 44.6 627.2 41.9" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M637.7 30.6001C637 32.3001 636.3 34.0001 635.6 35.7001C634.5 38.5001 633.4 41.2001 632.7 44.1001" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M641.6 16.3999C640.8 16.3999 640 16.7999 639.5 17.4999C639 18.1999 638.9 18.9999 639.2 19.7999C640 19.7999 640.9 19.3999 641.4 18.7999C642 18.1999 642.2 17.2999 642.1 16.4999C641.8 16.4999 641.5 16.4999 641.2 16.4999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M655.2 12.7C647.8 22.9 643.2 35 641.8 47.5" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                            <path d="M656.7 33.5999C657.2 34.8999 658.6 35.7999 660 35.9999C661.4 36.1999 662.8 35.8999 664.1 35.2999C665.6 34.5999 666.9 33.4999 667.7 32.0999C668.5 30.6999 668.6 28.8999 667.9 27.3999C667 25.5999 664.8 24.5999 662.7 24.7999C660.6 24.9999 658.8 26.2999 657.5 27.8999C656.2 29.4999 655.4 31.4999 654.8 33.3999C654.1 35.6999 653.7 37.9999 653.4 40.2999C653.3 41.4999 653.2 42.7999 653.9 43.7999C654.9 45.4999 657.2 45.8999 659.1 45.1999C660.9 44.5999 662.4 43.0999 663.7 41.6999" stroke="currentColor" stroke-width="4" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="w-layout-grid layout242_list">
                  <div class="layout242_item">
                    <div class="margin-bottom margin-small">
                      <h4 class="heading-style-h4">Fachwissen</h4>
                    </div>
                    <p class="text-size-regular">Fundierte Kenntnisse in Bauplanung und Bauausführung bilden unsere Basis – wir übertragen sie gezielt in Projektmanagement und Bauleitung. So führen wir <a href="projekte.html" class="landing-link">Bauvorhaben</a> sicher zum Ziel: mit Klarheit, digitaler Präzision und einem tiefen Verständnis für Menschen und Prozesse.</p>
                  </div>
                  <div class="layout242_item">
                    <div class="margin-bottom margin-small">
                      <h3 class="heading-style-h4">Präsenz</h3>
                    </div>
                    <p class="text-size-regular">Mit <a href="kontakt.html" class="landing-link">Standorten</a> in Zürich, Zug und Baden sind wir fest in den Regionen verankert und stark vernetzt. Die Nähe zu Partnern und Baustellen garantiert kurze Wege, schnelle Entscheidungen und eine intensive Begleitung. Wir begleiten Bauprojekte dort, wo sie entstehen.<br></p>
                  </div>
                  <div class="layout242_item">
                    <div class="margin-bottom margin-small">
                      <h3 class="heading-style-h4">Agilität</h3>
                    </div>
                    <p class="text-size-regular word-break">Agilität ist Grundsatz und Teil unseres Management-Verständnisses. Sie ermöglicht uns proaktives, projektbezogenes Handeln. Wir ergänzen durch Design-to-Cost-Strategien, eine digitale Kommunikationskultur und eine konsequente Termin- und Kostensteuerung.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_layout459">
        <div class="padding-global height-custom">
          <div class="container-large height-custom">
            <div class="padding-section-large height-custom">
              <div class="layout459_component">
                <div class="margin-bottom margin-xxlarge">
                  <div class="max-width-large">
                    <div class="headline-spacing">
                      <div class="headline-tag-group">
                        <div blocks-name="tagline" class="text-style-tagline wirsind-tagline hide">Kernkompetenzen</div>
                        <div class="after-header-headline">
                          <h1 class="heading-style-h1">Teamwork - Mit Einsatz zum Projekterfolg</h1>
                        </div>
                      </div>
                      <a href="leistungen.html" role="button" class="w-inline-block">
                        <div class="cta-button">
                          <div class="heading-style-h4 cta-text">Kompetenzen</div>
                          <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                            </svg></div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
                <div class="intro-boxes">
                  <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217cb-e717c714" class="layout459_group-item grid-item-double">
                    <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217cc-e717c714" class="max-width-xsmall vw-width">
                      <div data-w-id="1818cbf1-6206-ff10-4bf4-dd1b5e2217d3" class="intro-akkordion-box akkordion-anim">
                        <div blocks-name="layout497_tab-title" blocks-slot-children="ST265" class="intro-akkordion-title">
                          <div class="akkordion-title">
                            <h4 blocks-non-deletable="true" blocks-name="heading-style-h4-2" blocks-slot-item-canonical="EL127" class="heading-style-h4 text-color-white">Für Investoren &amp; Bauherren</h4>
                            <div class="icon-embed-small intro-expand-icon w-embed">
                              <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#ffffff">
                                <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg>
                            </div>
                          </div>
                          <div blocks-name="layout497_paragraph" style="width:100%;height:0px" class="intro-akkordion-p">
                            <div>
                              <p blocks-slot-item-canonical="EL10" class="text-size-regular text-color-white padding-expand">Fokus auf Bauökonomie, Wirtschaftlichkeit und Ergebnistransparenz vom Projektstart bis zur Übergabe. Design-to-Cost-Strategien und digitale Unterstützung gewährleisten eine effiziente Bauplanung und nachhaltige Wertschöpfung.<br></p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217de-e717c714" class="max-width-xsmall vw-width vw-width-left">
                      <div data-w-id="1818cbf1-6206-ff10-4bf4-dd1b5e2217e5" class="intro-akkordion-box akkordion-anim">
                        <div blocks-name="layout497_tab-title" blocks-slot-children="ST265" class="intro-akkordion-title">
                          <div class="akkordion-title">
                            <h4 blocks-non-deletable="true" blocks-name="heading-style-h4-2" blocks-slot-item-canonical="EL127" class="heading-style-h4 text-color-white">Für Planungspartner</h4>
                            <div class="icon-embed-small intro-expand-icon w-embed">
                              <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#ffffff">
                                <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg>
                            </div>
                          </div>
                          <div blocks-name="layout497_paragraph" style="width:100%;height:0px" class="intro-akkordion-p">
                            <div>
                              <p blocks-slot-item-canonical="EL10" class="text-size-regular text-color-white padding-expand">Starkes Netzwerk, klare Kommunikation und reibungslose digitale Zusammenarbeit als Grundlage erfolgreicher Partnerschaften. Frühzeitige Einbindung und klare Prozesse sichern eine partnerschaftliche Zusammenarbeit für langfristigen Erfolg.</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217ef-e717c714" class="layout459_group-item grid-item-center wolf-icon-mobile">
                    <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217f0-e717c714" class="max-width-xsmall vw-width wolf-icon-mitte">
                      <div class="icon-embed-xlarge wolf-svg w-embed"><svg width="191" height="194" viewbox="0 0 191 194" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M183.945 9.64457C183.926 9.25248 183.877 8.86021 183.807 8.46873C183.796 8.41001 183.782 8.3527 183.77 8.29435C183.713 8.00986 183.642 7.72715 183.556 7.44559C183.532 7.36472 183.507 7.28434 183.481 7.20432C183.393 6.94382 183.292 6.68607 183.18 6.43009C183.148 6.35709 183.12 6.28288 183.086 6.21085C183.064 6.16373 183.048 6.11533 183.025 6.0684C182.727 5.46134 182.366 4.90635 181.96 4.39841C181.94 4.37302 181.917 4.34946 181.897 4.32426C181.692 4.07298 181.474 3.83591 181.246 3.61082C181.204 3.5687 181.16 3.52781 181.117 3.48655C180.884 3.26585 180.642 3.05839 180.39 2.86509C180.361 2.84287 180.333 2.81937 180.304 2.79746C179.714 2.35624 179.076 1.99046 178.404 1.70524C178.361 1.68674 178.317 1.67112 178.273 1.65342C177.984 1.53599 177.69 1.43412 177.391 1.34678C177.336 1.33079 177.282 1.314 177.226 1.29911C175.826 0.918678 174.336 0.871864 172.877 1.19956C172.872 1.2006 172.868 1.20145 172.863 1.20255C172.178 1.35758 171.5 1.5853 170.843 1.91074L95.4955 38.9214L20.1513 1.91074C19.4994 1.58891 18.8269 1.36234 18.1481 1.20725C18.1288 1.20286 18.1095 1.19938 18.0902 1.19517C17.7393 1.11741 17.3874 1.0615 17.0345 1.02634C17.0257 1.02543 17.0169 1.02433 17.008 1.02348C15.8966 0.916237 14.788 1.01621 13.7309 1.30711C13.7045 1.31437 13.6783 1.32243 13.6519 1.32993C13.3104 1.42722 12.9752 1.54295 12.6473 1.6793C12.6402 1.68223 12.6331 1.68473 12.6261 1.68766C11.5992 2.11759 10.6521 2.73661 9.83469 3.52372C9.81607 3.5416 9.7974 3.55912 9.7789 3.57719C9.52292 3.82798 9.27835 4.09226 9.051 4.37589C9.05002 4.37711 9.04886 4.37827 9.04788 4.37949C8.63694 4.89262 8.27115 5.45353 7.97019 6.0684C7.94718 6.11533 7.93112 6.16373 7.90897 6.21085C7.87516 6.28288 7.84671 6.35709 7.81473 6.43009C7.70243 6.68601 7.60135 6.94364 7.51425 7.20408C7.48746 7.2844 7.46268 7.36503 7.43808 7.44621C7.35263 7.72739 7.28153 8.00974 7.22446 8.29374C7.21268 8.35239 7.1987 8.40995 7.18808 8.46891C7.11746 8.86027 7.06858 9.25236 7.04996 9.64433L0.213412 124.731C0.0229827 127.923 1.52738 130.989 4.17435 132.792L90.3748 191.539C91.9173 192.593 93.7074 193.12 95.4973 193.12C97.2874 193.12 99.0774 192.593 100.62 191.539L186.814 132.792C189.461 130.989 190.965 127.923 190.775 124.731L183.945 9.64457ZM95.4973 173.004L18.685 120.655L24.3978 24.4765L73.8308 49.563L45.7385 63.3619C41.2253 65.5772 39.3655 71.0298 41.5808 75.5429C43.1614 78.7612 46.3986 80.6338 49.7565 80.6338C51.1022 80.6338 52.4733 80.3354 53.7619 79.7007L95.0284 59.4306C95.3401 59.4468 95.6509 59.4469 95.9626 59.4309L137.227 79.7007C138.515 80.3354 139.886 80.6338 141.232 80.6338C144.59 80.6338 147.827 78.7612 149.408 75.5429C151.623 71.0298 149.763 65.5772 145.25 63.3619L117.161 49.5639L166.591 24.4765L172.303 120.655L95.4973 173.004Z" fill="white"></path>
                        </svg></div>
                    </div>
                  </div>
                  <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217f2-e717c714" class="layout459_group-item grid-item-double">
                    <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e2217f3-e717c714" class="max-width-xsmall vw-width vw-width-right">
                      <div data-w-id="1818cbf1-6206-ff10-4bf4-dd1b5e2217f4" class="intro-akkordion-box akkordion-anim">
                        <div blocks-name="layout497_tab-title" blocks-slot-children="ST265" class="intro-akkordion-title">
                          <div class="akkordion-title">
                            <h4 blocks-non-deletable="true" blocks-name="heading-style-h4-2" blocks-slot-item-canonical="EL127" class="heading-style-h4 text-color-white">Für Baudienstleister</h4>
                            <div class="icon-embed-small intro-expand-icon w-embed">
                              <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#ffffff">
                                <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg>
                            </div>
                          </div>
                          <div blocks-name="layout497_paragraph" style="width:100%;height:0px" class="intro-akkordion-p">
                            <p blocks-slot-item-canonical="EL10" class="text-size-regular text-color-white padding-expand">Verlassen Sie sich auf präzise Ausschreibungen und durchdachte Bauzeitenpläne. Unsere digitale Qualitätssicherung und vorausschauende Planung minimieren Leerlaufzeiten und Nacharbeiten.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="w-node-_1818cbf1-6206-ff10-4bf4-dd1b5e221803-e717c714" class="max-width-xsmall vw-width">
                      <div data-w-id="1818cbf1-6206-ff10-4bf4-dd1b5e221804" class="intro-akkordion-box akkordion-anim">
                        <div blocks-name="layout497_tab-title" blocks-slot-children="ST265" class="intro-akkordion-title">
                          <div class="akkordion-title">
                            <h4 blocks-non-deletable="true" blocks-name="heading-style-h4-2" blocks-slot-item-canonical="EL127" class="heading-style-h4 text-color-white">Für Käufer &amp; Nutzer</h4>
                            <div class="icon-embed-small intro-expand-icon w-embed">
                              <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#ffffff">
                                <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#ffffff" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                              </svg>
                            </div>
                          </div>
                          <div blocks-name="layout497_paragraph" style="width:100%;height:0px" class="intro-akkordion-p">
                            <p blocks-slot-item-canonical="EL10" class="text-size-regular text-color-white padding-expand">Wir bieten massgeschneiderte Lösungen und persönliche Betreuung. Als Ihre Drehscheibe im Rahmen der Verkaufsabwicklung garantieren wir höchste Qualität, termingerechte Fertigstellung und eine einwandfreie Customer Experience.</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="image-section"><img data-w-id="1818cbf1-6206-ff10-4bf4-dd1b5e22180e" loading="lazy" alt="Bild einer Wölfli Baustelle" src="images/woelfli-intro-bg_1woelfli-intro-bg.avif" class="moving-image"></div>
      </section>
      <header class="section_header46">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="header46_component">
                <div class="max-width-large">
                  <div class="headline-spacing">
                    <div class="headline-tag-group">
                      <div blocks-name="tagline" class="text-style-tagline hide">Regional organisiert und weiträumig engagiert</div>
                      <div class="after-header-headline mapbox-headline">
                        <h1 class="heading-style-h1 black-h1">Projektlandkarte</h1>
                        <h4 class="heading-style-h4">Projekte in Planung und Realisierung</h4>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <section class="mapbox-wrapper">
        <div class="section mapbox-section">
          <div class="container is-map">
            <div class="locations-map_wrapper">
              <div id="office-list" class="locations-map_list"></div>
            </div>
            <div class="locations-map_wrapper">
              <div id="location-list" class="locations-map_list"></div>
            </div>
            <div id="map" class="mapbox-wrap"></div>
            <div class="w-embed">
              <style>.mapboxgl-canvas {
  touch-action: manipulation !important; /* Allows pinch-zoom but enables taps */
}
.card_heading {
line-height:1!important;
}
.close-block { 
color:#fff!important;
}
.close-block .icon-embed-xxsmall { 
color:#fff!important;
}
</style>
            </div>
          </div>
        </div>
        <div class="map-highlight-buttons">
          <a id="show-offices" href="#" class="button-2 w-button">Unsere Büros</a>
          <a id="show-projects" href="#" class="button-2 w-button">Laufende Projekte</a>
        </div>
        <div class="w-embed">
          <div class="section mapbox-section">
            <div class="container is-map">
              <!--  Map container  -->
              <div id="map"></div>
              <!--  Filter buttons  -->
              <div class="map-filter-buttons">
                <button id="show-offices" class="map-filter-button">Büros</button>
                <button id="show-projects" class="map-filter-button">Projekte</button>
              </div>
              <!--  Office locations sidebar  -->
              <div class="locations-map_wrapper">
                <div id="office-list" class="locations-map_list">
                  <!--  Office locations will be inserted here by JavaScript  -->
                </div>
              </div>
              <!--  Project locations sidebar  -->
              <div class="locations-map_wrapper is--show">
                <div id="location-list" class="locations-map_list">
                  <!--  Project locations will be inserted here by JavaScript  -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <header class="section_header46">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="header46_component">
                <div class="max-width-large">
                  <div class="headline-spacing">
                    <div class="headline-tag-group">
                      <div blocks-name="tagline" class="text-style-tagline hide">Regional organisiert und weiträumig engagiert</div>
                      <div class="after-header-headline mapbox-headline">
                        <h1 class="heading-style-h1 black-h1">Blick in die Vergangenheit</h1>
                      </div>
                    </div>
                    <a role="button" href="projekte.html" class="w-inline-block">
                      <div class="cta-button">
                        <div class="heading-style-h4 cta-text">Realisierte Bauten</div>
                        <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>
      <section class="section_faq6">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="header46_component">
                <div class="max-width-large">
                  <div class="margin-bottom margin-xsmall hide">
                    <div blocks-name="tagline" class="text-style-tagline">Vernetzt und vorbereitet</div>
                  </div>
                  <div class="grid-header">
                    <h1 class="heading-style-h1 black-h1">Nachhaltigkeit als Prinzip. Fortschritt mit Substanz.</h1>
                    <p class="text-size-regular grid-p-headline">Nachhaltigkeit bedeutet für uns das ausgewogene Zusammenspiel von Umwelt, Wirtschaft und Gesellschaft. Wir integrieren nachhaltige Lösungen konsequent in unsere Prozesse und Planungen und setzen sie aktiv um.<br></p>
                  </div>
                </div>
              </div>
              <div class="w-layout-grid layout364_grid-list">
                <div blocks-name="layout364_row" blocks-slot-children="ST369" class="w-layout-grid layout364_row">
                  <div blocks-name="layout364_card" data-w-id="b8049e7c-f054-ab2c-364e-be2a9630fd87" class="layout364_card">
                    <div blocks-name="layout364_card-content" blocks-slot-children="ST265" class="layout364_card-content">
                      <div blocks-slot-children="ST265" blocks-name="layout364_card-content-top" class="layout364_card-content-top">
                        <div class="fortschrott-expand-title">
                          <h4 blocks-non-deletable="true" blocks-name="heading-2" blocks-slot-item-canonical="EL125" class="heading-style-h4">Präzision durch Datenbanken</h4>
                          <div class="icon-embed-small fortschritt-expand-icon w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                              <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          </div>
                        </div>
                        <div style="width:100%;height:0px" class="fortschritt-p">
                          <p blocks-slot-item-canonical="EL10" blocks-name="paragraph-2" class="text-size-regular padding-expand">Unsere Kostenplanung  ist datenbankbasiert und garantiert stets marktaktuelle Preise und maximale Kostentransparenz. So treffen wir fundierte Entscheidungen auf Basis verlässlicher Zahlen.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div blocks-name="layout364_card" data-w-id="d7f383ac-f7ff-ab4e-b15e-21fdba145498" class="layout364_card">
                    <div blocks-name="layout364_card-content" blocks-slot-children="ST265" class="layout364_card-content">
                      <div blocks-slot-children="ST265" blocks-name="layout364_card-content-top" class="layout364_card-content-top">
                        <div class="fortschrott-expand-title">
                          <h4 blocks-non-deletable="true" blocks-name="heading-2" blocks-slot-item-canonical="EL125" class="heading-style-h4">KI-gestützte Prozesse</h4>
                          <div class="icon-embed-small fortschritt-expand-icon w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                              <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          </div>
                        </div>
                        <div style="width:100%;height:0px" class="fortschritt-p">
                          <p blocks-slot-item-canonical="EL10" blocks-name="paragraph-2" class="text-size-regular padding-expand">Mit modernster KI-Technologie optimieren wir interne Abläufe und minimieren Fehlerquellen. Das bedeutet für unsere Projekte: schnellere Reaktionszeiten und höhere Präzision.<br></p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div blocks-name="layout364_row" blocks-slot-children="ST369" class="w-layout-grid layout364_row">
                  <div blocks-name="layout364_card" data-w-id="8074f3cf-f9c1-3ab1-1edd-a31ff0aeb6f4" class="layout364_card">
                    <div blocks-name="layout364_card-content" blocks-slot-children="ST265" class="layout364_card-content">
                      <div blocks-slot-children="ST265" blocks-name="layout364_card-content-top" class="layout364_card-content-top">
                        <div class="fortschrott-expand-title">
                          <h4 blocks-non-deletable="true" blocks-name="heading-2" blocks-slot-item-canonical="EL125" class="heading-style-h4">Digitale Qualitätskontrolle</h4>
                          <div class="icon-embed-small fortschritt-expand-icon w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                              <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          </div>
                        </div>
                        <div style="width:100%;height:0px" class="fortschritt-p">
                          <p blocks-slot-item-canonical="EL10" blocks-name="paragraph-2" class="text-size-regular padding-expand">Mit auf uns zugeschnittenen Software-Lösungen nutzen wir den digitalen Fortschritt für besseres Projektieren und höhere Qualität des Outputs</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div blocks-name="layout364_card" id="w-node-fabb9201-1166-ecd7-5f4a-d3d41c8c6ab1-e717c714" data-w-id="fabb9201-1166-ecd7-5f4a-d3d41c8c6ab1" class="layout364_card">
                    <div blocks-name="layout364_card-content" blocks-slot-children="ST265" class="layout364_card-content">
                      <div blocks-slot-children="ST265" blocks-name="layout364_card-content-top" class="layout364_card-content-top">
                        <div class="fortschrott-expand-title">
                          <h4 blocks-non-deletable="true" blocks-name="heading-2" blocks-slot-item-canonical="EL125" class="heading-style-h4">Kollaborationslösung</h4>
                          <div class="icon-embed-small fortschritt-expand-icon w-embed">
                            <?xml version="1.0" encoding="UTF-8"?><svg width="24px" height="24px" stroke-width="1.5" viewbox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" color="#000000">
                              <path d="M6 12H12M18 12H12M12 12V6M12 12V18" stroke="#000000" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                            </svg>
                          </div>
                        </div>
                        <div style="width:100%;height:0px" class="fortschritt-p">
                          <p blocks-slot-item-canonical="EL10" blocks-name="paragraph-2" class="text-size-regular padding-expand">Wir nutzen eine in die Projektkommunikation implementierte interaktiv nutzbare Plattform zur transparenten Projektsteuerung, für relevante Informationen.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_testimonial1 hide">
        <div class="padding-global testimonial-padding">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="max-width-xlarge">
                <div class="testimonial1_component">
                  <div class="testimonial1_content">
                    <div class="margin-bottom margin-medium">
                      <div class="testimonial1_logo-wrapper"><img loading="lazy" src="images/aura_logo.svg" alt="" class="testimonial1_logo"></div>
                    </div>
                    <h3 class="heading-style-h2">“ Wölfli Bauplanung Gmbh hat es uns ermöglicht bereits nach 6 Wochen von der Planungsphase in die Umsetzung zu gehen. Unsere eigenen Vorstellungen und eine nachhaltige Bauweise wurden dabei zu keinem Zeitpunkt ausser Acht gelassen “</h3>
                    <div class="margin-top margin-medium">
                      <div class="testimonial1_client">
                        <div>
                          <div class="testimonial1_client-image-wrapper"><img loading="lazy" src="images/6191a88a1c0e39463c2bf022_placeholder-image.svg" alt="Platzhalter Bild für das füllen einer Fläche" class="testimonial1_client-image"></div>
                        </div>
                        <div>
                          <div class="text-size-medium">Vorname Nachname</div>
                          <div class="text-size-small">Position, Company name</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section_layout361">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="layout361_component">
                <div class="margin-bottom">
                  <div class="text-align-left">
                    <div class="max-width-large">
                      <div class="margin-bottom margin-xsmall hide">
                        <div blocks-name="tagline" class="text-style-tagline">Bausteine für Ihren Erfolg</div>
                      </div>
                      <div class="grid-header">
                        <h1 class="heading-style-h1 black-h1">Marken für die Bau- und Immobilienwelt: Bausteine für den Erfolg</h1>
                        <p class="text-size-regular grid-p-headline">Eine umfassende Palette an Dienstleistungen für eine durchgängige zentral gesteuerte Projektentwicklung– von der Studie bis zur Ausführung.</p>
                        <a role="button" href="marken.html" class="w-inline-block">
                          <div class="cta-button cta-secondary">
                            <div class="heading-style-h4 cta-text">Unsere Marken<br></div>
                            <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                              </svg></div>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="div-block-6">
                  <div class="w-layout-grid card-row4_component">
                    <div class="card-row4_card propertylab-card">
                      <div class="card-row4_card-content">
                        <div class="card-top">
                          <div class="marken-card-logo"><img src="images/propertylab_logo.png" loading="lazy" sizes="100vw" srcset="images/propertylab_logo-p-500.png 500w, images/propertylab_logo-p-800.png 800w, images/propertylab_logo-p-1080.png 1080w, images/propertylab_logo.png 1891w" alt="Propertylab Logo" class="logo-image"></div>
                          <p class="text-size-regular hyphen">Propertylab steht für einen durchgängig gesteuerten Prozess für die ganzheitliche Projektentwicklung- von der Potenzialanalyse bis zur Markteinführung. Als strategischer Partner begleiten wir Investoren durch alle Entwicklungsphasen.</p>
                        </div>
                      </div>
                    </div>
                    <div class="card-row4_card coreal-card">
                      <div class="card-row4_card-content">
                        <div class="card-top">
                          <div class="marken-card-logo"><img src="images/coreal_logo.png" loading="lazy" sizes="100vw" srcset="images/coreal_logo-p-500.png 500w, images/coreal_logo.png 1088w" alt="Coreal Logo" class="logo-image"></div>
                          <p class="text-size-regular hyphen">Coreal bietet eine Alternative zur klassischen Immobilienvermarktung in der Schweiz. Im Sinne des „Ab-Hof-Verkaufs“ bieten wir integrierte Vermarktungsprozesse bis hin zur Käuferbetreuung im Aftersales.</p>
                        </div>
                      </div>
                    </div>
                    <div class="card-row4_card conexpool-card">
                      <div class="card-row4_card-content">
                        <div class="card-top">
                          <div class="marken-card-logo"><img src="images/conexpool_logo.png" loading="lazy" sizes="100vw" srcset="images/conexpool_logo-p-500.png 500w, images/conexpool_logo-p-800.png 800w, images/conexpool_logo.png 1508w" alt="Conexpool Logo" class="logo-image"></div>
                          <p class="text-size-regular hyphen">Conexpool vereint ausgewählte Experten aus dem Bauwesen in einem qualitätsorientierten Partnernetzwerk, das Planung, Ausführung und Investment  intelligent verbindet. </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="card-row4_card woelfli-card">
                    <div class="card-row4_card-content">
                      <div class="card-top woelfli-logo-top">
                        <div class="marken-card-logo"><img src="images/woelfli-plus-wolf-schwarz.svg" loading="lazy" alt="Wölfli Logo " class="logo-image"></div>
                        <div class="max-width-medium">
                          <p class="text-size-regular">Unter unserem Dach - alle Marken vereint. Diese Kombination ermöglicht einen holistischen Ansatz, Immobilienprojekte von der Idee bis zur Nutzung zu begleiten und zu verantworten.</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <header class="section_header67 text-color-alternate">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large">
              <div class="before-footer-content">
                <div data-w-id="f849abdc-8677-3e74-93c1-c5142c12fd8d" class="max-width-large">
                  <div data-w-id="f849abdc-8677-3e74-93c1-c5142c12fd8e" class="text-style-tagline text-color-white">Spezialisten im Einsatz</div>
                  <div class="card-grid-headline">
                    <h2 class="heading-style-h1">Leistungen</h2>
                    <a role="button" href="leistungen.html" class="link-block-4 w-inline-block">
                      <div class="cta-button">
                        <div class="heading-style-h4 cta-text">Breites Leistungsspektrum</div>
                        <div class="icon-embed-xsmall w-embed"><svg width="16" height="17" viewbox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M1 8.5H15M15 8.5L8.38889 1.5M15 8.5L8.38889 15.5" stroke="black" stroke-width="1.5" stroke-linecap="square" stroke-linejoin="round"></path>
                          </svg></div>
                      </div>
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="header67_background-image-wrapper">
          <div class="image-overlay-layer footer-overlay-bg"></div><img loading="lazy" src="images/20211116_103820315_iOS.avif" alt="" class="header67_background-image">
        </div>
      </header>
      <section class="footer">
        <div class="padding-global">
          <div class="container-large">
            <div class="padding-section-large footer-padding">
              <div class="layout367_component">
                <div class="w-layout-grid layout367_grid-list">
                  <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b19-398b1b13" class="w-layout-grid layout367_row">
                    <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b1a-398b1b13" class="layout367_card-large">
                      <div class="layout367_card-small-content footer-top-margin">
                        <div class="footer-column-content-top">
                          <div class="layout367_card-small-content-top">
                            <div class="div-block-10">
                              <h3 class="heading-style-h2 text-weight-medium">Engagement für  Bauprojekte.</h3>
                            </div>
                          </div>
                          <div class="layout367_card-small-content-top small-content-contact">
                            <a href="mailto:<EMAIL>" class="footer-email w-inline-block"><img src="images/footer-mail-icon.svg" loading="lazy" alt="" class="footer-contact-icon hide">
                              <p class="footer-contact-text"><EMAIL></p>
                            </a>
                            <a href="tel:+41445352222" class="footer-phone w-inline-block"><img src="images/footer-phone-icon.svg" loading="lazy" alt="" class="footer-contact-icon hide">
                              <p class="footer-contact-text">+41 44 535 22 22</p>
                            </a>
                          </div>
                        </div>
                        <div class="footer-logos-desktop">
                          <p class="footer-contact-text">Mitgliedschaften</p>
                          <div class="layout367_card-small-content-top footer-logo-grid">
                            <a aria-label="Maneco Logo" href="https://maneco.pro/" target="_blank" class="footer-logo w-inline-block"><img src="images/maneco-logo.svg" loading="lazy" alt="Maneco Logo" class="footer-logo-img"></a>
                            <a aria-label="Future Areas Logo" href="https://www.future-areas.swiss/" target="_blank" class="footer-logo w-inline-block"><img src="images/future-areas-logo.svg" loading="lazy" alt="Future Areas Logo" class="footer-logo-img"></a>
                            <a aria-label="OBS OBD Logo" href="https://www.obs-osd.ch/" target="_blank" class="footer-logo w-inline-block"><img src="images/logo_obs.svg" loading="lazy" alt="OBS OSD Logo" class="footer-logo-img"></a>
                            <a aria-label="Swiss Leaders Logo" href="https://swissleaders.ch/" target="_blank" class="footer-logo w-inline-block"><img src="images/swiss-leaders-logo.svg" loading="lazy" alt="Swiss Leaders Logo" class="footer-logo-img"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b2d-398b1b13" class="layout367_card-large">
                      <div class="layout367_card-large-content">
                        <div class="layout367_card-large-content-top">
                          <div class="margin-bottom margin-xxsmall hide">
                            <div class="tag is-text uppercase text-color-black">Menu</div>
                          </div>
                          <div class="footer13_link-list">
                            <a href="team.html" class="footer_link">Team</a>
                            <a href="leistungen.html" class="footer_link">Leistungen</a>
                            <a href="marken.html" class="footer_link">Marken</a>
                            <a href="projekte.html" class="footer_link">Projekte</a>
                            <a href="karriere.html" class="footer_link">Karriere</a>
                            <a href="kontakt.html" class="footer_link">Kontakt</a>
                          </div>
                        </div>
                        <div class="layout367_card-large-content-top">
                          <div class="margin-bottom margin-xxsmall hide">
                            <div class="tag is-text uppercase text-color-black">Socials</div>
                          </div>
                          <div class="footer13_link-list social-link-list">
                            <a href="https://www.instagram.com/woelfli_bauplanung" target="_blank" class="footer-link-icon w-inline-block"><img src="images/instagram.svg" loading="lazy" alt="" class="footer-icon">
                              <div class="footer_link footer-social-link">Instagram</div>
                            </a>
                            <a href="https://www.linkedin.com/company/woelfli-bauplanung-gmbh/" target="_blank" class="footer-link-icon w-inline-block"><img src="images/linkedin.svg" loading="lazy" alt="" class="footer-icon">
                              <div class="footer_link footer-social-link">LinkedIn</div>
                            </a>
                          </div>
                        </div>
                      </div>
                      <div id="w-node-c3e3fe22-3d73-2991-fac8-1e4a398b1b49-398b1b13" class="layout367_card-large-footer desktop-large-footer">
                        <div class="wolf-column-mobile">
                          <a href="index.html" aria-current="page" class="footer-logo-link w-inline-block w--current">
                            <div class="logo-wolf-column"><img src="images/woelfli-logo-sw.svg" loading="lazy" alt="Wöfli Logo "></div>
                          </a>
                          <div class="wolf-symbol-footer wolf-mobile-2">
                            <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                          </div>
                        </div>
                        <div class="footer-column">
                          <div class="policy-column policy-mobile">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                          </div>
                          <div class="policy-column policy-desktop">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                          </div>
                          <div class="copyright-column copyright-desktop">
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                        </div>
                        <div class="wolf-symbol-footer wolf-desktop">
                          <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                        </div>
                        <div class="copyright-column copyright-mobile">
                          <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                        </div>
                      </div>
                      <div class="layout367_card-large-footer mobile-large-footer">
                        <div class="woelfli-logo-column">
                          <div class="wolf-column-mobile">
                            <a href="index.html" aria-current="page" class="footer-logo-link w-inline-block w--current">
                              <div class="logo-wolf-column"><img src="images/woelfli-logo-sw.svg" loading="lazy" alt="Wöfli Logo "></div>
                            </a>
                            <div class="wolf-symbol-footer wolf-mobile-2">
                              <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                            </div>
                          </div>
                          <div class="wolf-symbol-footer wolf-desktop">
                            <a href="identitaet.html" class="footer-icon-link w-inline-block"><img src="images/wolf-symbol.svg" loading="lazy" alt="Das Wölfli Logo icon als Link das zur relevanten Unterseite führt"></a>
                          </div>
                        </div>
                        <div class="footer-column footer-column-mobile">
                          <div class="policy-column policy-mobile">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                          <div class="copyright-column copyright-mobile"></div>
                          <div class="policy-column policy-desktop">
                            <a href="impressum.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Impressum</div>
                            </a>
                            <a href="datenschutzerklaerung.html" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Datenschutz</div>
                            </a>
                          </div>
                          <div class="copyright-column copyright-desktop">
                            <a id="cw-cookie-icon" href="#" class="policy-link w-inline-block">
                              <div class="text-size-small text-color-primary">Cookies</div>
                            </a>
                            <div class="text-size-small text-color-primary">© 2025 Wölfli</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="layout367_card-large card-mobile">
                      <div class="layout367_card-small-content footer-top-margin">
                        <div class="footer-logos-mobile">
                          <p class="footer-contact-text">Mitgliedschaften</p>
                          <div class="layout367_card-small-content-top footer-logo-grid">
                            <a href="https://maneco.pro/" target="_blank" class="footer-logo w-inline-block"><img src="images/maneco-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="https://www.future-areas.swiss/" target="_blank" class="footer-logo w-inline-block"><img src="images/future-areas-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="#" target="_blank" class="footer-logo w-inline-block"><img src="images/logo_obs.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                            <a href="https://swissleaders.ch/" target="_blank" class="footer-logo w-inline-block"><img src="images/swiss-leaders-logo.svg" loading="lazy" alt="" class="footer-logo-img"></a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
    <div class="cw-cookies">
      <div id="cw-cookie-banner" class="cw-cookie_banner">
        <div class="cw-cookie_content">
          <h3 class="heading-style-h3">Cookie Einstellungen</h3>
          <p class="text-size-regular">Wir verwenden Cookies, um Ihnen die bestmögliche Nutzung unserer Website zu ermöglichen. Diese helfen uns dabei, die Website zu analysieren und zu verbessern sowie Ihnen ein optimales Nutzungserlebnis zu bieten.</p>
          <a href="datenschutzerklaerung.html" class="text-size-medium text-color-black">Datenschutzerklärung ansehen</a>
          <div class="cw-cookie_buttons"><button id="cw-btn-reject-all" class="cw-button_secondary">
              <div class="text-size-regular">Alle ablehnen</div>
            </button><button data-w-id="b7421a8e-479b-8458-8a2b-6c4cb088a137" id="cw-btn-options" class="cw-button_secondary">
              <div class="text-size-regular">Auswählen</div>
            </button><button id="cw-btn-accept-all" class="cw-button_primary">
              <div class="text-size-regular">Alle akzeptieren</div>
            </button></div>
          <div id="cw-cookie-options" class="cw-cookie_selection">
            <div class="w-form">
              <form id="email-form" name="email-form" data-name="Email Form" method="get" class="cw-cookie_options" data-wf-page-id="68257cde3c60ae59e717c714" data-wf-element-id="b7421a8e-479b-8458-8a2b-6c4cb088a13f"><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Notwendige-Cookies-2" id="consent-necessary" data-name="Notwendige Cookies 2" style="opacity:0;position:absolute;z-index:-1" checked=""><span id="consent-necessary" class="form_checkbox-label text-size-regular w-form-label" for="Notwendige-Cookies-2">Notwendige Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Analyse-Cookies" id="consent-analytics" data-name="Analyse Cookies" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Analyse-Cookies">Analyse Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Marketing" id="consent-ad-marketing" data-name="Marketing" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Marketing">Marketing-Cookies</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Nutzerdaten" id="consent-ad-user" data-name="Nutzerdaten" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Nutzerdaten">Nutzerdaten für Werbung</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Personalisierte-Werbung" id="consent-ad-personalization" data-name="Personalisierte Werbung" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Personalisierte-Werbung">Personalisierte Werbung</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Speichern-von-Informationen" id="consent-personalization" data-name="Speichern von Informationen" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="text-size-regular w-form-label" for="Speichern-von-Informationen">Speichern der Informationen für Zugriff oder auf Endgeräten</span>
                </label><label class="w-checkbox cw-cookie_checkbox">
                  <div class="w-checkbox-input w-checkbox-input--inputType-custom cw-cookie_checkbox-check w--redirected-checked"></div><input type="checkbox" name="Sicherheits-Cookies" id="consent-security" data-name="Sicherheits-Cookies" style="opacity:0;position:absolute;z-index:-1" checked=""><span class="form_checkbox-label text-size-regular w-form-label" for="Sicherheits-Cookies">Sicherheits-Cookies</span>
                </label></form>
              <div class="w-form-done">
                <div>Thank you! Your submission has been received!</div>
              </div>
              <div class="w-form-fail">
                <div>Oops! Something went wrong while submitting the form.</div>
              </div>
            </div><button id="cw-btn-accept-some" class="cw-button_secondary">
              <div class="text-size-regular">Auswahl übernehmen</div>
            </button>
          </div>
        </div>
      </div>
      <div class="cw-cookie_script w-embed w-script">
        <script>
// Immediate execution - prevent layout shift
(function() {
    const consentModeString = localStorage.getItem('consentMode');
    if (consentModeString) {
        document.write('<style>#cw-cookie-banner { display: none; } #cw-cookie-icon { display: flex; }</style>');
        // Apply consent to scripts immediately
        try {
            const consentMode = JSON.parse(consentModeString);
            setTimeout(function() {
                document.querySelectorAll('script[type="text/plain"][data-consent]').forEach(script => {
                    const consentCategory = script.getAttribute('data-consent');
                    const consentStatus = consentMode[consentCategory] || 'denied';
                    if (consentStatus === 'granted') {
                        const newScript = document.createElement('script');
                        Array.from(script.attributes).forEach(attr => {
                            if (attr.name !== 'data-consent' && attr.name !== 'type') {
                                newScript.setAttribute(attr.name, attr.value);
                            }
                        });
                        newScript.type = 'text/javascript';
                        if (script.innerHTML) newScript.innerHTML = script.innerHTML;
                        script.parentNode.insertBefore(newScript, script.nextSibling);
                    }
                });
            }, 100);
        } catch (e) {
            console.error("Error applying consent:", e);
        }
    }
})();
// FIXED: Find ALL cookie links, not just the first one with the ID
function setupAllCookieLinks() {
    // Find all links that contain "Cookies" text (case insensitive)
    const allCookieLinks = Array.from(document.querySelectorAll('a')).filter(link => 
        link.textContent.toLowerCase().includes('cookie') && 
        link.href && 
        link.href.includes('#')
    );
    console.log(`Found ${allCookieLinks.length} cookie links`);
    allCookieLinks.forEach((link, index) => {
        if (!link.hasAttribute('data-cookie-listener-added')) {
            link.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();
                console.log(`Cookie link ${index + 1} clicked`);
                // Force show banner
                const banner = document.getElementById('cw-cookie-banner');
                if (banner) {
                    banner.style.display = 'block';
                    console.log('Banner shown');
                }
                // Force hide all cookie icons
                document.querySelectorAll('#cw-cookie-icon').forEach(icon => {
                    icon.style.display = 'none';
                });
                // Reset checkboxes and options
                setTimeout(function() {
                    if (window.setConsentCheckboxes) {
                        window.setConsentCheckboxes();
                    }
                    if (window.hideOptions) {
                        window.hideOptions();
                    }
                }, 10);
                return false;
            });
            link.setAttribute('data-cookie-listener-added', 'true');
            console.log(`Attached listener to cookie link ${index + 1}: "${link.textContent.trim()}"`);
        }
    });
}
// Try multiple times to catch all cookie links
setupAllCookieLinks();
setTimeout(setupAllCookieLinks, 100);
setTimeout(setupAllCookieLinks, 500);
setTimeout(setupAllCookieLinks, 1000);
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupAllCookieLinks);
} else {
    setupAllCookieLinks();
}
// Show banner if no consent
if (localStorage.getItem('consentMode') === null) {
    setTimeout(function() {
        const banner = document.getElementById('cw-cookie-banner');
        if (banner) banner.style.display = 'block';
    }, 10);
}
// Main script logic
document.addEventListener('DOMContentLoaded', function() {
    const banner = document.getElementById('cw-cookie-banner');
    if (!banner) return;
    const consentMapping = {
        'functionality_storage': 'consent-necessary',
        'ad_storage': 'consent-ad-marketing', 
        'analytics_storage': 'consent-analytics',
        'ad_user_data': 'consent-ad-user',
        'ad_personalization': 'consent-ad-personalization',
        'personalization_storage': 'consent-personalization',
        'security_storage': 'consent-security',
    };
    function uncheckAllConsentCheckboxes() {
        ['consent-analytics', 'consent-ad-personalization', 'consent-ad-marketing', 'consent-ad-user', 'consent-personalization', 'consent-security'].forEach(checkboxId => {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = false;
                const checkboxDiv = checkbox.previousElementSibling;
                if (checkboxDiv && checkboxDiv.classList.contains('w--redirected-checked')) {
                    checkboxDiv.classList.remove('w--redirected-checked');
                }
            }
        });
    }
    function setConsentCheckboxes() {
        uncheckAllConsentCheckboxes();
        const consentModeString = localStorage.getItem('consentMode');
        if (consentModeString) {
            const consentMode = JSON.parse(consentModeString);
            Object.entries(consentMapping).forEach(([storageKey, checkboxId]) => {
                const checkbox = document.getElementById(checkboxId);
                if (checkbox) {
                    const isChecked = consentMode[storageKey] === 'granted';
                    checkbox.checked = isChecked;
                    const checkboxDiv = checkbox.previousElementSibling;
                    if (checkboxDiv) {
                        if (isChecked) {
                            checkboxDiv.classList.add('w--redirected-checked');
                        } else {
                            checkboxDiv.classList.remove('w--redirected-checked');
                        }
                    }
                }
            });
        }
    }
    function hideOptions() {
        const options = document.getElementById('cw-cookie-options');
        if (options) options.style.height = '0px';
    }
    function hideBanner() {
        banner.style.display = 'none';
        // Show all cookie icons
        document.querySelectorAll('#cw-cookie-icon').forEach(icon => {
            icon.style.display = 'flex';
        });
    }
    function setConsent(consent) {
        const consentMode = {
            'functionality_storage': consent.necessary ? 'granted' : 'denied',
            'ad_user_data': consent.aduser ? 'granted' : 'denied',
            'ad_storage': consent.admarketing ? 'granted' : 'denied',
            'analytics_storage': consent.analytics ? 'granted' : 'denied',
            'ad_personalization': consent.adpersonalized ? 'granted' : 'denied',
            'personalization_storage': consent.personalized ? 'granted' : 'denied',
            'security_storage': consent.security ? 'granted' : 'denied',
        };
        localStorage.setItem('consentMode', JSON.stringify(consentMode));
        hideBanner();
    }
    // Make functions global for the cookie link handlers
    window.setConsentCheckboxes = setConsentCheckboxes;
    window.hideOptions = hideOptions;
    // Button event listeners
    const acceptAll = document.getElementById('cw-btn-accept-all');
    if (acceptAll) {
        acceptAll.addEventListener('click', function() {
            setConsent({
                necessary: true, analytics: true, adpersonalized: true, admarketing: true,
                aduser: true, personalized: true, security: true,
            });
        });
    }
    const rejectAll = document.getElementById('cw-btn-reject-all');
    if (rejectAll) {
        rejectAll.addEventListener('click', function() {
            setConsent({
                necessary: true, analytics: false, adpersonalized: false, admarketing: false,
                aduser: false, personalized: false, security: false
            });
        });
    }
    const acceptSome = document.getElementById('cw-btn-accept-some');
    if (acceptSome) {
        acceptSome.addEventListener('click', function() {
            setConsent({
                necessary: true,
                analytics: document.getElementById('consent-analytics')?.checked || false,
                adpersonalized: document.getElementById('consent-ad-personalization')?.checked || false,
                admarketing: document.getElementById('consent-ad-marketing')?.checked || false,
                aduser: document.getElementById('consent-ad-user')?.checked || false,
                personalized: document.getElementById('consent-personalization')?.checked || false,
                security: document.getElementById('consent-security')?.checked || false,
            });
        });
    }
    const optionsBtn = document.getElementById('cw-btn-options');
    if (optionsBtn) {
        optionsBtn.addEventListener('click', function() {
            const options = document.getElementById('cw-cookie-options');
            if (options) {
                if (options.style.height === '0px' || options.style.height === '') {
                    options.style.height = options.scrollHeight + 'px';
                } else {
                    options.style.height = '0px';
                }
            }
        });
    }
    // Initialize
    setConsentCheckboxes();
    // Set up cookie links one more time
    setTimeout(setupAllCookieLinks, 100);
});
</script>
      </div>
    </div>
  </div>
  <script src="https://d3e54v103j8qbb.cloudfront.net/js/jquery-3.5.1.min.dc5e7f18c8.js?site=68257cde3c60ae59e717c715" type="text/javascript" integrity="sha256-9/aliU8dGd2tb6OSsuzixeV4y/faTqgFtohetphbbj0=" crossorigin="anonymous"></script>
  <script src="js/webflow.js" type="text/javascript"></script>
  <script src="https://instant.page/5.2.0" type="module" integrity="sha384-jnZyxPjiipYXnSU0ygqeac2q7CVYMbh84q0uHVRRxEtvFPiQYbXWUorga2aqZJ0z"></script>
  <script>
//Navbar Scroll Script
document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.querySelector('.navbar2_component');
    const navLinks = document.querySelectorAll('.navbar2_link');
    const navButton = document.querySelector('.button.is-navbar2-button');
    const logoSvgPaths = document.querySelectorAll('.navbar2_component .w-embed svg path');
    const menuLines = document.querySelectorAll('.button_dialog-menu-line');
    let scrollThreshold = 50; // Adjust this value to change when the animation triggers
    function updateNavbar() {
        if (window.scrollY > scrollThreshold) {
            // Scrolled state
            navbar.style.position = 'fixed';
            navbar.style.top = '0px';
            navbar.style.left = '0px';
            navbar.style.width = '100%';
            navbar.style.zIndex = '1000'; // Ensure it's on top
            navbar.style.backgroundColor = 'white';
            // Update nav links color
            navLinks.forEach(link => {
                link.style.color = '#000000'; // Dark grey color
            });
            // Update button background
            if (navButton) {
                navButton.style.backgroundColor = '#f5f5f5'; // Light grey background
            }
            // Update SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = '#000000';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = '#000000';
                }
            });
            // Update hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = '#000000';
            });
        } else {
            // Reset to original state
            navbar.style.position = ''; // Revert to original position
            navbar.style.top = '';
            navbar.style.left = '';
            navbar.style.width = '';
            navbar.style.zIndex = '';
            navbar.style.backgroundColor = 'transparent';
            // Reset nav links color
            navLinks.forEach(link => {
                link.style.color = ''; // Reset to original color
            });
            // Reset button background
            if (navButton) {
                navButton.style.backgroundColor = ''; // Reset to original background
            }
            // Reset SVG colors
            logoSvgPaths.forEach(path => {
                path.style.fill = 'white';
                if (path.hasAttribute('stroke') && path.getAttribute('stroke') === 'currentColor') {
                    path.style.stroke = 'white';
                }
            });
            // Reset hamburger menu line color
            menuLines.forEach(line => {
                line.style.color = 'white';
            });
        }
    }
    // Add smooth transition styles
    navbar.style.transition = 'all 0.3s ease';
    navLinks.forEach(link => {
        link.style.transition = 'color 0.3s ease';
    });
    if (navButton) {
        navButton.style.transition = 'background-color 0.3s ease';
    }
    logoSvgPaths.forEach(path => {
        path.style.transition = 'fill 0.3s ease, stroke 0.3s ease';
    });
    menuLines.forEach(line => {
        line.style.transition = 'color 0.3s ease';
    });
    // Initial check
    updateNavbar();
    // Add scroll event listener
    window.addEventListener('scroll', updateNavbar);
});
</script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.3/jquery.min.js" async=""></script>
  <!-- <script>
//SVG Icon Line Animation
  $(document).ready(function() {
    $('[svg="animated"]').css({
      opacity: 0,
      transition: "opacity 400ms ease"
    });
    $(window).on("load resize scroll", function() {
      $('[svg="animated"] path').each(function() {
        var pathLength = this.getTotalLength();
        $(this).attr({
          "stroke-dasharray": pathLength,
          "stroke-dashoffset": pathLength
        });
        var svgAnimated = $(this).closest('[svg="animated"]');
        var svgAnimatedTop = svgAnimated.offset().top;
        var svgAnimatedHeight = svgAnimated.outerHeight();
        var windowHeight = $(window).height();
        var windowScrollTop = $(window).scrollTop();
        var animationDuration = svgAnimated.attr('svg-animation-time') || 5000;
        if (windowScrollTop + windowHeight > svgAnimatedTop && windowScrollTop < svgAnimatedTop + svgAnimatedHeight) {
          $(svgAnimated).css("opacity", 1);
          $(this).css({
            transition: "stroke-dashoffset " + animationDuration + "ms ease-out",
            "stroke-dashoffset": 0
          });
        }
      });
    });
  });
</script>  -->
  <!-- <script>
document.addEventListener('DOMContentLoaded', function() {
  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const svg = entry.target;
        const paths = Array.from(svg.querySelectorAll('path'));
        // Sort paths by their x-position (left to right)
        paths.sort((a, b) => {
          const aBBox = a.getBBox();
          const bBBox = b.getBBox();
          return aBBox.x - bBBox.x;
        });
        let totalDelay = 0;
        // --- Speed Adjustment ---
        const speedMultiplier = 6;
        // -----------------------
        paths.forEach((path, index) => {
          // Calculate the path length
          const pathLength = path.getTotalLength();
          // Set initial styles
          path.style.strokeDasharray = pathLength;
          path.style.strokeDashoffset = pathLength;
          path.style.visibility = 'visible'; // Make visible right before animating
          // Calculate duration based on path length & speedMultiplier
          // (Keeping the minimum duration and divisor the same for now)
          const duration = Math.max(0.3, pathLength / 800 * (1/speedMultiplier)); 
          // Trigger animation with cascading delay
          setTimeout(() => {
            path.style.transition = `stroke-dashoffset ${duration}s ease-in-out`;
            path.style.strokeDashoffset = '0';
          }, totalDelay);
          // Update total delay (keeping the 30ms pause)
          totalDelay += duration * 800 + 30; 
        });
        // --- Keep this line to prevent replaying ---
        observer.unobserve(svg); 
        // ------------------------------------------
      }
    });
  }, {
    threshold: 0.2 // Trigger when 20% is visible
  });
  const svgs = document.querySelectorAll('.handwriting-svg');
  svgs.forEach(svg => {
    // Hide all paths initially
    svg.querySelectorAll('path').forEach(path => {
      path.style.visibility = 'hidden'; 
    });
    observer.observe(svg);
  });
});
</script>  -->
  <script>
(function(){
  document.addEventListener("DOMContentLoaded",function(){
    const resources={animeJs:false,scripts:false};
    const navLinks=document.querySelectorAll("a.nav-link,a.navbar2_link,nav a,.navigation a,.menu a");
    navLinks.forEach(link=>{
      link.addEventListener("mouseenter",preloadResources);
    });
    let scrollTriggered=false;
    window.addEventListener("scroll",function(){
      if(!scrollTriggered){
        scrollTriggered=true;
        preloadResources();
      }
    },{once:true});
    function preloadResources(){
      if(resources.animeJs&&resources.scripts){
        return;
      }
      if(!resources.animeJs&&typeof anime==="undefined"){
        resources.animeJs=true;
        const preloadLink=document.createElement("link");
        preloadLink.rel="preload";
        preloadLink.as="script";
        preloadLink.href="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js";
        document.head.appendChild(preloadLink);
        const script=document.createElement("script");
        script.src="https://cdnjs.cloudflare.com/ajax/libs/animejs/3.2.1/anime.min.js";
        document.head.appendChild(script);
      }
      if(!resources.scripts){
        resources.scripts=true;
        const scriptPreload=document.createElement("link");
        scriptPreload.rel="prefetch";
        scriptPreload.as="script";
        scriptPreload.href="https://cdn.jsdelivr.net/gh/woelfli/webflow-animations@main/woelfli-moving-image.min.js";
        document.head.appendChild(scriptPreload);
        if(typeof anime!=="undefined"){
          initializeAnimations();
        } else {
          setTimeout(checkAnimeAndInitialize,100);
        }
      }
    }
    function checkAnimeAndInitialize(){
      if(typeof anime!=="undefined"){
        initializeAnimations();
      } else {
        setTimeout(checkAnimeAndInitialize,100);
      }
    }
    function initializeAnimations(){
      const movingImage=document.querySelector(".moving-image");
      if(movingImage){
        movingImage.style.transition="none";
        movingImage.style.transform="scale(1.1) translateX(-5%)";
        const parent=movingImage.parentElement;
        if(parent&&!parent.style.overflow){
          parent.style.overflow="hidden";
        }
      }
      const sections=document.querySelectorAll("section,header:not(.navbar2_component)");
      sections.forEach(section=>{
        if(section.classList.contains("woelfli-animation-ready")||
           section.classList.contains("navbar2_component")||
           section.classList.contains("section-header-2")){
          return;
        }
        section.classList.add("woelfli-animation-ready");
      });
    }
  });
})(); 
</script>
</body>
</html>